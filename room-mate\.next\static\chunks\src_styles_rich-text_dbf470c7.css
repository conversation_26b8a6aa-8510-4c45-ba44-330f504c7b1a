/* [project]/src/styles/rich-text.css [app-client] (css) */
.rich-text-display {
  color: #374151;
  line-height: 1.7;
}

.rich-text-display * {
  max-width: 100%;
}

.rich-text-display h1, .rich-text-display h2, .rich-text-display h3, .rich-text-display h4, .rich-text-display h5, .rich-text-display h6 {
  color: inherit;
  margin-top: 1.5em;
  margin-bottom: .5em;
  font-weight: 600;
  line-height: 1.4;
}

.rich-text-display h1 {
  margin-top: 0;
  font-size: 2em;
}

.rich-text-display h2 {
  font-size: 1.5em;
}

.rich-text-display h3 {
  font-size: 1.25em;
}

.rich-text-display h4 {
  font-size: 1.125em;
}

.rich-text-display h5, .rich-text-display h6 {
  font-size: 1em;
}

.rich-text-display p {
  margin-top: 0;
  margin-bottom: 1em;
}

.rich-text-display p:last-child {
  margin-bottom: 0;
}

.rich-text-display a {
  color: #2563eb;
  font-weight: 500;
  text-decoration: none;
  transition: color .2s;
}

.rich-text-display a:hover {
  color: #1d4ed8;
  text-decoration: underline;
}

.rich-text-display ul, .rich-text-display ol {
  margin-top: 0;
  margin-bottom: 1em;
  padding-left: 1.5em;
}

.rich-text-display li {
  margin-bottom: .25em;
}

.rich-text-display li:last-child {
  margin-bottom: 0;
}

.rich-text-display ul li {
  list-style-type: disc;
}

.rich-text-display ol li {
  list-style-type: decimal;
}

.rich-text-display ul ul, .rich-text-display ol ol, .rich-text-display ul ol, .rich-text-display ol ul {
  margin-top: .25em;
  margin-bottom: .25em;
}

.rich-text-display strong, .rich-text-display b {
  color: inherit;
  font-weight: 600;
}

.rich-text-display em, .rich-text-display i {
  font-style: italic;
}

.rich-text-display u {
  text-decoration: underline;
}

.rich-text-display blockquote {
  background-color: #f9fafb;
  border-left: 4px solid #d1d5db;
  border-radius: .375rem;
  margin: 1.5em 0;
  padding: 1em;
  font-style: italic;
}

.rich-text-display blockquote p {
  margin: 0;
}

.rich-text-display code {
  background-color: #f3f4f6;
  border-radius: .25rem;
  padding: .125rem .25rem;
  font-family: ui-monospace, SFMono-Regular, SF Mono, Consolas, Liberation Mono, Menlo, monospace;
  font-size: .875em;
  font-weight: 400;
}

.rich-text-display pre {
  background-color: #f3f4f6;
  border-radius: .5rem;
  margin: 1em 0;
  padding: 1rem;
  overflow-x: auto;
}

.rich-text-display pre code {
  font-size: inherit;
  background-color: #0000;
  border-radius: 0;
  padding: 0;
}

.rich-text-display img {
  border-radius: .5rem;
  max-width: 100%;
  height: auto;
  margin: 1em 0;
  box-shadow: 0 4px 6px -1px #0000001a;
}

.rich-text-display table {
  border-collapse: collapse;
  border: 1px solid #d1d5db;
  border-radius: .375rem;
  width: 100%;
  margin: 1em 0;
  overflow: hidden;
}

.rich-text-display th, .rich-text-display td {
  text-align: left;
  border: 1px solid #d1d5db;
  padding: .75rem;
}

.rich-text-display th {
  background-color: #f9fafb;
  font-weight: 600;
}

.rich-text-display tbody tr:nth-child(2n) {
  background-color: #f9fafb;
}

.rich-text-display hr {
  border: 0;
  border-top: 1px solid #d1d5db;
  margin: 2em 0;
}

.rich-text-variant-card {
  background-color: #fff;
  border: 1px solid #e5e7eb;
  border-radius: .5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px #0000001a;
}

.rich-text-variant-quote {
  background-color: #f9fafb;
  border-left: 4px solid #3b82f6;
  border-radius: .5rem;
  padding: 1.5rem;
  font-style: italic;
  position: relative;
}

.rich-text-variant-quote:before {
  content: "\"";
  color: #3b82f6;
  opacity: .3;
  font-family: serif;
  font-size: 4rem;
  position: absolute;
  top: -.5rem;
  left: 1rem;
}

@media (prefers-color-scheme: dark) {
  .rich-text-display {
    color: #f3f4f6;
  }

  .rich-text-display h1, .rich-text-display h2, .rich-text-display h3, .rich-text-display h4, .rich-text-display h5, .rich-text-display h6 {
    color: #f9fafb;
  }

  .rich-text-display a {
    color: #60a5fa;
  }

  .rich-text-display a:hover {
    color: #93c5fd;
  }

  .rich-text-display blockquote {
    background-color: #374151;
    border-left-color: #6b7280;
  }

  .rich-text-display code {
    color: #f3f4f6;
    background-color: #374151;
  }

  .rich-text-display pre {
    background-color: #374151;
  }

  .rich-text-display table, .rich-text-display th, .rich-text-display td {
    border-color: #4b5563;
  }

  .rich-text-display th, .rich-text-display tbody tr:nth-child(2n) {
    background-color: #374151;
  }

  .rich-text-display hr {
    border-top-color: #4b5563;
  }

  .rich-text-variant-card {
    background-color: #1f2937;
    border-color: #374151;
  }

  .rich-text-variant-quote {
    background-color: #374151;
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: .5s ease-out fade-in;
}

@media (width <= 640px) {
  .rich-text-display h1 {
    font-size: 1.75em;
  }

  .rich-text-display h2 {
    font-size: 1.375em;
  }

  .rich-text-display h3 {
    font-size: 1.125em;
  }

  .rich-text-display blockquote {
    margin-left: 0;
    margin-right: 0;
    padding-left: .75rem;
    padding-right: .75rem;
  }

  .rich-text-display table {
    font-size: .875rem;
  }

  .rich-text-display th, .rich-text-display td {
    padding: .5rem;
  }
}

@media print {
  .rich-text-display {
    color: #000;
  }

  .rich-text-display a {
    color: #000;
    text-decoration: underline;
  }

  .rich-text-display blockquote {
    background-color: #0000;
    border-left: 2px solid #000;
  }

  .rich-text-display code, .rich-text-display pre {
    background-color: #f5f5f5;
    border: 1px solid #ccc;
  }
}


/*# sourceMappingURL=src_styles_rich-text_dbf470c7.css.map*/
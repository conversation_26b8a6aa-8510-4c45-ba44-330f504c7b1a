import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { QueryUsersDto } from './dto/query-users.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
export declare const createUserExample: CreateUserDto;
export declare const minimalUserExample: CreateUserDto;
export declare const updateUserExample: UpdateUserDto;
export declare const changePasswordExample: ChangePasswordDto;
export declare const queryExamples: {
    getAllUsers: QueryUsersDto;
    searchByName: QueryUsersDto;
    filterByGender: QueryUsersDto;
    filterSmokers: QueryUsersDto;
    filterByAge: QueryUsersDto;
    complexFilter: QueryUsersDto;
};
export declare const validationExamples: {
    validGenders: string[];
    validAges: string[];
    validPhones: string[];
    validPasswords: string[];
    invalidExamples: ({
        gender: string;
        phone?: undefined;
        password?: undefined;
        name?: undefined;
    } | {
        phone: string;
        gender?: undefined;
        password?: undefined;
        name?: undefined;
    } | {
        password: string;
        gender?: undefined;
        phone?: undefined;
        name?: undefined;
    } | {
        name: string;
        gender?: undefined;
        phone?: undefined;
        password?: undefined;
    })[];
};
export declare const apiEndpointExamples: {
    create: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
        body: CreateUserDto;
    };
    getAll: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    search: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    getStats: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    getProfile: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    getById: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    updateProfile: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
        body: UpdateUserDto;
    };
    updateById: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
        body: UpdateUserDto;
    };
    changePassword: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
        body: ChangePasswordDto;
    };
    delete: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
};
export declare const responseExamples: {
    userResponse: {
        id: string;
        name: string;
        email: string;
        phone: string;
        smoker: boolean;
        age: string;
        gender: string;
        nationality: string;
        occupation: string;
        avatar: string;
        emailVerified: boolean;
        role: string;
        isActive: boolean;
        createdAt: string;
        updatedAt: string;
        properties: {
            id: string;
            title: string;
            type: string;
            city: string;
            country: string;
        }[];
        offers: {
            id: string;
            message: string;
            price: string;
            status: string;
        }[];
    };
    paginatedResponse: {
        data: never[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };
    statsResponse: {
        totalUsers: number;
        activeUsers: number;
        verifiedUsers: number;
        usersByGender: {
            male: number;
            female: number;
            other: number;
        };
        usersByAge: {
            '18-25': number;
            '26-35': number;
            '36-45': number;
            '46+': number;
        };
        smokersCount: number;
        nonSmokersCount: number;
        usersWithProperties: number;
        usersWithOffers: number;
        newUsersThisMonth: number;
    };
    updateSuccessResponse: {
        success: boolean;
        message: string;
        data: {};
    };
    passwordChangeResponse: {
        success: boolean;
        message: string;
    };
};

// Frontend types for the room-mate application

export interface Offer {
  id: string
  message: string
  price: string
  phone: string
  duration?: string
  deposit: boolean
  status: 'pending' | 'accepted' | 'rejected' | 'cancelled'
  createdAt: string
  updatedAt: string
  propertyId?: string
  property?: {
    id: string
    title: string
    city: string
    country: string
    type: string
    price: string
    images: string[]
    user: {
      id: string
      name: string
      email: string
      phone: string
    }
  }
  personId?: string
  person?: {
    id: string
    name: string
    email: string
    phone: string
    age: string
    gender: string
    occupation: string
  }
  userId: string
  user: {
    id: string
    name: string
    email: string
    phone: string
    age?: string
    gender?: string
    occupation?: string
  }
  bookings?: {
    id: string
    startDate: string
    endDate: string
    status: string
  }[]
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

export interface ApiResponse<T> {
  data: T
  status: number
}

// Component prop types
export type ViewType = 'properties' | 'persons'
export type SortOption = 'newest' | 'oldest' | 'price-high' | 'price-low' | 'popular'
export type ViewMode = 'grid' | 'list'

export interface GetOffersParams {
  page?: number
  limit?: number
  categoryId?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

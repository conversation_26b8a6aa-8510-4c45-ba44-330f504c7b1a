export interface UserPayload {
    sub: string;
    email: string;
    isAdmin: boolean;
    iat?: number;
    exp?: number;
}
export interface UserResponse {
    id: string;
    name: string;
    email: string;
    phone: string | null;
    smoker: boolean;
    age: string | null;
    gender: string | null;
    nationality: string | null;
    country: string | null;
    occupation: string | null;
    isAdmin: boolean;
    isVerified: boolean;
    createdAt: Date;
    updatedAt?: Date;
}
export interface UsersQueryResult {
    users: UserResponse[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };
}

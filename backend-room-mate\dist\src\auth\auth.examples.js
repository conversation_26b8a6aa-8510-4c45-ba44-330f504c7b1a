"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.jwtExamples = exports.responseExamples = exports.apiEndpointExamples = exports.validationExamples = exports.googleRegisterExample = exports.minimalRegisterExample = exports.registerExample = exports.loginExample = void 0;
exports.loginExample = {
    email: '<EMAIL>',
    password: 'MySecureP@ss123',
};
exports.registerExample = {
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'MySecureP@ss123',
    phone: '+1234567890',
    smoker: false,
    age: '25',
    gender: 'male',
    nationality: 'American',
    occupation: 'Software Engineer',
};
exports.minimalRegisterExample = {
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'SecureP@ssword456',
};
exports.googleRegisterExample = {
    name: '<PERSON>',
    email: '<EMAIL>',
    googleId: 'google_oauth_id_12345',
    avatar: 'https://lh3.googleusercontent.com/a/avatar.jpg',
};
exports.validationExamples = {
    validEmails: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
    ],
    validPasswords: ['MySecureP@ss123', 'StrongP@ssw0rd!', 'Complex123@Password'],
    validNames: ['John Doe', 'Ahmed Al-Hassan', 'María García', '李小明'],
    validPhones: ['+1234567890', '+201234567890', '+44 20 7946 0958'],
    invalidExamples: [
        { email: 'invalid-email' },
        { email: 'user@' },
        { email: '@domain.com' },
        { password: '123456' },
        { password: 'password' },
        { password: 'PASSWORD123' },
        { name: 'A' },
        { name: '' },
        { phone: '123' },
        { phone: 'invalid-phone' },
    ],
};
exports.apiEndpointExamples = {
    register: {
        method: 'POST',
        url: '/api/auth/register',
        body: exports.registerExample,
    },
    login: {
        method: 'POST',
        url: '/api/auth/login',
        body: exports.loginExample,
    },
    googleAuth: {
        method: 'GET',
        url: '/api/auth/google',
    },
    googleCallback: {
        method: 'GET',
        url: '/api/auth/google/callback',
    },
    logout: {
        method: 'POST',
        url: '/api/auth/logout',
        headers: { Authorization: 'Bearer jwt-token-here' },
    },
    refresh: {
        method: 'POST',
        url: '/api/auth/refresh',
        headers: { Authorization: 'Bearer refresh-token-here' },
    },
    getProfile: {
        method: 'GET',
        url: '/api/auth/profile',
        headers: { Authorization: 'Bearer jwt-token-here' },
    },
};
exports.responseExamples = {
    loginResponse: {
        success: true,
        message: 'Login successful',
        data: {
            user: {
                id: 'user-uuid-123',
                name: 'John Doe',
                email: '<EMAIL>',
                phone: '+1234567890',
                smoker: false,
                age: '25',
                gender: 'male',
                nationality: 'American',
                occupation: 'Software Engineer',
                avatar: null,
                emailVerified: true,
                createdAt: '2024-01-01T00:00:00.000Z',
                updatedAt: '2024-01-01T00:00:00.000Z',
            },
            tokens: {
                accessToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
                refreshToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
                expiresIn: 3600,
            },
        },
    },
    registerResponse: {
        success: true,
        message: 'Registration successful',
        data: {
            user: {
                id: 'user-uuid-456',
                name: 'Jane Smith',
                email: '<EMAIL>',
                emailVerified: false,
                createdAt: '2024-01-01T00:00:00.000Z',
            },
            tokens: {
                accessToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
                refreshToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
                expiresIn: 3600,
            },
        },
    },
    errorResponses: {
        invalidCredentials: {
            success: false,
            message: 'Invalid email or password',
            statusCode: 401,
        },
        userAlreadyExists: {
            success: false,
            message: 'User with this email already exists',
            statusCode: 409,
        },
        validationError: {
            success: false,
            message: 'Validation failed',
            errors: [
                'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
            ],
            statusCode: 400,
        },
    },
};
exports.jwtExamples = {
    accessTokenPayload: {
        sub: 'user-uuid-123',
        email: '<EMAIL>',
        name: 'John Doe',
        role: 'user',
        iat: 1640995200,
        exp: 1640998800,
    },
    refreshTokenPayload: {
        sub: 'user-uuid-123',
        type: 'refresh',
        iat: 1640995200,
        exp: 1641081600,
    },
};
//# sourceMappingURL=auth.examples.js.map
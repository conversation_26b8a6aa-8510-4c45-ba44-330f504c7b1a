{"node": {"7f7ed2832f654977865887574304abff898a3508c9": {"workers": {"app/[locale]/(root)/favorites/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(root)/favorites/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/auth.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/actions/properties.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/persons.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(root)/favorites/page": "action-browser"}}, "7fc81d422d946583d67e965e01874ba827ddfb076a": {"workers": {"app/[locale]/(root)/favorites/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(root)/favorites/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/auth.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/actions/properties.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/persons.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(root)/favorites/page": "action-browser"}}, "7ff5228fd53ce23c0ab89ce73ea7504be3fdad58ed": {"workers": {"app/[locale]/(root)/favorites/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(root)/favorites/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/auth.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/actions/properties.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/persons.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(root)/favorites/page": "action-browser"}}, "7f66a103c6ddae774301f8b2622c6ae732936859ae": {"workers": {"app/[locale]/(root)/favorites/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(root)/favorites/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/auth.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/actions/properties.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/persons.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(root)/favorites/page": "action-browser"}}, "7fce98afd99c671ec24efe38503d6078d69fdd6123": {"workers": {"app/[locale]/(root)/favorites/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(root)/favorites/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/auth.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/actions/properties.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/persons.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(root)/favorites/page": "action-browser"}}, "7f8b0dd2de503cb91c0b806431ef15d9c76e3733d2": {"workers": {"app/[locale]/(root)/favorites/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(root)/favorites/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/auth.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/actions/properties.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/persons.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(root)/favorites/page": "action-browser"}}, "7f924a6698cf37dcbe72d45b49626a81e5aeb6b336": {"workers": {"app/[locale]/(root)/favorites/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(root)/favorites/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/auth.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/actions/properties.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/persons.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(root)/favorites/page": "action-browser"}}, "7f8da09ce3e39f4be65c901832c32498e0dbef03c7": {"workers": {"app/[locale]/(root)/favorites/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(root)/favorites/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/auth.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/actions/properties.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/persons.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(root)/favorites/page": "action-browser"}}, "7fb1aa08bc05db07ee3ebabadcf8c4595aaa265265": {"workers": {"app/[locale]/(root)/favorites/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(root)/favorites/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/auth.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/actions/properties.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/persons.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(root)/favorites/page": "action-browser"}}}, "edge": {}}
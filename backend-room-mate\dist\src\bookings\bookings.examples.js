"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.responseExamples = exports.apiEndpointExamples = exports.validationExamples = exports.queryExamples = exports.updateBookingExample = exports.shortTermBookingExample = exports.openEndedBookingExample = exports.createBookingExample = void 0;
exports.createBookingExample = {
    offerId: 'offer-uuid-123',
    startDate: '2024-03-01T00:00:00.000Z',
    endDate: '2024-03-31T23:59:59.999Z',
    totalAmount: '8000',
    depositPaid: true,
};
exports.openEndedBookingExample = {
    offerId: 'offer-uuid-456',
    startDate: '2024-02-15T00:00:00.000Z',
    totalAmount: '5000',
    depositPaid: false,
};
exports.shortTermBookingExample = {
    offerId: 'offer-uuid-789',
    startDate: '2024-01-20T00:00:00.000Z',
    endDate: '2024-01-27T23:59:59.999Z',
    totalAmount: '1400',
    depositPaid: true,
};
exports.updateBookingExample = {
    endDate: '2024-04-15T23:59:59.999Z',
    totalAmount: '10000',
    status: 'confirmed',
    depositPaid: true,
};
exports.queryExamples = {
    getAllBookings: {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc',
    },
    filterByStatus: {
        status: 'confirmed',
        page: 1,
        limit: 20,
    },
    filterByDateRange: {
        startDateFrom: '2024-01-01T00:00:00.000Z',
        startDateTo: '2024-12-31T23:59:59.999Z',
        page: 1,
        limit: 15,
        sortBy: 'startDate',
        sortOrder: 'asc',
    },
    filterByProperty: {
        propertyId: 'property-uuid-123',
        page: 1,
        limit: 10,
    },
    filterByUser: {
        userId: 'user-uuid-456',
        page: 1,
        limit: 10,
        sortBy: 'startDate',
        sortOrder: 'desc',
    },
    filterUpcoming: {
        depositPaid: true,
        page: 1,
        limit: 10,
        sortBy: 'startDate',
        sortOrder: 'asc',
    },
    filterCurrent: {
        depositPaid: false,
        page: 1,
        limit: 10,
    },
    complexFilter: {
        status: 'confirmed',
        propertyId: 'property-uuid-123',
        startDateFrom: '2024-01-01T00:00:00.000Z',
        startDateTo: '2024-06-30T23:59:59.999Z',
        depositPaid: true,
        page: 1,
        limit: 5,
        sortBy: 'startDate',
        sortOrder: 'asc',
    },
};
exports.validationExamples = {
    validStatuses: ['PENDING', 'CONFIRMED', 'CANCELLED', 'COMPLETED'],
    validDates: [
        '2024-01-01T00:00:00.000Z',
        '2024-12-31T23:59:59.999Z',
        '2024-06-15T12:30:00.000Z',
    ],
    validAmounts: ['1000', '5000.50', '12000', '999.99'],
    validUUIDs: ['offer-uuid-123', 'property-uuid-456', 'user-uuid-789'],
    invalidExamples: [
        { offerId: 'invalid-uuid' },
        { offerId: '' },
        { startDate: 'invalid-date' },
        { startDate: '2024-01-01' },
        { endDate: '2024-13-01T00:00:00.000Z' },
        { totalAmount: '' },
        { totalAmount: 'not-a-number' },
        { totalAmount: '-1000' },
        {
            startDate: '2024-02-01T00:00:00.000Z',
            endDate: '2024-01-01T00:00:00.000Z',
        },
    ],
};
exports.apiEndpointExamples = {
    create: {
        method: 'POST',
        url: '/api/bookings',
        headers: { Authorization: 'Bearer user-jwt-token' },
        body: exports.createBookingExample,
    },
    getAll: {
        method: 'GET',
        url: '/api/bookings?page=1&limit=10&sortBy=created_at&sortOrder=desc',
        headers: { Authorization: 'Bearer admin-jwt-token' },
    },
    getUserBookings: {
        method: 'GET',
        url: '/api/bookings/my-bookings',
        headers: { Authorization: 'Bearer user-jwt-token' },
    },
    getPropertyBookings: {
        method: 'GET',
        url: '/api/bookings?propertyId=property-uuid-123',
        headers: { Authorization: 'Bearer property-owner-jwt-token' },
    },
    filterByStatus: {
        method: 'GET',
        url: '/api/bookings?status=CONFIRMED&page=1&limit=20',
        headers: { Authorization: 'Bearer user-jwt-token' },
    },
    getUpcoming: {
        method: 'GET',
        url: '/api/bookings?upcoming=true&sortBy=start_date&sortOrder=asc',
        headers: { Authorization: 'Bearer user-jwt-token' },
    },
    getStats: {
        method: 'GET',
        url: '/api/bookings/stats',
        headers: { Authorization: 'Bearer admin-jwt-token' },
    },
    getById: {
        method: 'GET',
        url: '/api/bookings/booking-uuid-123',
        headers: { Authorization: 'Bearer user-jwt-token' },
    },
    update: {
        method: 'PATCH',
        url: '/api/bookings/booking-uuid-123',
        headers: { Authorization: 'Bearer user-jwt-token' },
        body: exports.updateBookingExample,
    },
    cancel: {
        method: 'PATCH',
        url: '/api/bookings/booking-uuid-123/cancel',
        headers: { Authorization: 'Bearer user-jwt-token' },
    },
    confirm: {
        method: 'PATCH',
        url: '/api/bookings/booking-uuid-123/confirm',
        headers: { Authorization: 'Bearer property-owner-jwt-token' },
    },
    complete: {
        method: 'PATCH',
        url: '/api/bookings/booking-uuid-123/complete',
        headers: { Authorization: 'Bearer property-owner-jwt-token' },
    },
    delete: {
        method: 'DELETE',
        url: '/api/bookings/booking-uuid-123',
        headers: { Authorization: 'Bearer admin-jwt-token' },
    },
};
exports.responseExamples = {
    bookingResponse: {
        id: 'booking-uuid-123',
        startDate: '2024-03-01T00:00:00.000Z',
        endDate: '2024-03-31T23:59:59.999Z',
        totalAmount: '8000',
        depositPaid: true,
        status: 'CONFIRMED',
        paymentStatus: 'PAID',
        checkInDate: '2024-03-01T14:00:00.000Z',
        checkOutDate: null,
        createdAt: '2024-02-15T10:30:00.000Z',
        updatedAt: '2024-02-20T16:45:00.000Z',
        offer: {
            id: 'offer-uuid-123',
            message: 'Interested in long-term rental',
            price: '8000',
            duration: '1 month',
            property: {
                id: 'property-uuid-456',
                title: 'Spacious 2-Bedroom Apartment in New Cairo',
                city: 'Cairo',
                country: 'Egypt',
                type: 'APARTMENT',
                images: ['https://example.com/images/property1.jpg'],
                user: {
                    id: 'property-owner-uuid',
                    name: 'Ahmed Hassan',
                    email: '<EMAIL>',
                    phone: '+************',
                },
            },
            user: {
                id: 'tenant-uuid',
                name: 'Sara Mohamed',
                email: '<EMAIL>',
                phone: '+************',
            },
        },
        notifications: [
            {
                id: 'notification-uuid-1',
                title: 'Booking Confirmed',
                message: 'Your booking has been confirmed by the property owner',
                createdAt: '2024-02-20T16:45:00.000Z',
            },
        ],
    },
    paginatedResponse: {
        data: [],
        total: 58,
        page: 1,
        limit: 10,
        totalPages: 6,
        hasNext: true,
        hasPrev: false,
    },
    statsResponse: {
        totalBookings: 234,
        confirmedBookings: 187,
        pendingBookings: 23,
        cancelledBookings: 18,
        completedBookings: 6,
        totalRevenue: 1250000,
        avgBookingDuration: 28,
        avgBookingAmount: 5340,
        bookingsByMonth: {
            '2024-01': 15,
            '2024-02': 28,
            '2024-03': 31,
        },
        bookingsByStatus: {
            PENDING: 23,
            CONFIRMED: 187,
            CANCELLED: 18,
            COMPLETED: 6,
        },
        topProperties: [
            {
                id: 'property-uuid-1',
                title: 'Luxury Apartment in Zamalek',
                bookingCount: 12,
                totalRevenue: 96000,
            },
            {
                id: 'property-uuid-2',
                title: 'Modern Studio in Maadi',
                bookingCount: 8,
                totalRevenue: 48000,
            },
        ],
    },
    createSuccessResponse: {
        success: true,
        message: 'Booking created successfully',
        data: {},
    },
    statusUpdateResponse: {
        success: true,
        message: 'Booking status updated successfully',
        data: {
            id: 'booking-uuid-123',
            status: 'CONFIRMED',
            updatedAt: '2024-02-20T16:45:00.000Z',
        },
    },
};
//# sourceMappingURL=bookings.examples.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.responseExamples = exports.apiEndpointExamples = exports.validationExamples = exports.queryExamples = exports.updateOfferExample = exports.shortTermOfferExample = exports.detailedOfferExample = exports.simpleOfferExample = exports.createOfferExample = void 0;
exports.createOfferExample = {
    message: 'I am interested in renting this beautiful apartment for 6 months. I am a non-smoker, working professional with excellent references. Available to move in by March 1st.',
    price: '7500',
    phone: '+************',
    duration: '6 months',
    deposit: true,
    propertyId: 'property-uuid-123',
};
exports.simpleOfferExample = {
    message: 'Interested in this property. Please contact me.',
    price: '5000',
    phone: '+************',
    propertyId: 'property-uuid-456',
};
exports.detailedOfferExample = {
    message: 'Hello, I am very interested in your property. I am a 28-year-old software engineer, non-smoker, with stable income. I can provide employment certificate and bank statements. Would you consider 6500 EGP per month for a 12-month lease?',
    price: '6500',
    phone: '+************',
    duration: '12 months',
    deposit: true,
    propertyId: 'property-uuid-789',
};
exports.shortTermOfferExample = {
    message: 'Looking for a 2-week accommodation while visiting Cairo for business. Clean, responsible tenant.',
    price: '3000',
    phone: '+************',
    duration: '2 weeks',
    deposit: false,
    propertyId: 'property-uuid-101',
};
exports.updateOfferExample = {
    message: 'Updated offer: I can increase my budget to 8000 EGP per month for the right property. Still interested in long-term rental.',
    price: '8000',
    duration: '12 months',
    status: 'pending',
};
exports.queryExamples = {
    getAllOffers: {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc',
    },
    filterByStatus: {
        status: 'pending',
        page: 1,
        limit: 20,
    },
    filterByProperty: {
        propertyId: 'property-uuid-123',
        page: 1,
        limit: 15,
        sortBy: 'price',
        sortOrder: 'desc',
    },
    filterByUser: {
        userId: 'user-uuid-456',
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc',
    },
    filterByPrice: {
        page: 1,
        limit: 10,
        sortBy: 'price',
        sortOrder: 'asc',
    },
    filterWithDeposit: {
        deposit: true,
        page: 1,
        limit: 10,
    },
    searchOffers: {
        search: 'software engineer professional',
        page: 1,
        limit: 5,
    },
    complexFilter: {
        status: 'pending',
        propertyId: 'property-uuid-123',
        deposit: true,
        search: 'long-term',
        page: 1,
        limit: 5,
        sortBy: 'price',
        sortOrder: 'desc',
    },
};
exports.validationExamples = {
    validStatuses: ['PENDING', 'ACCEPTED', 'REJECTED', 'CANCELLED'],
    validPrices: ['5000', '7500.50', '12000', '999.99', '10000.00'],
    validPhones: [
        '+************',
        '+20 12 3456 7890',
        '01234567890',
        '******-123-4567',
        '+44 20 7946 0958',
    ],
    validDurations: [
        '1 week',
        '2 weeks',
        '1 month',
        '3 months',
        '6 months',
        '12 months',
        'Long-term',
        'Short-term',
    ],
    validMessages: [
        'Short interest message.',
        'A detailed message explaining my interest in the property and providing background information about myself as a potential tenant.',
        'Very detailed message with specific requirements, background information, and negotiation points that demonstrates serious interest in the property.',
    ],
    invalidExamples: [
        { message: '' },
        { message: 'Hi' },
        { message: 'A'.repeat(1001) },
        { price: '' },
        { price: 'free' },
        { price: '-1000' },
        { price: 'abc' },
        { phone: '' },
        { phone: '123' },
        { phone: 'not-a-phone' },
        { phone: '+' },
        { propertyId: '' },
        { propertyId: 'invalid-uuid' },
        { propertyId: '123' },
        { duration: 'A'.repeat(101) },
    ],
};
exports.apiEndpointExamples = {
    create: {
        method: 'POST',
        url: '/api/offers',
        headers: { Authorization: 'Bearer user-jwt-token' },
        body: exports.createOfferExample,
    },
    getAll: {
        method: 'GET',
        url: '/api/offers?page=1&limit=10&sortBy=created_at&sortOrder=desc',
        headers: { Authorization: 'Bearer admin-jwt-token' },
    },
    getUserOffers: {
        method: 'GET',
        url: '/api/offers/my-offers',
        headers: { Authorization: 'Bearer user-jwt-token' },
    },
    getPropertyOffers: {
        method: 'GET',
        url: '/api/offers?propertyId=property-uuid-123',
        headers: { Authorization: 'Bearer property-owner-jwt-token' },
    },
    filterByStatus: {
        method: 'GET',
        url: '/api/offers?status=PENDING&page=1&limit=20',
        headers: { Authorization: 'Bearer user-jwt-token' },
    },
    search: {
        method: 'GET',
        url: '/api/offers?search=engineer&minPrice=5000&maxPrice=10000',
        headers: { Authorization: 'Bearer property-owner-jwt-token' },
    },
    getStats: {
        method: 'GET',
        url: '/api/offers/stats',
        headers: { Authorization: 'Bearer admin-jwt-token' },
    },
    getById: {
        method: 'GET',
        url: '/api/offers/offer-uuid-123',
        headers: { Authorization: 'Bearer user-jwt-token' },
    },
    update: {
        method: 'PATCH',
        url: '/api/offers/offer-uuid-123',
        headers: { Authorization: 'Bearer user-jwt-token' },
        body: exports.updateOfferExample,
    },
    accept: {
        method: 'PATCH',
        url: '/api/offers/offer-uuid-123/accept',
        headers: { Authorization: 'Bearer property-owner-jwt-token' },
    },
    reject: {
        method: 'PATCH',
        url: '/api/offers/offer-uuid-123/reject',
        headers: { Authorization: 'Bearer property-owner-jwt-token' },
        body: { reason: 'Price too low for the property value' },
    },
    cancel: {
        method: 'PATCH',
        url: '/api/offers/offer-uuid-123/cancel',
        headers: { Authorization: 'Bearer user-jwt-token' },
    },
    delete: {
        method: 'DELETE',
        url: '/api/offers/offer-uuid-123',
        headers: { Authorization: 'Bearer admin-jwt-token' },
    },
};
exports.responseExamples = {
    offerResponse: {
        id: 'offer-uuid-123',
        message: 'I am interested in renting this beautiful apartment for 6 months...',
        price: '7500',
        phone: '+************',
        duration: '6 months',
        deposit: true,
        status: 'PENDING',
        createdAt: '2024-02-15T10:30:00.000Z',
        updatedAt: '2024-02-15T10:30:00.000Z',
        property: {
            id: 'property-uuid-123',
            title: 'Spacious 2-Bedroom Apartment in New Cairo',
            city: 'Cairo',
            country: 'Egypt',
            type: 'APARTMENT',
            price: '8000',
            images: ['https://example.com/images/property1.jpg'],
            user: {
                id: 'property-owner-uuid',
                name: 'Ahmed Hassan',
                email: '<EMAIL>',
                phone: '+************',
            },
        },
        user: {
            id: 'offer-creator-uuid',
            name: 'Sara Mohamed',
            email: '<EMAIL>',
            phone: '+************',
            age: '28',
            gender: 'female',
            occupation: 'Software Engineer',
        },
        bookings: [
            {
                id: 'booking-uuid-456',
                startDate: '2024-03-01T00:00:00.000Z',
                endDate: '2024-08-31T23:59:59.999Z',
                status: 'CONFIRMED',
            },
        ],
    },
    paginatedResponse: {
        data: [],
        total: 89,
        page: 1,
        limit: 10,
        totalPages: 9,
        hasNext: true,
        hasPrev: false,
    },
    statsResponse: {
        totalOffers: 234,
        pendingOffers: 87,
        acceptedOffers: 95,
        rejectedOffers: 42,
        cancelledOffers: 10,
        avgOfferPrice: 6750,
        avgResponseTime: 2.5,
        offersByStatus: {
            PENDING: 87,
            ACCEPTED: 95,
            REJECTED: 42,
            CANCELLED: 10,
        },
        offersByPrice: {
            'Under 5000': 45,
            '5000-8000': 123,
            '8000-12000': 54,
            'Above 12000': 12,
        },
        topProperties: [
            {
                id: 'property-uuid-1',
                title: 'Luxury Apartment in Zamalek',
                offerCount: 23,
                avgOfferPrice: 9500,
            },
            {
                id: 'property-uuid-2',
                title: 'Modern Studio in Maadi',
                offerCount: 18,
                avgOfferPrice: 4750,
            },
        ],
        newOffersThisMonth: 34,
    },
    createSuccessResponse: {
        success: true,
        message: 'Offer created successfully',
        data: {},
    },
    statusUpdateResponse: {
        success: true,
        message: 'Offer status updated successfully',
        data: {
            id: 'offer-uuid-123',
            status: 'ACCEPTED',
            updatedAt: '2024-02-20T16:45:00.000Z',
        },
    },
};
//# sourceMappingURL=offers.examples.js.map
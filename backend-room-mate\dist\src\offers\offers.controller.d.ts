import { OffersService } from './offers.service';
import { CreateOfferDto } from './dto/create-offer.dto';
import { UpdateOfferDto } from './dto/update-offer.dto';
import { QueryOffersDto } from './dto/query-offers.dto';
import { UserPayload } from './interfaces/user.interface';
export declare class OffersController {
    private readonly offersService;
    constructor(offersService: OffersService);
    create(createOfferDto: CreateOfferDto, user: UserPayload): Promise<{
        success: boolean;
        data: {
            user: {
                id: string;
                name: string;
                email: string;
            };
            property: {
                id: string;
                country: string | null;
                isVerified: boolean;
                createdAt: Date;
                updatedAt: Date;
                title: string | null;
                userId: string | null;
                slug: string | null;
                images: string[];
                city: string | null;
                neighborhood: string | null;
                address: string | null;
                description: string | null;
                latitude: string | null;
                longitude: string | null;
                type: import(".prisma/client").$Enums.PropertyType;
                roomType: import(".prisma/client").$Enums.RoomType;
                genderRequired: string | null;
                totalRooms: string | null;
                availableRooms: string | null;
                roomsToComplete: string | null;
                price: string | null;
                size: string | null;
                floor: string | null;
                bathrooms: string | null;
                separatedBathroom: boolean;
                residentsCount: string | null;
                availablePersons: string | null;
                rentTime: import(".prisma/client").$Enums.RentTime;
                paymentTime: import(".prisma/client").$Enums.PaymentTime;
                priceIncludeWaterAndElectricity: boolean;
                allowSmoking: boolean;
                includeFurniture: boolean;
                airConditioning: boolean;
                includeWaterHeater: boolean;
                parking: boolean;
                internet: boolean;
                nearToMetro: boolean;
                nearToMarket: boolean;
                elevator: boolean;
                trialPeriod: boolean;
                goodForForeigners: boolean;
                rating: number;
                totalRatings: number;
                termsAndConditions: string | null;
                isAvailable: boolean;
                categoryId: string;
                ownerId: string;
            } | null;
            person: {
                id: string;
                country: string | null;
                isVerified: boolean;
                createdAt: Date;
                updatedAt: Date;
                title: string | null;
                slug: string | null;
                images: string[];
                city: string | null;
                neighborhood: string | null;
                address: string | null;
                description: string | null;
                latitude: string | null;
                longitude: string | null;
                type: import(".prisma/client").$Enums.PropertyType;
                roomType: import(".prisma/client").$Enums.RoomType;
                genderRequired: string | null;
                totalRooms: string | null;
                availableRooms: string | null;
                price: string | null;
                size: string | null;
                floor: string | null;
                bathrooms: string | null;
                separatedBathroom: boolean;
                residentsCount: string | null;
                availablePersons: string | null;
                rentTime: import(".prisma/client").$Enums.RentTime;
                paymentTime: import(".prisma/client").$Enums.PaymentTime;
                priceIncludeWaterAndElectricity: boolean;
                allowSmoking: boolean;
                includeFurniture: boolean;
                airConditioning: boolean;
                includeWaterHeater: boolean;
                parking: boolean;
                internet: boolean;
                nearToMetro: boolean;
                nearToMarket: boolean;
                elevator: boolean;
                trialPeriod: boolean;
                goodForForeigners: boolean;
                rating: number;
                totalRatings: number;
                termsAndConditions: string | null;
                isAvailable: boolean;
                categoryId: string;
                ownerId: string;
            } | null;
        } & {
            id: string;
            phone: string;
            createdAt: Date;
            updatedAt: Date;
            message: string;
            userId: string;
            price: string;
            duration: string | null;
            deposit: boolean;
            status: import(".prisma/client").$Enums.OfferStatus;
            propertyId: string | null;
            personId: string | null;
        };
        message: string;
    }>;
    findAll(query: QueryOffersDto, user: UserPayload): Promise<{
        success: boolean;
        data: ({
            user: {
                id: string;
                name: string;
                email: string;
            };
            property: {
                id: string;
                country: string | null;
                title: string | null;
                images: string[];
                city: string | null;
                price: string | null;
            } | null;
            person: {
                id: string;
                country: string | null;
                title: string | null;
                images: string[];
                city: string | null;
                price: string | null;
            } | null;
        } & {
            id: string;
            phone: string;
            createdAt: Date;
            updatedAt: Date;
            message: string;
            userId: string;
            price: string;
            duration: string | null;
            deposit: boolean;
            status: import(".prisma/client").$Enums.OfferStatus;
            propertyId: string | null;
            personId: string | null;
        })[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
    }>;
    getMyOffers(query: QueryOffersDto, user: UserPayload): Promise<{
        success: boolean;
        data: ({
            property: {
                id: string;
                country: string | null;
                title: string | null;
                images: string[];
                city: string | null;
                price: string | null;
            } | null;
            person: {
                id: string;
                country: string | null;
                title: string | null;
                images: string[];
                city: string | null;
                price: string | null;
            } | null;
        } & {
            id: string;
            phone: string;
            createdAt: Date;
            updatedAt: Date;
            message: string;
            userId: string;
            price: string;
            duration: string | null;
            deposit: boolean;
            status: import(".prisma/client").$Enums.OfferStatus;
            propertyId: string | null;
            personId: string | null;
        })[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
    }>;
    getPropertyOffers(propertyId: string, query: QueryOffersDto): Promise<{
        success: boolean;
        data: ({
            user: {
                id: string;
                name: string;
                email: string;
            };
        } & {
            id: string;
            phone: string;
            createdAt: Date;
            updatedAt: Date;
            message: string;
            userId: string;
            price: string;
            duration: string | null;
            deposit: boolean;
            status: import(".prisma/client").$Enums.OfferStatus;
            propertyId: string | null;
            personId: string | null;
        })[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
    }>;
    getPersonOffers(personId: string, query: QueryOffersDto): Promise<{
        success: boolean;
        data: ({
            user: {
                id: string;
                name: string;
                email: string;
            };
        } & {
            id: string;
            phone: string;
            createdAt: Date;
            updatedAt: Date;
            message: string;
            userId: string;
            price: string;
            duration: string | null;
            deposit: boolean;
            status: import(".prisma/client").$Enums.OfferStatus;
            propertyId: string | null;
            personId: string | null;
        })[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
    }>;
    findOne(id: string): Promise<{
        success: boolean;
        data: {
            user: {
                id: string;
                name: string;
                email: string;
                phone: string | null;
            };
            property: {
                id: string;
                country: string | null;
                title: string | null;
                images: string[];
                city: string | null;
                address: string | null;
                description: string | null;
                price: string | null;
                owner: {
                    id: string;
                    name: string;
                    email: string;
                    phone: string | null;
                };
            } | null;
            person: {
                id: string;
                country: string | null;
                title: string | null;
                images: string[];
                city: string | null;
                address: string | null;
                description: string | null;
                price: string | null;
                owner: {
                    id: string;
                    name: string;
                    email: string;
                    phone: string | null;
                };
            } | null;
            booking: {
                id: string;
                status: import(".prisma/client").$Enums.BookingStatus;
                startDate: Date;
                endDate: Date | null;
            } | null;
        } & {
            id: string;
            phone: string;
            createdAt: Date;
            updatedAt: Date;
            message: string;
            userId: string;
            price: string;
            duration: string | null;
            deposit: boolean;
            status: import(".prisma/client").$Enums.OfferStatus;
            propertyId: string | null;
            personId: string | null;
        };
    }>;
    update(id: string, updateOfferDto: UpdateOfferDto, user: UserPayload): Promise<{
        success: boolean;
        data: {
            user: {
                id: string;
                name: string;
                email: string;
            };
            property: {
                id: string;
                country: string | null;
                isVerified: boolean;
                createdAt: Date;
                updatedAt: Date;
                title: string | null;
                userId: string | null;
                slug: string | null;
                images: string[];
                city: string | null;
                neighborhood: string | null;
                address: string | null;
                description: string | null;
                latitude: string | null;
                longitude: string | null;
                type: import(".prisma/client").$Enums.PropertyType;
                roomType: import(".prisma/client").$Enums.RoomType;
                genderRequired: string | null;
                totalRooms: string | null;
                availableRooms: string | null;
                roomsToComplete: string | null;
                price: string | null;
                size: string | null;
                floor: string | null;
                bathrooms: string | null;
                separatedBathroom: boolean;
                residentsCount: string | null;
                availablePersons: string | null;
                rentTime: import(".prisma/client").$Enums.RentTime;
                paymentTime: import(".prisma/client").$Enums.PaymentTime;
                priceIncludeWaterAndElectricity: boolean;
                allowSmoking: boolean;
                includeFurniture: boolean;
                airConditioning: boolean;
                includeWaterHeater: boolean;
                parking: boolean;
                internet: boolean;
                nearToMetro: boolean;
                nearToMarket: boolean;
                elevator: boolean;
                trialPeriod: boolean;
                goodForForeigners: boolean;
                rating: number;
                totalRatings: number;
                termsAndConditions: string | null;
                isAvailable: boolean;
                categoryId: string;
                ownerId: string;
            } | null;
            person: {
                id: string;
                country: string | null;
                isVerified: boolean;
                createdAt: Date;
                updatedAt: Date;
                title: string | null;
                slug: string | null;
                images: string[];
                city: string | null;
                neighborhood: string | null;
                address: string | null;
                description: string | null;
                latitude: string | null;
                longitude: string | null;
                type: import(".prisma/client").$Enums.PropertyType;
                roomType: import(".prisma/client").$Enums.RoomType;
                genderRequired: string | null;
                totalRooms: string | null;
                availableRooms: string | null;
                price: string | null;
                size: string | null;
                floor: string | null;
                bathrooms: string | null;
                separatedBathroom: boolean;
                residentsCount: string | null;
                availablePersons: string | null;
                rentTime: import(".prisma/client").$Enums.RentTime;
                paymentTime: import(".prisma/client").$Enums.PaymentTime;
                priceIncludeWaterAndElectricity: boolean;
                allowSmoking: boolean;
                includeFurniture: boolean;
                airConditioning: boolean;
                includeWaterHeater: boolean;
                parking: boolean;
                internet: boolean;
                nearToMetro: boolean;
                nearToMarket: boolean;
                elevator: boolean;
                trialPeriod: boolean;
                goodForForeigners: boolean;
                rating: number;
                totalRatings: number;
                termsAndConditions: string | null;
                isAvailable: boolean;
                categoryId: string;
                ownerId: string;
            } | null;
        } & {
            id: string;
            phone: string;
            createdAt: Date;
            updatedAt: Date;
            message: string;
            userId: string;
            price: string;
            duration: string | null;
            deposit: boolean;
            status: import(".prisma/client").$Enums.OfferStatus;
            propertyId: string | null;
            personId: string | null;
        };
        message: string;
    }>;
    acceptOffer(id: string, user: UserPayload): Promise<{
        success: boolean;
        data: {
            user: {
                id: string;
                name: string;
                email: string;
            };
            property: {
                id: string;
                country: string | null;
                isVerified: boolean;
                createdAt: Date;
                updatedAt: Date;
                title: string | null;
                userId: string | null;
                slug: string | null;
                images: string[];
                city: string | null;
                neighborhood: string | null;
                address: string | null;
                description: string | null;
                latitude: string | null;
                longitude: string | null;
                type: import(".prisma/client").$Enums.PropertyType;
                roomType: import(".prisma/client").$Enums.RoomType;
                genderRequired: string | null;
                totalRooms: string | null;
                availableRooms: string | null;
                roomsToComplete: string | null;
                price: string | null;
                size: string | null;
                floor: string | null;
                bathrooms: string | null;
                separatedBathroom: boolean;
                residentsCount: string | null;
                availablePersons: string | null;
                rentTime: import(".prisma/client").$Enums.RentTime;
                paymentTime: import(".prisma/client").$Enums.PaymentTime;
                priceIncludeWaterAndElectricity: boolean;
                allowSmoking: boolean;
                includeFurniture: boolean;
                airConditioning: boolean;
                includeWaterHeater: boolean;
                parking: boolean;
                internet: boolean;
                nearToMetro: boolean;
                nearToMarket: boolean;
                elevator: boolean;
                trialPeriod: boolean;
                goodForForeigners: boolean;
                rating: number;
                totalRatings: number;
                termsAndConditions: string | null;
                isAvailable: boolean;
                categoryId: string;
                ownerId: string;
            } | null;
            person: {
                id: string;
                country: string | null;
                isVerified: boolean;
                createdAt: Date;
                updatedAt: Date;
                title: string | null;
                slug: string | null;
                images: string[];
                city: string | null;
                neighborhood: string | null;
                address: string | null;
                description: string | null;
                latitude: string | null;
                longitude: string | null;
                type: import(".prisma/client").$Enums.PropertyType;
                roomType: import(".prisma/client").$Enums.RoomType;
                genderRequired: string | null;
                totalRooms: string | null;
                availableRooms: string | null;
                price: string | null;
                size: string | null;
                floor: string | null;
                bathrooms: string | null;
                separatedBathroom: boolean;
                residentsCount: string | null;
                availablePersons: string | null;
                rentTime: import(".prisma/client").$Enums.RentTime;
                paymentTime: import(".prisma/client").$Enums.PaymentTime;
                priceIncludeWaterAndElectricity: boolean;
                allowSmoking: boolean;
                includeFurniture: boolean;
                airConditioning: boolean;
                includeWaterHeater: boolean;
                parking: boolean;
                internet: boolean;
                nearToMetro: boolean;
                nearToMarket: boolean;
                elevator: boolean;
                trialPeriod: boolean;
                goodForForeigners: boolean;
                rating: number;
                totalRatings: number;
                termsAndConditions: string | null;
                isAvailable: boolean;
                categoryId: string;
                ownerId: string;
            } | null;
        } & {
            id: string;
            phone: string;
            createdAt: Date;
            updatedAt: Date;
            message: string;
            userId: string;
            price: string;
            duration: string | null;
            deposit: boolean;
            status: import(".prisma/client").$Enums.OfferStatus;
            propertyId: string | null;
            personId: string | null;
        };
        message: string;
    }>;
    rejectOffer(id: string, user: UserPayload): Promise<{
        success: boolean;
        data: {
            user: {
                id: string;
                name: string;
                email: string;
            };
            property: {
                id: string;
                country: string | null;
                isVerified: boolean;
                createdAt: Date;
                updatedAt: Date;
                title: string | null;
                userId: string | null;
                slug: string | null;
                images: string[];
                city: string | null;
                neighborhood: string | null;
                address: string | null;
                description: string | null;
                latitude: string | null;
                longitude: string | null;
                type: import(".prisma/client").$Enums.PropertyType;
                roomType: import(".prisma/client").$Enums.RoomType;
                genderRequired: string | null;
                totalRooms: string | null;
                availableRooms: string | null;
                roomsToComplete: string | null;
                price: string | null;
                size: string | null;
                floor: string | null;
                bathrooms: string | null;
                separatedBathroom: boolean;
                residentsCount: string | null;
                availablePersons: string | null;
                rentTime: import(".prisma/client").$Enums.RentTime;
                paymentTime: import(".prisma/client").$Enums.PaymentTime;
                priceIncludeWaterAndElectricity: boolean;
                allowSmoking: boolean;
                includeFurniture: boolean;
                airConditioning: boolean;
                includeWaterHeater: boolean;
                parking: boolean;
                internet: boolean;
                nearToMetro: boolean;
                nearToMarket: boolean;
                elevator: boolean;
                trialPeriod: boolean;
                goodForForeigners: boolean;
                rating: number;
                totalRatings: number;
                termsAndConditions: string | null;
                isAvailable: boolean;
                categoryId: string;
                ownerId: string;
            } | null;
            person: {
                id: string;
                country: string | null;
                isVerified: boolean;
                createdAt: Date;
                updatedAt: Date;
                title: string | null;
                slug: string | null;
                images: string[];
                city: string | null;
                neighborhood: string | null;
                address: string | null;
                description: string | null;
                latitude: string | null;
                longitude: string | null;
                type: import(".prisma/client").$Enums.PropertyType;
                roomType: import(".prisma/client").$Enums.RoomType;
                genderRequired: string | null;
                totalRooms: string | null;
                availableRooms: string | null;
                price: string | null;
                size: string | null;
                floor: string | null;
                bathrooms: string | null;
                separatedBathroom: boolean;
                residentsCount: string | null;
                availablePersons: string | null;
                rentTime: import(".prisma/client").$Enums.RentTime;
                paymentTime: import(".prisma/client").$Enums.PaymentTime;
                priceIncludeWaterAndElectricity: boolean;
                allowSmoking: boolean;
                includeFurniture: boolean;
                airConditioning: boolean;
                includeWaterHeater: boolean;
                parking: boolean;
                internet: boolean;
                nearToMetro: boolean;
                nearToMarket: boolean;
                elevator: boolean;
                trialPeriod: boolean;
                goodForForeigners: boolean;
                rating: number;
                totalRatings: number;
                termsAndConditions: string | null;
                isAvailable: boolean;
                categoryId: string;
                ownerId: string;
            } | null;
        } & {
            id: string;
            phone: string;
            createdAt: Date;
            updatedAt: Date;
            message: string;
            userId: string;
            price: string;
            duration: string | null;
            deposit: boolean;
            status: import(".prisma/client").$Enums.OfferStatus;
            propertyId: string | null;
            personId: string | null;
        };
        message: string;
    }>;
    cancelOffer(id: string, user: UserPayload): Promise<{
        success: boolean;
        data: {
            user: {
                id: string;
                name: string;
                email: string;
            };
            property: {
                id: string;
                country: string | null;
                isVerified: boolean;
                createdAt: Date;
                updatedAt: Date;
                title: string | null;
                userId: string | null;
                slug: string | null;
                images: string[];
                city: string | null;
                neighborhood: string | null;
                address: string | null;
                description: string | null;
                latitude: string | null;
                longitude: string | null;
                type: import(".prisma/client").$Enums.PropertyType;
                roomType: import(".prisma/client").$Enums.RoomType;
                genderRequired: string | null;
                totalRooms: string | null;
                availableRooms: string | null;
                roomsToComplete: string | null;
                price: string | null;
                size: string | null;
                floor: string | null;
                bathrooms: string | null;
                separatedBathroom: boolean;
                residentsCount: string | null;
                availablePersons: string | null;
                rentTime: import(".prisma/client").$Enums.RentTime;
                paymentTime: import(".prisma/client").$Enums.PaymentTime;
                priceIncludeWaterAndElectricity: boolean;
                allowSmoking: boolean;
                includeFurniture: boolean;
                airConditioning: boolean;
                includeWaterHeater: boolean;
                parking: boolean;
                internet: boolean;
                nearToMetro: boolean;
                nearToMarket: boolean;
                elevator: boolean;
                trialPeriod: boolean;
                goodForForeigners: boolean;
                rating: number;
                totalRatings: number;
                termsAndConditions: string | null;
                isAvailable: boolean;
                categoryId: string;
                ownerId: string;
            } | null;
            person: {
                id: string;
                country: string | null;
                isVerified: boolean;
                createdAt: Date;
                updatedAt: Date;
                title: string | null;
                slug: string | null;
                images: string[];
                city: string | null;
                neighborhood: string | null;
                address: string | null;
                description: string | null;
                latitude: string | null;
                longitude: string | null;
                type: import(".prisma/client").$Enums.PropertyType;
                roomType: import(".prisma/client").$Enums.RoomType;
                genderRequired: string | null;
                totalRooms: string | null;
                availableRooms: string | null;
                price: string | null;
                size: string | null;
                floor: string | null;
                bathrooms: string | null;
                separatedBathroom: boolean;
                residentsCount: string | null;
                availablePersons: string | null;
                rentTime: import(".prisma/client").$Enums.RentTime;
                paymentTime: import(".prisma/client").$Enums.PaymentTime;
                priceIncludeWaterAndElectricity: boolean;
                allowSmoking: boolean;
                includeFurniture: boolean;
                airConditioning: boolean;
                includeWaterHeater: boolean;
                parking: boolean;
                internet: boolean;
                nearToMetro: boolean;
                nearToMarket: boolean;
                elevator: boolean;
                trialPeriod: boolean;
                goodForForeigners: boolean;
                rating: number;
                totalRatings: number;
                termsAndConditions: string | null;
                isAvailable: boolean;
                categoryId: string;
                ownerId: string;
            } | null;
        } & {
            id: string;
            phone: string;
            createdAt: Date;
            updatedAt: Date;
            message: string;
            userId: string;
            price: string;
            duration: string | null;
            deposit: boolean;
            status: import(".prisma/client").$Enums.OfferStatus;
            propertyId: string | null;
            personId: string | null;
        };
        message: string;
    }>;
    remove(id: string, user: UserPayload): Promise<{
        success: boolean;
        message: string;
    }>;
}

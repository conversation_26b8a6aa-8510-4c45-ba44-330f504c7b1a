"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.responseExamples = exports.apiEndpointExamples = exports.queryExamples = exports.updateCategoryExample = exports.simpleCategoryExample = exports.createCategoryExample = void 0;
exports.createCategoryExample = {
    name: 'Apartments',
    icon: 'apartment-icon.svg',
};
exports.simpleCategoryExample = {
    name: 'Student Housing',
};
exports.updateCategoryExample = {
    name: 'Modern Apartments',
    icon: 'modern-apartment-icon.svg',
};
exports.queryExamples = {
    getAllCategories: {
        page: 1,
        limit: 10,
        sortBy: 'name',
        sortOrder: 'asc',
    },
    searchCategories: {
        search: 'apartment',
        page: 1,
        limit: 5,
    },
};
exports.apiEndpointExamples = {
    create: {
        method: 'POST',
        url: '/api/categories',
        headers: { Authorization: 'Bearer admin-jwt-token' },
        body: exports.createCategoryExample,
    },
    getAll: {
        method: 'GET',
        url: '/api/categories',
    },
};
exports.responseExamples = {
    categoryResponse: {
        id: 'cat-uuid-123',
        name: 'Apartments',
        icon: 'apartment-icon.svg',
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
        properties: [
            {
                id: 'property-uuid-456',
                title: 'Beautiful Apartment in Cairo',
                price: '8000',
                city: 'Cairo',
            },
        ],
    },
};
//# sourceMappingURL=categories.examples.js.map
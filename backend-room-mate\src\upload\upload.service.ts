import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { UploadResponseDto } from './upload.dto';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

interface UploadedFile {
  filename: string;
  originalname: string;
  mimetype: string;
  size: number;
}

@Injectable()
export class UploadService {
  constructor(private configService: ConfigService) {}

  private getBaseUrl(): string {
    return (
      this.configService.get<string>('BASE_URL') || 'http://localhost:4000/nestjs'
    );
  }

  generateFilename(originalName: string): string {
    const fileExtension = path.extname(originalName);
    const baseName = path.basename(originalName, fileExtension);
    const timestamp = Date.now();
    const uuid = uuidv4().substring(0, 8);
    return `${baseName}-${timestamp}-${uuid}${fileExtension}`;
  }

  processUploadedFile(file: UploadedFile): UploadResponseDto {
    const baseUrl = this.getBaseUrl();

    return {
      filename: file.filename,
      originalName: file.originalname,
      url: `${baseUrl}/uploads/${file.filename}`,
      mimetype: file.mimetype,
      size: file.size,
    };
  }

  processMultipleUploadedFiles(files: UploadedFile[]): UploadResponseDto[] {
    return files.map((file) => this.processUploadedFile(file));
  }

  validateImageFile(file: UploadedFile): boolean {
    const allowedMimeTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml',
    ];

    return allowedMimeTypes.includes(file.mimetype);
  }
}

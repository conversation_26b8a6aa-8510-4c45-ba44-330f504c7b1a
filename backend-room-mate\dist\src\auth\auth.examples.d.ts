import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
export declare const loginExample: LoginDto;
export declare const registerExample: RegisterDto;
export declare const minimalRegisterExample: RegisterDto;
export declare const googleRegisterExample: {
    name: string;
    email: string;
    googleId: string;
    avatar: string;
};
export declare const validationExamples: {
    validEmails: string[];
    validPasswords: string[];
    validNames: string[];
    validPhones: string[];
    invalidExamples: ({
        email: string;
        password?: undefined;
        name?: undefined;
        phone?: undefined;
    } | {
        password: string;
        email?: undefined;
        name?: undefined;
        phone?: undefined;
    } | {
        name: string;
        email?: undefined;
        password?: undefined;
        phone?: undefined;
    } | {
        phone: string;
        email?: undefined;
        password?: undefined;
        name?: undefined;
    })[];
};
export declare const apiEndpointExamples: {
    register: {
        method: string;
        url: string;
        body: RegisterDto;
    };
    login: {
        method: string;
        url: string;
        body: LoginDto;
    };
    googleAuth: {
        method: string;
        url: string;
    };
    googleCallback: {
        method: string;
        url: string;
    };
    logout: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    refresh: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    getProfile: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
};
export declare const responseExamples: {
    loginResponse: {
        success: boolean;
        message: string;
        data: {
            user: {
                id: string;
                name: string;
                email: string;
                phone: string;
                smoker: boolean;
                age: string;
                gender: string;
                nationality: string;
                occupation: string;
                avatar: null;
                emailVerified: boolean;
                createdAt: string;
                updatedAt: string;
            };
            tokens: {
                accessToken: string;
                refreshToken: string;
                expiresIn: number;
            };
        };
    };
    registerResponse: {
        success: boolean;
        message: string;
        data: {
            user: {
                id: string;
                name: string;
                email: string;
                emailVerified: boolean;
                createdAt: string;
            };
            tokens: {
                accessToken: string;
                refreshToken: string;
                expiresIn: number;
            };
        };
    };
    errorResponses: {
        invalidCredentials: {
            success: boolean;
            message: string;
            statusCode: number;
        };
        userAlreadyExists: {
            success: boolean;
            message: string;
            statusCode: number;
        };
        validationError: {
            success: boolean;
            message: string;
            errors: string[];
            statusCode: number;
        };
    };
};
export declare const jwtExamples: {
    accessTokenPayload: {
        sub: string;
        email: string;
        name: string;
        role: string;
        iat: number;
        exp: number;
    };
    refreshTokenPayload: {
        sub: string;
        type: string;
        iat: number;
        exp: number;
    };
};

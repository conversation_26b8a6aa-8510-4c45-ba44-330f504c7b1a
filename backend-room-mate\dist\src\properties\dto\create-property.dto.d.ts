import { PropertyType, RoomType, RentTime, PaymentTime } from '@prisma/client';
export declare class CreatePropertyDto {
    title: string;
    images?: string[];
    city?: string;
    country?: string;
    neighborhood?: string;
    address?: string;
    description?: string;
    latitude?: string;
    longitude?: string;
    type?: PropertyType;
    roomType?: RoomType;
    genderRequired?: string;
    totalRooms?: string;
    availableRooms?: string;
    roomsToComplete?: string;
    price?: string;
    size?: string;
    floor?: string;
    bathrooms?: string;
    separatedBathroom?: boolean;
    residentsCount?: string;
    availablePersons?: string;
    rentTime?: RentTime;
    paymentTime?: PaymentTime;
    priceIncludeWaterAndElectricity?: boolean;
    allowSmoking?: boolean;
    includeFurniture?: boolean;
    airConditioning?: boolean;
    includeWaterHeater?: boolean;
    parking?: boolean;
    internet?: boolean;
    nearToMetro?: boolean;
    nearToMarket?: boolean;
    elevator?: boolean;
    trialPeriod?: boolean;
    goodForForeigners?: boolean;
    termsAndConditions?: string;
    categoryId: string;
}

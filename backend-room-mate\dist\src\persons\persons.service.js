"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PersonsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
let PersonsService = class PersonsService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createPersonDto, user) {
        const category = await this.prisma.category.findUnique({
            where: { id: createPersonDto.categoryId },
        });
        if (!category) {
            throw new common_1.NotFoundException('Category not found');
        }
        const slug = await this.generateUniqueSlug(createPersonDto.title);
        try {
            const person = await this.prisma.person.create({
                data: {
                    ...createPersonDto,
                    slug,
                    ownerId: user.sub,
                },
                include: {
                    category: true,
                    owner: {
                        select: {
                            id: true,
                            name: true,
                            email: true,
                            phone: true,
                            isVerified: true,
                        },
                    },
                },
            });
            return {
                success: true,
                data: person,
                message: 'Person listing created successfully',
            };
        }
        catch (error) {
            if (error.code === 'P2002') {
                throw new common_1.ConflictException('A person listing with this slug already exists');
            }
            throw error;
        }
    }
    async findAll(query) {
        const page = parseInt(query.page?.toString() || '1', 10);
        const limit = parseInt(query.limit?.toString() || '10', 10);
        const skip = (page - 1) * limit;
        const where = {};
        if (query.isAvailable !== undefined) {
            where.isAvailable = query.isAvailable;
        }
        else {
            where.isAvailable = true;
        }
        if (query.search) {
            where.OR = [
                { title: { contains: query.search, mode: 'insensitive' } },
                { description: { contains: query.search, mode: 'insensitive' } },
                { city: { contains: query.search, mode: 'insensitive' } },
                { country: { contains: query.search, mode: 'insensitive' } },
                { address: { contains: query.search, mode: 'insensitive' } },
                { neighborhood: { contains: query.search, mode: 'insensitive' } },
            ];
        }
        if (query.title) {
            where.title = { contains: query.title, mode: 'insensitive' };
        }
        if (query.slug) {
            where.slug = query.slug;
        }
        if (query.city) {
            where.city = { contains: query.city, mode: 'insensitive' };
        }
        if (query.country) {
            where.country = { contains: query.country, mode: 'insensitive' };
        }
        if (query.neighborhood) {
            where.neighborhood = {
                contains: query.neighborhood,
                mode: 'insensitive',
            };
        }
        if (query.address) {
            where.address = { contains: query.address, mode: 'insensitive' };
        }
        if (query.latitude) {
            where.latitude = query.latitude;
        }
        if (query.longitude) {
            where.longitude = query.longitude;
        }
        if (query.type) {
            where.type = query.type;
        }
        if (query.roomType) {
            where.roomType = query.roomType;
        }
        if (query.genderRequired) {
            where.genderRequired = query.genderRequired;
        }
        if (query.totalRooms) {
            where.totalRooms = query.totalRooms;
        }
        if (query.availableRooms) {
            where.availableRooms = query.availableRooms;
        }
        if (query.size) {
            where.size = query.size;
        }
        if (query.floor) {
            where.floor = query.floor;
        }
        if (query.bathrooms) {
            where.bathrooms = query.bathrooms;
        }
        if (query.residentsCount) {
            where.residentsCount = query.residentsCount;
        }
        if (query.availablePersons) {
            where.availablePersons = query.availablePersons;
        }
        if (query.rentTime) {
            where.rentTime = query.rentTime;
        }
        if (query.paymentTime) {
            where.paymentTime = query.paymentTime;
        }
        if (query.categoryId) {
            where.categoryId = query.categoryId;
        }
        if (query.ownerId) {
            where.ownerId = query.ownerId;
        }
        if (query.minPrice || query.maxPrice) {
            where.price = {};
            if (query.minPrice) {
                where.price.gte = query.minPrice;
            }
            if (query.maxPrice) {
                where.price.lte = query.maxPrice;
            }
        }
        if (query.minRating !== undefined) {
            where.rating = { gte: query.minRating };
        }
        if (query.minTotalRatings !== undefined) {
            where.totalRatings = { gte: query.minTotalRatings };
        }
        if (query.createdAfter || query.createdBefore) {
            where.createdAt = {};
            if (query.createdAfter) {
                where.createdAt.gte = new Date(query.createdAfter);
            }
            if (query.createdBefore) {
                where.createdAt.lte = new Date(query.createdBefore);
            }
        }
        if (query.updatedAfter || query.updatedBefore) {
            where.updatedAt = {};
            if (query.updatedAfter) {
                where.updatedAt.gte = new Date(query.updatedAfter);
            }
            if (query.updatedBefore) {
                where.updatedAt.lte = new Date(query.updatedBefore);
            }
        }
        const booleanFilters = [
            'separatedBathroom',
            'priceIncludeWaterAndElectricity',
            'allowSmoking',
            'includeFurniture',
            'airConditioning',
            'includeWaterHeater',
            'parking',
            'internet',
            'nearToMetro',
            'nearToMarket',
            'elevator',
            'trialPeriod',
            'goodForForeigners',
            'isVerified',
        ];
        booleanFilters.forEach((filter) => {
            if (query[filter] !== undefined) {
                where[filter] = query[filter];
            }
        });
        const orderBy = {};
        const sortBy = query.sortBy || 'createdAt';
        const sortOrder = query.sortOrder || 'desc';
        orderBy[sortBy] = sortOrder;
        try {
            const [persons, total] = await Promise.all([
                this.prisma.person.findMany({
                    where,
                    skip,
                    take: limit,
                    include: {
                        category: true,
                        owner: {
                            select: {
                                id: true,
                                name: true,
                                email: true,
                                phone: true,
                                isVerified: true,
                            },
                        },
                        _count: {
                            select: {
                                favorites: true,
                            },
                        },
                    },
                    orderBy,
                }),
                this.prisma.person.count({ where }),
            ]);
            const totalPages = Math.ceil(total / limit);
            return {
                success: true,
                data: {
                    persons,
                    pagination: {
                        page,
                        limit,
                        total,
                        totalPages,
                        hasNext: page < totalPages,
                        hasPrev: page > 1,
                    },
                },
            };
        }
        catch (error) {
            console.log(error);
            throw new common_1.BadRequestException('Failed to fetch person listings');
        }
    }
    async findOne(id) {
        const person = await this.prisma.person.findUnique({
            where: { id },
            include: {
                category: true,
                owner: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        phone: true,
                        isVerified: true,
                    },
                },
                offers: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                name: true,
                                email: true,
                                phone: true,
                                isVerified: true,
                            },
                        },
                    },
                    orderBy: { createdAt: 'desc' },
                },
                comments: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                name: true,
                                email: true,
                                isVerified: true,
                            },
                        },
                    },
                    orderBy: { createdAt: 'desc' },
                },
                _count: {
                    select: {
                        favorites: true,
                        offers: true,
                        comments: true,
                    },
                },
            },
        });
        if (!person) {
            throw new common_1.NotFoundException('Person listing not found');
        }
        return {
            success: true,
            data: person,
        };
    }
    async findBySlug(slug) {
        const person = await this.prisma.person.findUnique({
            where: { slug },
            include: {
                category: true,
                owner: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        phone: true,
                        isVerified: true,
                    },
                },
                offers: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                name: true,
                                email: true,
                                phone: true,
                                isVerified: true,
                            },
                        },
                    },
                    orderBy: { createdAt: 'desc' },
                },
                comments: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                name: true,
                                email: true,
                                isVerified: true,
                            },
                        },
                    },
                    orderBy: { createdAt: 'desc' },
                },
                _count: {
                    select: {
                        favorites: true,
                        offers: true,
                        comments: true,
                    },
                },
            },
        });
        if (!person) {
            throw new common_1.NotFoundException('Person listing not found');
        }
        return {
            success: true,
            data: person,
        };
    }
    async getMyPersons(userId, query) {
        const page = parseInt(query.page?.toString() || '1', 10);
        const limit = parseInt(query.limit?.toString() || '10', 10);
        const skip = (page - 1) * limit;
        const where = {
            ownerId: userId,
        };
        if (query.search) {
            where.OR = [
                { title: { contains: query.search, mode: 'insensitive' } },
                { description: { contains: query.search, mode: 'insensitive' } },
            ];
        }
        if (query.isAvailable !== undefined) {
            where.isAvailable = query.isAvailable;
        }
        try {
            const [persons, total] = await Promise.all([
                this.prisma.person.findMany({
                    where,
                    skip,
                    take: limit,
                    include: {
                        category: true,
                        _count: {
                            select: {
                                favorites: true,
                            },
                        },
                    },
                    orderBy: { createdAt: 'desc' },
                }),
                this.prisma.person.count({ where }),
            ]);
            const totalPages = Math.ceil(total / limit);
            return {
                success: true,
                data: {
                    persons,
                    pagination: {
                        page,
                        limit,
                        total,
                        totalPages,
                        hasNext: page < totalPages,
                        hasPrev: page > 1,
                    },
                },
            };
        }
        catch (error) {
            console.log(error);
            throw new common_1.BadRequestException('Failed to fetch your person listings');
        }
    }
    async getFavorites(userId, query) {
        const page = parseInt(query.page?.toString() || '1', 10);
        const limit = parseInt(query.limit?.toString() || '10', 10);
        const skip = (page - 1) * limit;
        try {
            const [favorites, total] = await Promise.all([
                this.prisma.person.findMany({
                    where: {
                        favorites: {
                            some: {
                                id: userId,
                            },
                        },
                        isAvailable: true,
                    },
                    skip,
                    take: limit,
                    include: {
                        category: true,
                        owner: {
                            select: {
                                id: true,
                                name: true,
                                email: true,
                                phone: true,
                                isVerified: true,
                            },
                        },
                        _count: {
                            select: {
                                favorites: true,
                            },
                        },
                    },
                    orderBy: { createdAt: 'desc' },
                }),
                this.prisma.person.count({
                    where: {
                        favorites: {
                            some: {
                                id: userId,
                            },
                        },
                        isAvailable: true,
                    },
                }),
            ]);
            const totalPages = Math.ceil(total / limit);
            return {
                success: true,
                data: {
                    persons: favorites,
                    pagination: {
                        page,
                        limit,
                        total,
                        totalPages,
                        hasNext: page < totalPages,
                        hasPrev: page > 1,
                    },
                },
            };
        }
        catch (error) {
            console.log(error);
            throw new common_1.BadRequestException('Failed to fetch favorite person listings');
        }
    }
    async update(id, updatePersonDto, user) {
        const existingPerson = await this.prisma.person.findUnique({
            where: { id },
            select: { ownerId: true, slug: true },
        });
        if (!existingPerson) {
            throw new common_1.NotFoundException('Person listing not found');
        }
        if (!user.isAdmin && existingPerson.ownerId !== user.sub) {
            throw new common_1.ForbiddenException('You can only update your own person listings');
        }
        let slug = existingPerson.slug;
        if (updatePersonDto.title) {
            slug = await this.generateUniqueSlug(updatePersonDto.title, id);
        }
        if (updatePersonDto.categoryId) {
            const category = await this.prisma.category.findUnique({
                where: { id: updatePersonDto.categoryId },
            });
            if (!category) {
                throw new common_1.NotFoundException('Category not found');
            }
        }
        try {
            const updatedPerson = await this.prisma.person.update({
                where: { id },
                data: {
                    ...updatePersonDto,
                    slug,
                    updatedAt: new Date(),
                },
                include: {
                    category: true,
                    owner: {
                        select: {
                            id: true,
                            name: true,
                            email: true,
                            phone: true,
                            isVerified: true,
                        },
                    },
                },
            });
            return {
                success: true,
                data: updatedPerson,
                message: 'Person listing updated successfully',
            };
        }
        catch (error) {
            if (error.code === 'P2002') {
                throw new common_1.ConflictException('A person listing with this slug already exists');
            }
            throw new common_1.BadRequestException('Failed to update person listing');
        }
    }
    async toggleFavorite(id, user) {
        const person = await this.prisma.person.findUnique({
            where: { id },
            include: {
                favorites: {
                    where: { id: user.sub },
                    select: { id: true },
                },
            },
        });
        if (!person) {
            throw new common_1.NotFoundException('Person listing not found');
        }
        const isFavorited = person.favorites.length > 0;
        try {
            if (isFavorited) {
                await this.prisma.person.update({
                    where: { id },
                    data: {
                        favorites: {
                            disconnect: { id: user.sub },
                        },
                    },
                });
                return {
                    success: true,
                    data: { isFavorited: false },
                    message: 'Removed from favorites',
                };
            }
            else {
                await this.prisma.person.update({
                    where: { id },
                    data: {
                        favorites: {
                            connect: { id: user.sub },
                        },
                    },
                });
                return {
                    success: true,
                    data: { isFavorited: true },
                    message: 'Added to favorites',
                };
            }
        }
        catch (error) {
            console.log(error);
            throw new common_1.BadRequestException('Failed to toggle favorite');
        }
    }
    async remove(id, user) {
        const person = await this.prisma.person.findUnique({
            where: { id },
            select: { ownerId: true, title: true },
        });
        if (!person) {
            throw new common_1.NotFoundException('Person listing not found');
        }
        if (!user.isAdmin && person.ownerId !== user.sub) {
            throw new common_1.ForbiddenException('You can only delete your own person listings');
        }
        try {
            await this.prisma.person.delete({
                where: { id },
            });
            return {
                success: true,
                message: 'Person listing deleted successfully',
            };
        }
        catch (error) {
            console.log(error);
            throw new common_1.BadRequestException('Failed to delete person listing');
        }
    }
    async generateUniqueSlug(title, excludeId) {
        const baseSlug = title
            .toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim();
        let slug = baseSlug;
        let counter = 1;
        while (true) {
            const where = { slug };
            if (excludeId) {
                where.id = { not: excludeId };
            }
            const existingPerson = await this.prisma.person.findUnique({
                where,
                select: { id: true },
            });
            if (!existingPerson) {
                break;
            }
            slug = `${baseSlug}-${counter}`;
            counter++;
        }
        return slug;
    }
};
exports.PersonsService = PersonsService;
exports.PersonsService = PersonsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], PersonsService);
//# sourceMappingURL=persons.service.js.map
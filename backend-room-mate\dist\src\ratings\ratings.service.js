"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RatingsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
let RatingsService = class RatingsService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(userId, createRatingDto) {
        const { propertyId, personId, score } = createRatingDto;
        if (!propertyId && !personId) {
            throw new common_1.BadRequestException('Either propertyId or personId must be provided');
        }
        if (propertyId && personId) {
            throw new common_1.BadRequestException('Cannot rate both property and person in the same request');
        }
        if (propertyId) {
            const property = await this.prisma.property.findUnique({
                where: { id: propertyId },
            });
            if (!property) {
                throw new common_1.NotFoundException('Property not found');
            }
        }
        if (personId) {
            const person = await this.prisma.person.findUnique({
                where: { id: personId },
            });
            if (!person) {
                throw new common_1.NotFoundException('Person not found');
            }
        }
        const existingRating = await this.prisma.rating.findFirst({
            where: {
                userId,
                ...(propertyId && { propertyId }),
                ...(personId && { personId }),
            },
        });
        if (existingRating) {
            const updatedRating = await this.prisma.rating.update({
                where: { id: existingRating.id },
                data: { score },
                include: {
                    user: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                    property: propertyId
                        ? {
                            select: {
                                id: true,
                                title: true,
                            },
                        }
                        : undefined,
                    person: personId
                        ? {
                            select: {
                                id: true,
                                title: true,
                            },
                        }
                        : undefined,
                },
            });
            if (propertyId) {
                await this.updatePropertyRatingStats(propertyId);
            }
            if (personId) {
                await this.updatePersonRatingStats(personId);
            }
            return updatedRating;
        }
        const data = {
            score,
            userId,
        };
        if (propertyId) {
            data.propertyId = propertyId;
        }
        if (personId) {
            data.personId = personId;
        }
        const rating = await this.prisma.rating.create({
            data,
            include: {
                user: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
                property: propertyId
                    ? {
                        select: {
                            id: true,
                            title: true,
                        },
                    }
                    : undefined,
                person: personId
                    ? {
                        select: {
                            id: true,
                            title: true,
                        },
                    }
                    : undefined,
            },
        });
        if (propertyId) {
            await this.updatePropertyRatingStats(propertyId);
        }
        if (personId) {
            await this.updatePersonRatingStats(personId);
        }
        return rating;
    }
    async createFromBooking(userId, createBookingRatingDto) {
        const { bookingId, score } = createBookingRatingDto;
        const booking = await this.prisma.booking.findUnique({
            where: { id: bookingId },
            include: { property: true },
        });
        if (!booking) {
            throw new common_1.NotFoundException('Booking not found');
        }
        if (booking.userId !== userId) {
            throw new common_1.ForbiddenException('You can only rate your own bookings');
        }
        if (booking.status !== 'completed') {
            throw new common_1.ConflictException('You can only rate completed bookings');
        }
        const existingRating = await this.prisma.rating.findUnique({
            where: { userId_propertyId: { userId, propertyId: booking.propertyId } },
        });
        if (existingRating) {
            throw new common_1.ConflictException('This booking has already been rated');
        }
        const rating = await this.prisma.rating.create({
            data: {
                score,
                userId,
                propertyId: booking.propertyId,
            },
            include: {
                user: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
                property: {
                    select: {
                        id: true,
                        title: true,
                    },
                },
            },
        });
        await this.updatePropertyRatingStats(booking.propertyId);
        return rating;
    }
    async findByProperty(propertyId) {
        return this.prisma.rating.findMany({
            where: { propertyId },
            include: {
                user: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
            },
            orderBy: {
                createdAt: 'desc',
            },
        });
    }
    async findByPerson(personId) {
        return this.prisma.rating.findMany({
            where: { personId },
            include: {
                user: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
            },
            orderBy: {
                createdAt: 'desc',
            },
        });
    }
    async findByUser(userId) {
        return this.prisma.rating.findMany({
            where: { userId },
            include: {
                property: {
                    select: {
                        id: true,
                        title: true,
                        images: true,
                    },
                },
                person: {
                    select: {
                        id: true,
                        title: true,
                        images: true,
                    },
                },
            },
            orderBy: {
                createdAt: 'desc',
            },
        });
    }
    async findUserRatingForProperty(userId, propertyId) {
        return this.prisma.rating.findFirst({
            where: {
                userId,
                propertyId,
            },
            include: {
                property: {
                    select: {
                        id: true,
                        title: true,
                    },
                },
            },
        });
    }
    async findUserRatingForPerson(userId, personId) {
        return this.prisma.rating.findFirst({
            where: {
                userId,
                personId,
            },
            include: {
                person: {
                    select: {
                        id: true,
                        title: true,
                    },
                },
            },
        });
    }
    async update(userId, ratingId, updateRatingDto) {
        const rating = await this.prisma.rating.findUnique({
            where: { id: ratingId },
        });
        if (!rating) {
            throw new common_1.NotFoundException('Rating not found');
        }
        if (rating.userId !== userId) {
            throw new common_1.ForbiddenException('You can only update your own ratings');
        }
        const updatedRating = await this.prisma.rating.update({
            where: { id: ratingId },
            data: updateRatingDto,
            include: {
                user: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
                property: {
                    select: {
                        id: true,
                        title: true,
                    },
                },
            },
        });
        if (rating.propertyId) {
            await this.updatePropertyRatingStats(rating.propertyId);
        }
        if (rating.personId) {
            await this.updatePersonRatingStats(rating.personId);
        }
        return updatedRating;
    }
    async remove(userId, ratingId) {
        const rating = await this.prisma.rating.findUnique({
            where: { id: ratingId },
        });
        if (!rating) {
            throw new common_1.NotFoundException('Rating not found');
        }
        if (rating.userId !== userId) {
            throw new common_1.ForbiddenException('You can only delete your own ratings');
        }
        await this.prisma.rating.delete({
            where: { id: ratingId },
        });
        if (rating.propertyId) {
            await this.updatePropertyRatingStats(rating.propertyId);
        }
        if (rating.personId) {
            await this.updatePersonRatingStats(rating.personId);
        }
        return { message: 'Rating deleted successfully' };
    }
    async getPropertyRatingStats(propertyId) {
        const ratings = await this.prisma.rating.findMany({
            where: { propertyId },
            select: { score: true },
        });
        const totalRatings = ratings.length;
        const averageRating = totalRatings > 0
            ? ratings.reduce((sum, rating) => sum + rating.score, 0) / totalRatings
            : 0;
        return {
            averageRating: Number(averageRating.toFixed(2)),
            totalRatings,
        };
    }
    async getPersonRatingStats(personId) {
        const ratings = await this.prisma.rating.findMany({
            where: { personId },
            select: { score: true },
        });
        const totalRatings = ratings.length;
        const averageRating = totalRatings > 0
            ? ratings.reduce((sum, rating) => sum + rating.score, 0) / totalRatings
            : 0;
        return {
            averageRating: Number(averageRating.toFixed(2)),
            totalRatings,
        };
    }
    async updatePropertyRatingStats(propertyId) {
        const ratings = await this.prisma.rating.findMany({
            where: { propertyId },
            select: { score: true },
        });
        const totalRatings = ratings.length;
        const averageRating = totalRatings > 0
            ? ratings.reduce((sum, rating) => sum + rating.score, 0) / totalRatings
            : 0;
        await this.prisma.property.update({
            where: { id: propertyId },
            data: {
                rating: Number(averageRating.toFixed(2)),
                totalRatings,
            },
        });
    }
    async updatePersonRatingStats(personId) {
        const ratings = await this.prisma.rating.findMany({
            where: { personId },
            select: { score: true },
        });
        const totalRatings = ratings.length;
        const averageRating = totalRatings > 0
            ? ratings.reduce((sum, rating) => sum + rating.score, 0) / totalRatings
            : 0;
        await this.prisma.person.update({
            where: { id: personId },
            data: {
                rating: Number(averageRating.toFixed(2)),
                totalRatings,
            },
        });
    }
};
exports.RatingsService = RatingsService;
exports.RatingsService = RatingsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], RatingsService);
//# sourceMappingURL=ratings.service.js.map
import { PrismaService } from '../prisma.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { QueryUsersDto } from './dto/query-users.dto';
import { UserResponse, UsersQueryResult, UserPayload } from './interfaces/user.interface';
export declare class UsersService {
    private prisma;
    constructor(prisma: PrismaService);
    private readonly userSelectFields;
    create(createUserDto: CreateUserDto): Promise<UserResponse>;
    findAll(query: QueryUsersDto, currentUser: UserPayload): Promise<UsersQueryResult>;
    findOne(id: string): Promise<UserResponse>;
    update(id: string, updateUserDto: UpdateUserDto, currentUser: UserPayload): Promise<UserResponse>;
    changePassword(id: string, changePasswordDto: ChangePasswordDto, currentUser: UserPayload): Promise<{
        message: string;
    }>;
    remove(id: string, currentUser: UserPayload): Promise<{
        message: string;
    }>;
    toggleVerification(id: string, currentUser: UserPayload): Promise<UserResponse>;
    toggleAdminStatus(id: string, currentUser: UserPayload): Promise<UserResponse>;
}

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.apiEndpointExamples = exports.updateVerificationExample = exports.createVerificationExample = void 0;
exports.createVerificationExample = {
    images: [
        'https://example.com/uploads/id-front.jpg',
        'https://example.com/uploads/id-back.jpg',
        'https://example.com/uploads/selfie.jpg',
    ],
    propertyId: 'property-uuid-123',
};
exports.updateVerificationExample = {
    status: 'approved',
};
exports.apiEndpointExamples = {
    create: {
        method: 'POST',
        url: '/api/verification/request',
        headers: { Authorization: 'Bearer user-jwt-token' },
        body: exports.createVerificationExample,
    },
    updateStatus: {
        method: 'PATCH',
        url: '/api/verification/verification-uuid-123',
        headers: { Authorization: 'Bearer admin-jwt-token' },
        body: exports.updateVerificationExample,
    },
};
//# sourceMappingURL=verification.examples.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.main = main;
const client_1 = require("@prisma/client");
const bcryptjs_1 = require("bcryptjs");
const prisma = new client_1.PrismaClient();
const userData = [
    {
        name: '<PERSON>',
        email: '<EMAIL>',
        password: (0, bcryptjs_1.hashSync)('SecurePass123!', 10),
        phone: '+1234567890',
        smoker: false,
        age: '25',
        gender: 'male',
        nationality: 'US',
        occupation: 'Developer',
        country: 'Egypt',
        isAdmin: true,
    },
];
async function main() {
    for (const u of userData) {
        await prisma.user.create({ data: u });
    }
}
main();
//# sourceMappingURL=seed.js.map
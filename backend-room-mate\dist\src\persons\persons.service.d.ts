import { PrismaService } from '../prisma.service';
import { CreatePersonDto } from './dto/create-person.dto';
import { UpdatePersonDto } from './dto/update-person.dto';
import { QueryPersonsDto } from './dto/query-persons.dto';
import { UserPayload } from '../users/interfaces/user.interface';
export declare class PersonsService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    create(createPersonDto: CreatePersonDto, user: UserPayload): Promise<{
        success: boolean;
        data: {
            category: {
                createdAt: Date;
                updatedAt: Date;
                id: string;
                name: string;
                icon: string | null;
            };
            owner: {
                id: string;
                isVerified: boolean;
                name: string;
                email: string;
                phone: string | null;
            };
        } & {
            createdAt: Date;
            price: string | null;
            rating: number;
            updatedAt: Date;
            id: string;
            title: string | null;
            slug: string | null;
            city: string | null;
            images: string[];
            country: string | null;
            neighborhood: string | null;
            address: string | null;
            description: string | null;
            latitude: string | null;
            longitude: string | null;
            type: import(".prisma/client").$Enums.PropertyType;
            roomType: import(".prisma/client").$Enums.RoomType;
            genderRequired: string | null;
            totalRooms: string | null;
            availableRooms: string | null;
            size: string | null;
            floor: string | null;
            bathrooms: string | null;
            separatedBathroom: boolean;
            residentsCount: string | null;
            availablePersons: string | null;
            rentTime: import(".prisma/client").$Enums.RentTime;
            paymentTime: import(".prisma/client").$Enums.PaymentTime;
            priceIncludeWaterAndElectricity: boolean;
            allowSmoking: boolean;
            includeFurniture: boolean;
            airConditioning: boolean;
            includeWaterHeater: boolean;
            parking: boolean;
            internet: boolean;
            nearToMetro: boolean;
            nearToMarket: boolean;
            elevator: boolean;
            trialPeriod: boolean;
            goodForForeigners: boolean;
            totalRatings: number;
            termsAndConditions: string | null;
            isVerified: boolean;
            isAvailable: boolean;
            categoryId: string;
            ownerId: string;
        };
        message: string;
    }>;
    findAll(query: QueryPersonsDto): Promise<{
        success: boolean;
        data: {
            persons: ({
                category: {
                    createdAt: Date;
                    updatedAt: Date;
                    id: string;
                    name: string;
                    icon: string | null;
                };
                owner: {
                    id: string;
                    isVerified: boolean;
                    name: string;
                    email: string;
                    phone: string | null;
                };
                _count: {
                    favorites: number;
                };
            } & {
                createdAt: Date;
                price: string | null;
                rating: number;
                updatedAt: Date;
                id: string;
                title: string | null;
                slug: string | null;
                city: string | null;
                images: string[];
                country: string | null;
                neighborhood: string | null;
                address: string | null;
                description: string | null;
                latitude: string | null;
                longitude: string | null;
                type: import(".prisma/client").$Enums.PropertyType;
                roomType: import(".prisma/client").$Enums.RoomType;
                genderRequired: string | null;
                totalRooms: string | null;
                availableRooms: string | null;
                size: string | null;
                floor: string | null;
                bathrooms: string | null;
                separatedBathroom: boolean;
                residentsCount: string | null;
                availablePersons: string | null;
                rentTime: import(".prisma/client").$Enums.RentTime;
                paymentTime: import(".prisma/client").$Enums.PaymentTime;
                priceIncludeWaterAndElectricity: boolean;
                allowSmoking: boolean;
                includeFurniture: boolean;
                airConditioning: boolean;
                includeWaterHeater: boolean;
                parking: boolean;
                internet: boolean;
                nearToMetro: boolean;
                nearToMarket: boolean;
                elevator: boolean;
                trialPeriod: boolean;
                goodForForeigners: boolean;
                totalRatings: number;
                termsAndConditions: string | null;
                isVerified: boolean;
                isAvailable: boolean;
                categoryId: string;
                ownerId: string;
            })[];
            pagination: {
                page: number;
                limit: number;
                total: number;
                totalPages: number;
                hasNext: boolean;
                hasPrev: boolean;
            };
        };
    }>;
    findOne(id: string): Promise<{
        success: boolean;
        data: {
            category: {
                createdAt: Date;
                updatedAt: Date;
                id: string;
                name: string;
                icon: string | null;
            };
            owner: {
                id: string;
                isVerified: boolean;
                name: string;
                email: string;
                phone: string | null;
            };
            offers: ({
                user: {
                    id: string;
                    isVerified: boolean;
                    name: string;
                    email: string;
                    phone: string | null;
                };
            } & {
                createdAt: Date;
                price: string;
                updatedAt: Date;
                id: string;
                phone: string;
                message: string;
                duration: string | null;
                deposit: boolean;
                status: import(".prisma/client").$Enums.OfferStatus;
                propertyId: string | null;
                personId: string | null;
                userId: string;
            })[];
            comments: ({
                user: {
                    id: string;
                    isVerified: boolean;
                    name: string;
                    email: string;
                };
            } & {
                createdAt: Date;
                updatedAt: Date;
                id: string;
                propertyId: string | null;
                personId: string | null;
                userId: string;
                content: string;
            })[];
            _count: {
                offers: number;
                favorites: number;
                comments: number;
            };
        } & {
            createdAt: Date;
            price: string | null;
            rating: number;
            updatedAt: Date;
            id: string;
            title: string | null;
            slug: string | null;
            city: string | null;
            images: string[];
            country: string | null;
            neighborhood: string | null;
            address: string | null;
            description: string | null;
            latitude: string | null;
            longitude: string | null;
            type: import(".prisma/client").$Enums.PropertyType;
            roomType: import(".prisma/client").$Enums.RoomType;
            genderRequired: string | null;
            totalRooms: string | null;
            availableRooms: string | null;
            size: string | null;
            floor: string | null;
            bathrooms: string | null;
            separatedBathroom: boolean;
            residentsCount: string | null;
            availablePersons: string | null;
            rentTime: import(".prisma/client").$Enums.RentTime;
            paymentTime: import(".prisma/client").$Enums.PaymentTime;
            priceIncludeWaterAndElectricity: boolean;
            allowSmoking: boolean;
            includeFurniture: boolean;
            airConditioning: boolean;
            includeWaterHeater: boolean;
            parking: boolean;
            internet: boolean;
            nearToMetro: boolean;
            nearToMarket: boolean;
            elevator: boolean;
            trialPeriod: boolean;
            goodForForeigners: boolean;
            totalRatings: number;
            termsAndConditions: string | null;
            isVerified: boolean;
            isAvailable: boolean;
            categoryId: string;
            ownerId: string;
        };
    }>;
    findBySlug(slug: string): Promise<{
        success: boolean;
        data: {
            category: {
                createdAt: Date;
                updatedAt: Date;
                id: string;
                name: string;
                icon: string | null;
            };
            owner: {
                id: string;
                isVerified: boolean;
                name: string;
                email: string;
                phone: string | null;
            };
            offers: ({
                user: {
                    id: string;
                    isVerified: boolean;
                    name: string;
                    email: string;
                    phone: string | null;
                };
            } & {
                createdAt: Date;
                price: string;
                updatedAt: Date;
                id: string;
                phone: string;
                message: string;
                duration: string | null;
                deposit: boolean;
                status: import(".prisma/client").$Enums.OfferStatus;
                propertyId: string | null;
                personId: string | null;
                userId: string;
            })[];
            comments: ({
                user: {
                    id: string;
                    isVerified: boolean;
                    name: string;
                    email: string;
                };
            } & {
                createdAt: Date;
                updatedAt: Date;
                id: string;
                propertyId: string | null;
                personId: string | null;
                userId: string;
                content: string;
            })[];
            _count: {
                offers: number;
                favorites: number;
                comments: number;
            };
        } & {
            createdAt: Date;
            price: string | null;
            rating: number;
            updatedAt: Date;
            id: string;
            title: string | null;
            slug: string | null;
            city: string | null;
            images: string[];
            country: string | null;
            neighborhood: string | null;
            address: string | null;
            description: string | null;
            latitude: string | null;
            longitude: string | null;
            type: import(".prisma/client").$Enums.PropertyType;
            roomType: import(".prisma/client").$Enums.RoomType;
            genderRequired: string | null;
            totalRooms: string | null;
            availableRooms: string | null;
            size: string | null;
            floor: string | null;
            bathrooms: string | null;
            separatedBathroom: boolean;
            residentsCount: string | null;
            availablePersons: string | null;
            rentTime: import(".prisma/client").$Enums.RentTime;
            paymentTime: import(".prisma/client").$Enums.PaymentTime;
            priceIncludeWaterAndElectricity: boolean;
            allowSmoking: boolean;
            includeFurniture: boolean;
            airConditioning: boolean;
            includeWaterHeater: boolean;
            parking: boolean;
            internet: boolean;
            nearToMetro: boolean;
            nearToMarket: boolean;
            elevator: boolean;
            trialPeriod: boolean;
            goodForForeigners: boolean;
            totalRatings: number;
            termsAndConditions: string | null;
            isVerified: boolean;
            isAvailable: boolean;
            categoryId: string;
            ownerId: string;
        };
    }>;
    getMyPersons(userId: string, query: QueryPersonsDto): Promise<{
        success: boolean;
        data: {
            persons: ({
                category: {
                    createdAt: Date;
                    updatedAt: Date;
                    id: string;
                    name: string;
                    icon: string | null;
                };
                _count: {
                    favorites: number;
                };
            } & {
                createdAt: Date;
                price: string | null;
                rating: number;
                updatedAt: Date;
                id: string;
                title: string | null;
                slug: string | null;
                city: string | null;
                images: string[];
                country: string | null;
                neighborhood: string | null;
                address: string | null;
                description: string | null;
                latitude: string | null;
                longitude: string | null;
                type: import(".prisma/client").$Enums.PropertyType;
                roomType: import(".prisma/client").$Enums.RoomType;
                genderRequired: string | null;
                totalRooms: string | null;
                availableRooms: string | null;
                size: string | null;
                floor: string | null;
                bathrooms: string | null;
                separatedBathroom: boolean;
                residentsCount: string | null;
                availablePersons: string | null;
                rentTime: import(".prisma/client").$Enums.RentTime;
                paymentTime: import(".prisma/client").$Enums.PaymentTime;
                priceIncludeWaterAndElectricity: boolean;
                allowSmoking: boolean;
                includeFurniture: boolean;
                airConditioning: boolean;
                includeWaterHeater: boolean;
                parking: boolean;
                internet: boolean;
                nearToMetro: boolean;
                nearToMarket: boolean;
                elevator: boolean;
                trialPeriod: boolean;
                goodForForeigners: boolean;
                totalRatings: number;
                termsAndConditions: string | null;
                isVerified: boolean;
                isAvailable: boolean;
                categoryId: string;
                ownerId: string;
            })[];
            pagination: {
                page: number;
                limit: number;
                total: number;
                totalPages: number;
                hasNext: boolean;
                hasPrev: boolean;
            };
        };
    }>;
    getFavorites(userId: string, query: QueryPersonsDto): Promise<{
        success: boolean;
        data: {
            persons: ({
                category: {
                    createdAt: Date;
                    updatedAt: Date;
                    id: string;
                    name: string;
                    icon: string | null;
                };
                owner: {
                    id: string;
                    isVerified: boolean;
                    name: string;
                    email: string;
                    phone: string | null;
                };
                _count: {
                    favorites: number;
                };
            } & {
                createdAt: Date;
                price: string | null;
                rating: number;
                updatedAt: Date;
                id: string;
                title: string | null;
                slug: string | null;
                city: string | null;
                images: string[];
                country: string | null;
                neighborhood: string | null;
                address: string | null;
                description: string | null;
                latitude: string | null;
                longitude: string | null;
                type: import(".prisma/client").$Enums.PropertyType;
                roomType: import(".prisma/client").$Enums.RoomType;
                genderRequired: string | null;
                totalRooms: string | null;
                availableRooms: string | null;
                size: string | null;
                floor: string | null;
                bathrooms: string | null;
                separatedBathroom: boolean;
                residentsCount: string | null;
                availablePersons: string | null;
                rentTime: import(".prisma/client").$Enums.RentTime;
                paymentTime: import(".prisma/client").$Enums.PaymentTime;
                priceIncludeWaterAndElectricity: boolean;
                allowSmoking: boolean;
                includeFurniture: boolean;
                airConditioning: boolean;
                includeWaterHeater: boolean;
                parking: boolean;
                internet: boolean;
                nearToMetro: boolean;
                nearToMarket: boolean;
                elevator: boolean;
                trialPeriod: boolean;
                goodForForeigners: boolean;
                totalRatings: number;
                termsAndConditions: string | null;
                isVerified: boolean;
                isAvailable: boolean;
                categoryId: string;
                ownerId: string;
            })[];
            pagination: {
                page: number;
                limit: number;
                total: number;
                totalPages: number;
                hasNext: boolean;
                hasPrev: boolean;
            };
        };
    }>;
    update(id: string, updatePersonDto: UpdatePersonDto, user: UserPayload): Promise<{
        success: boolean;
        data: {
            category: {
                createdAt: Date;
                updatedAt: Date;
                id: string;
                name: string;
                icon: string | null;
            };
            owner: {
                id: string;
                isVerified: boolean;
                name: string;
                email: string;
                phone: string | null;
            };
        } & {
            createdAt: Date;
            price: string | null;
            rating: number;
            updatedAt: Date;
            id: string;
            title: string | null;
            slug: string | null;
            city: string | null;
            images: string[];
            country: string | null;
            neighborhood: string | null;
            address: string | null;
            description: string | null;
            latitude: string | null;
            longitude: string | null;
            type: import(".prisma/client").$Enums.PropertyType;
            roomType: import(".prisma/client").$Enums.RoomType;
            genderRequired: string | null;
            totalRooms: string | null;
            availableRooms: string | null;
            size: string | null;
            floor: string | null;
            bathrooms: string | null;
            separatedBathroom: boolean;
            residentsCount: string | null;
            availablePersons: string | null;
            rentTime: import(".prisma/client").$Enums.RentTime;
            paymentTime: import(".prisma/client").$Enums.PaymentTime;
            priceIncludeWaterAndElectricity: boolean;
            allowSmoking: boolean;
            includeFurniture: boolean;
            airConditioning: boolean;
            includeWaterHeater: boolean;
            parking: boolean;
            internet: boolean;
            nearToMetro: boolean;
            nearToMarket: boolean;
            elevator: boolean;
            trialPeriod: boolean;
            goodForForeigners: boolean;
            totalRatings: number;
            termsAndConditions: string | null;
            isVerified: boolean;
            isAvailable: boolean;
            categoryId: string;
            ownerId: string;
        };
        message: string;
    }>;
    toggleFavorite(id: string, user: UserPayload): Promise<{
        success: boolean;
        data: {
            isFavorited: boolean;
        };
        message: string;
    }>;
    remove(id: string, user: UserPayload): Promise<{
        success: boolean;
        message: string;
    }>;
    private generateUniqueSlug;
}

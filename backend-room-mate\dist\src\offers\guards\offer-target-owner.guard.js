"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OfferTargetOwnerGuard = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma.service");
let OfferTargetOwnerGuard = class OfferTargetOwnerGuard {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        const offerId = request.params.id;
        if (!user) {
            throw new common_1.ForbiddenException('Authentication required');
        }
        if (!offerId) {
            throw new common_1.ForbiddenException('Offer ID is required');
        }
        if (user.isAdmin) {
            return true;
        }
        const offer = await this.prisma.offer.findUnique({
            where: { id: offerId },
            include: {
                property: {
                    select: { ownerId: true },
                },
                person: {
                    select: { ownerId: true },
                },
            },
        });
        if (!offer) {
            throw new common_1.NotFoundException('Offer not found');
        }
        const isPropertyOwner = offer.property?.ownerId === user.sub;
        const isPersonOwner = offer.person?.ownerId === user.sub;
        if (isPropertyOwner || isPersonOwner) {
            return true;
        }
        throw new common_1.ForbiddenException('Only the listing owner can perform this action');
    }
};
exports.OfferTargetOwnerGuard = OfferTargetOwnerGuard;
exports.OfferTargetOwnerGuard = OfferTargetOwnerGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], OfferTargetOwnerGuard);
//# sourceMappingURL=offer-target-owner.guard.js.map
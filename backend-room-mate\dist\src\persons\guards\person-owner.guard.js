"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PersonOwnerGuard = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma.service");
let PersonOwnerGuard = class PersonOwnerGuard {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        const personId = request.params.id;
        if (!user) {
            throw new common_1.ForbiddenException('User not authenticated');
        }
        if (!personId) {
            throw new common_1.ForbiddenException('Person ID is required');
        }
        if (user.isAdmin) {
            return true;
        }
        const person = await this.prisma.person.findUnique({
            where: { id: personId },
            select: { ownerId: true },
        });
        if (!person) {
            throw new common_1.NotFoundException('Person listing not found');
        }
        if (person.ownerId !== user.sub) {
            throw new common_1.ForbiddenException('You can only access your own person listings');
        }
        return true;
    }
};
exports.PersonOwnerGuard = PersonOwnerGuard;
exports.PersonOwnerGuard = PersonOwnerGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], PersonOwnerGuard);
//# sourceMappingURL=person-owner.guard.js.map
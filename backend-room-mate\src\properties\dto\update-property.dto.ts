import {
  IsOptional,
  IsString,
  <PERSON><PERSON><PERSON>y,
  IsBoolean,
  IsEnum,
  IsUUID,
  MinLength,
  MaxLength,
} from 'class-validator';
import { PropertyType, RoomType, RentTime, PaymentTime } from '@prisma/client';

export class UpdatePropertyDto {
  @IsOptional()
  @IsString()
  @MaxLength(200, { message: 'Title must not exceed 200 characters' })
  title?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  images?: string[];

  @IsOptional()
  @IsString()
  city?: string;

  @IsOptional()
  @IsString()
  country?: string;

  @IsOptional()
  @IsString()
  neighborhood?: string;

  @IsOptional()
  @IsString()
  address?: string;

  @IsOptional()
  @IsString()
  @MaxLength(2000, { message: 'Description must not exceed 2000 characters' })
  description?: string;

  @IsOptional()
  @IsString()
  latitude?: string;

  @IsOptional()
  @IsString()
  longitude?: string;

  @IsOptional()
  @IsEnum(PropertyType)
  type?: PropertyType;

  @IsOptional()
  @IsEnum(RoomType)
  roomType?: RoomType;

  @IsOptional()
  @IsString()
  genderRequired?: string;

  @IsOptional()
  @IsString()
  totalRooms?: string;

  @IsOptional()
  @IsString()
  availableRooms?: string;

  @IsOptional()
  @IsString()
  roomsToComplete?: string;

  @IsOptional()
  @IsString()
  price?: string;

  @IsOptional()
  @IsString()
  size?: string;

  @IsOptional()
  @IsString()
  floor?: string;

  @IsOptional()
  @IsString()
  bathrooms?: string;

  @IsOptional()
  @IsBoolean()
  separatedBathroom?: boolean;

  @IsOptional()
  @IsString()
  residentsCount?: string;

  @IsOptional()
  @IsString()
  availablePersons?: string;

  @IsOptional()
  @IsEnum(RentTime)
  rentTime?: RentTime;

  @IsOptional()
  @IsEnum(PaymentTime)
  paymentTime?: PaymentTime;

  @IsOptional()
  @IsBoolean()
  priceIncludeWaterAndElectricity?: boolean;

  @IsOptional()
  @IsBoolean()
  allowSmoking?: boolean;

  @IsOptional()
  @IsBoolean()
  includeFurniture?: boolean;

  @IsOptional()
  @IsBoolean()
  airConditioning?: boolean;

  @IsOptional()
  @IsBoolean()
  includeWaterHeater?: boolean;

  @IsOptional()
  @IsBoolean()
  parking?: boolean;

  @IsOptional()
  @IsBoolean()
  internet?: boolean;

  @IsOptional()
  @IsBoolean()
  nearToMetro?: boolean;

  @IsOptional()
  @IsBoolean()
  nearToMarket?: boolean;

  @IsOptional()
  @IsBoolean()
  elevator?: boolean;

  @IsOptional()
  @IsBoolean()
  trialPeriod?: boolean;

  @IsOptional()
  @IsBoolean()
  goodForForeigners?: boolean;

  @IsOptional()
  @IsString()
  @MaxLength(1000, {
    message: 'Terms and conditions must not exceed 1000 characters',
  })
  termsAndConditions?: string;

  @IsOptional()
  @IsUUID(4, { message: 'Category ID must be a valid UUID' })
  categoryId?: string;

  @IsOptional()
  @IsBoolean()
  isAvailable?: boolean;

  @IsOptional()
  @IsString()
  slug?: string;
}

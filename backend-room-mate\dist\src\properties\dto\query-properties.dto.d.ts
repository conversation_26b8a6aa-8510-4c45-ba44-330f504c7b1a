import { PropertyType, RoomType, RentTime, PaymentTime } from '@prisma/client';
export interface PropertyFilters {
    page?: number;
    limit?: number;
    search?: string;
    title?: string;
    slug?: string;
    city?: string;
    country?: string;
    neighborhood?: string;
    address?: string;
    latitude?: string;
    longitude?: string;
    type?: PropertyType;
    roomType?: RoomType;
    genderRequired?: string;
    totalRooms?: string;
    availableRooms?: string;
    roomsToComplete?: string;
    size?: string;
    floor?: string;
    bathrooms?: string;
    separatedBathroom?: boolean;
    residentsCount?: string;
    availablePersons?: string;
    minPrice?: string;
    maxPrice?: string;
    rentTime?: RentTime;
    paymentTime?: PaymentTime;
    priceIncludeWaterAndElectricity?: boolean;
    minRating?: number;
    minTotalRatings?: number;
    isVerified?: boolean;
    isAvailable?: boolean;
    categoryId?: string;
    ownerId?: string;
    createdAfter?: string;
    createdBefore?: string;
    updatedAfter?: string;
    updatedBefore?: string;
    allowSmoking?: boolean;
    includeFurniture?: boolean;
    airConditioning?: boolean;
    includeWaterHeater?: boolean;
    parking?: boolean;
    internet?: boolean;
    nearToMetro?: boolean;
    nearToMarket?: boolean;
    elevator?: boolean;
    trialPeriod?: boolean;
    goodForForeigners?: boolean;
    sortBy?: 'createdAt' | 'updatedAt' | 'price' | 'rating' | 'title';
    sortOrder?: 'asc' | 'desc';
}
export interface BaseFilters {
    page?: number;
    limit?: number;
    search?: string;
    title?: string;
    slug?: string;
    city?: string;
    country?: string;
    neighborhood?: string;
    address?: string;
    latitude?: string;
    longitude?: string;
    type?: PropertyType;
    roomType?: RoomType;
    genderRequired?: string;
    totalRooms?: string;
    availableRooms?: string;
    size?: string;
    floor?: string;
    bathrooms?: string;
    separatedBathroom?: boolean;
    residentsCount?: string;
    availablePersons?: string;
    minPrice?: string;
    maxPrice?: string;
    rentTime?: RentTime;
    paymentTime?: PaymentTime;
    priceIncludeWaterAndElectricity?: boolean;
    minRating?: number;
    minTotalRatings?: number;
    isVerified?: boolean;
    isAvailable?: boolean;
    categoryId?: string;
    ownerId?: string;
    createdAfter?: string;
    createdBefore?: string;
    updatedAfter?: string;
    updatedBefore?: string;
    allowSmoking?: boolean;
    includeFurniture?: boolean;
    airConditioning?: boolean;
    includeWaterHeater?: boolean;
    parking?: boolean;
    internet?: boolean;
    nearToMetro?: boolean;
    nearToMarket?: boolean;
    elevator?: boolean;
    trialPeriod?: boolean;
    goodForForeigners?: boolean;
    sortBy?: 'createdAt' | 'updatedAt' | 'price' | 'rating' | 'title';
    sortOrder?: 'asc' | 'desc';
}
export interface PropertySpecificFilters extends BaseFilters {
    roomsToComplete?: string;
}
export interface LocationFilters {
    city?: string;
    country?: string;
    neighborhood?: string;
    address?: string;
    latitude?: string;
    longitude?: string;
}
export interface AmenityFilters {
    allowSmoking?: boolean;
    includeFurniture?: boolean;
    airConditioning?: boolean;
    includeWaterHeater?: boolean;
    parking?: boolean;
    internet?: boolean;
    nearToMetro?: boolean;
    nearToMarket?: boolean;
    elevator?: boolean;
    trialPeriod?: boolean;
    goodForForeigners?: boolean;
    separatedBathroom?: boolean;
    priceIncludeWaterAndElectricity?: boolean;
}
export interface QualityFilters {
    minPrice?: string;
    maxPrice?: string;
    minRating?: number;
    minTotalRatings?: number;
    isVerified?: boolean;
    isAvailable?: boolean;
}
export interface DateRangeFilters {
    createdAfter?: string;
    createdBefore?: string;
    updatedAfter?: string;
    updatedBefore?: string;
}
export interface SpaceFilters {
    totalRooms?: string;
    availableRooms?: string;
    roomsToComplete?: string;
    size?: string;
    floor?: string;
    bathrooms?: string;
    residentsCount?: string;
    availablePersons?: string;
}
export declare class QueryPropertiesDto {
    page?: number;
    limit?: number;
    search?: string;
    title?: string;
    slug?: string;
    type?: PropertyType;
    roomType?: RoomType;
    city?: string;
    country?: string;
    neighborhood?: string;
    address?: string;
    latitude?: string;
    longitude?: string;
    genderRequired?: string;
    totalRooms?: string;
    availableRooms?: string;
    roomsToComplete?: string;
    minPrice?: string;
    maxPrice?: string;
    size?: string;
    floor?: string;
    bathrooms?: string;
    separatedBathroom?: boolean;
    residentsCount?: string;
    availablePersons?: string;
    rentTime?: RentTime;
    paymentTime?: PaymentTime;
    categoryId?: string;
    minRating?: number;
    minTotalRatings?: number;
    ownerId?: string;
    createdAfter?: string;
    createdBefore?: string;
    updatedAfter?: string;
    updatedBefore?: string;
    priceIncludeWaterAndElectricity?: boolean;
    allowSmoking?: boolean;
    includeFurniture?: boolean;
    airConditioning?: boolean;
    includeWaterHeater?: boolean;
    parking?: boolean;
    internet?: boolean;
    nearToMetro?: boolean;
    nearToMarket?: boolean;
    elevator?: boolean;
    trialPeriod?: boolean;
    goodForForeigners?: boolean;
    isVerified?: boolean;
    isAvailable?: boolean;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}

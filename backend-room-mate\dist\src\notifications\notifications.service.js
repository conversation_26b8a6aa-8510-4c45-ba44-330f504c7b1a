"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
let NotificationsService = class NotificationsService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createNotificationDto) {
        try {
            const user = await this.prisma.user.findUnique({
                where: { id: createNotificationDto.userId },
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            if (createNotificationDto.adminId) {
                const admin = await this.prisma.user.findUnique({
                    where: { id: createNotificationDto.adminId },
                });
                if (!admin) {
                    throw new common_1.NotFoundException('Admin not found');
                }
                if (!admin.isAdmin) {
                    throw new common_1.ForbiddenException('User is not an admin');
                }
            }
            const notification = await this.prisma.notification.create({
                data: {
                    title: createNotificationDto.title,
                    message: createNotificationDto.message,
                    userId: createNotificationDto.userId,
                    adminId: createNotificationDto.adminId || createNotificationDto.userId,
                },
                include: {
                    user: {
                        select: {
                            id: true,
                            name: true,
                            email: true,
                        },
                    },
                    admin: {
                        select: {
                            id: true,
                            name: true,
                            email: true,
                        },
                    },
                },
            });
            return notification;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException ||
                error instanceof common_1.ForbiddenException) {
                throw error;
            }
            throw new common_1.BadRequestException('Failed to create notification');
        }
    }
    async findAll(queryDto) {
        const page = parseInt(queryDto.page || '1') || 1;
        const limit = parseInt(queryDto.limit || '10') || 10;
        const skip = (page - 1) * limit;
        const where = {};
        if (queryDto.userId) {
            where.userId = queryDto.userId;
        }
        if (queryDto.adminId) {
            where.adminId = queryDto.adminId;
        }
        if (queryDto.read !== undefined) {
            where.read = queryDto.read;
        }
        if (queryDto.search) {
            where.OR = [
                { title: { contains: queryDto.search, mode: 'insensitive' } },
                { message: { contains: queryDto.search, mode: 'insensitive' } },
            ];
        }
        const orderBy = {};
        if (queryDto.sortBy) {
            orderBy[queryDto.sortBy] = queryDto.sortOrder || 'desc';
        }
        else {
            orderBy.createdAt = 'desc';
        }
        const [notifications, total] = await Promise.all([
            this.prisma.notification.findMany({
                where,
                include: {
                    user: {
                        select: {
                            id: true,
                            name: true,
                            email: true,
                        },
                    },
                    admin: {
                        select: {
                            id: true,
                            name: true,
                            email: true,
                        },
                    },
                },
                orderBy,
                skip,
                take: limit,
            }),
            this.prisma.notification.count({ where }),
        ]);
        return {
            notifications,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    async findOne(id) {
        const notification = await this.prisma.notification.findUnique({
            where: { id },
            include: {
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                    },
                },
                admin: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                    },
                },
            },
        });
        if (!notification) {
            throw new common_1.NotFoundException('Notification not found');
        }
        return notification;
    }
    async update(id, updateNotificationDto) {
        try {
            const existingNotification = await this.prisma.notification.findUnique({
                where: { id },
            });
            if (!existingNotification) {
                throw new common_1.NotFoundException('Notification not found');
            }
            const notification = await this.prisma.notification.update({
                where: { id },
                data: updateNotificationDto,
                include: {
                    user: {
                        select: {
                            id: true,
                            name: true,
                            email: true,
                        },
                    },
                    admin: {
                        select: {
                            id: true,
                            name: true,
                            email: true,
                        },
                    },
                },
            });
            return notification;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.BadRequestException('Failed to update notification');
        }
    }
    async remove(id) {
        try {
            const existingNotification = await this.prisma.notification.findUnique({
                where: { id },
            });
            if (!existingNotification) {
                throw new common_1.NotFoundException('Notification not found');
            }
            await this.prisma.notification.delete({
                where: { id },
            });
            return { message: 'Notification deleted successfully' };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.BadRequestException('Failed to delete notification');
        }
    }
    async markAsRead(id) {
        try {
            const existingNotification = await this.prisma.notification.findUnique({
                where: { id },
            });
            if (!existingNotification) {
                throw new common_1.NotFoundException('Notification not found');
            }
            const notification = await this.prisma.notification.update({
                where: { id },
                data: { read: true },
                include: {
                    user: {
                        select: {
                            id: true,
                            name: true,
                            email: true,
                        },
                    },
                    admin: {
                        select: {
                            id: true,
                            name: true,
                            email: true,
                        },
                    },
                },
            });
            return notification;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.BadRequestException('Failed to mark notification as read');
        }
    }
    async markAllAsRead(userId) {
        try {
            const user = await this.prisma.user.findUnique({
                where: { id: userId },
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            const result = await this.prisma.notification.updateMany({
                where: {
                    userId: userId,
                    read: false,
                },
                data: {
                    read: true,
                },
            });
            return {
                message: `${result.count} notifications marked as read`,
                count: result.count,
            };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.BadRequestException('Failed to mark all notifications as read');
        }
    }
    async getUnreadCount(userId) {
        try {
            const count = await this.prisma.notification.count({
                where: {
                    userId: userId,
                    read: false,
                },
            });
            return { unreadCount: count };
        }
        catch {
            throw new common_1.BadRequestException('Failed to get unread count');
        }
    }
    async broadcastToAllUsers(broadcastNotificationDto) {
        try {
            if (broadcastNotificationDto.adminId) {
                const admin = await this.prisma.user.findUnique({
                    where: { id: broadcastNotificationDto.adminId },
                });
                if (!admin) {
                    throw new common_1.NotFoundException('Admin not found');
                }
                if (!admin.isAdmin) {
                    throw new common_1.ForbiddenException('User is not an admin');
                }
            }
            const users = await this.prisma.user.findMany({
                select: { id: true },
            });
            if (users.length === 0) {
                throw new common_1.NotFoundException('No users found');
            }
            const notificationsData = users.map((user) => ({
                title: broadcastNotificationDto.title,
                message: broadcastNotificationDto.message,
                userId: user.id,
                adminId: broadcastNotificationDto.adminId || user.id,
            }));
            const notifications = await this.prisma.notification.createMany({
                data: notificationsData,
            });
            return {
                message: `Notifications sent to ${users.length} users`,
                count: notifications.count,
            };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException ||
                error instanceof common_1.ForbiddenException) {
                throw error;
            }
            throw new common_1.BadRequestException('Failed to broadcast notifications');
        }
    }
    async sendToSpecificUsers(sendToUsersNotificationDto) {
        try {
            if (sendToUsersNotificationDto.adminId) {
                const admin = await this.prisma.user.findUnique({
                    where: { id: sendToUsersNotificationDto.adminId },
                });
                if (!admin) {
                    throw new common_1.NotFoundException('Admin not found');
                }
                if (!admin.isAdmin) {
                    throw new common_1.ForbiddenException('User is not an admin');
                }
            }
            const users = await this.prisma.user.findMany({
                where: {
                    id: {
                        in: sendToUsersNotificationDto.userIds,
                    },
                },
                select: { id: true },
            });
            if (users.length !== sendToUsersNotificationDto.userIds.length) {
                const foundUserIds = users.map((user) => user.id);
                const notFoundUserIds = sendToUsersNotificationDto.userIds.filter((id) => !foundUserIds.includes(id));
                throw new common_1.NotFoundException(`Users with IDs [${notFoundUserIds.join(', ')}] not found`);
            }
            const notificationsData = sendToUsersNotificationDto.userIds.map((userId) => ({
                title: sendToUsersNotificationDto.title,
                message: sendToUsersNotificationDto.message,
                userId: userId,
                adminId: sendToUsersNotificationDto.adminId || userId,
            }));
            const notifications = await this.prisma.notification.createMany({
                data: notificationsData,
            });
            return {
                message: `Notifications sent to ${sendToUsersNotificationDto.userIds.length} users`,
                count: notifications.count,
                userIds: sendToUsersNotificationDto.userIds,
            };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException ||
                error instanceof common_1.ForbiddenException) {
                throw error;
            }
            throw new common_1.BadRequestException('Failed to send notifications to users');
        }
    }
};
exports.NotificationsService = NotificationsService;
exports.NotificationsService = NotificationsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], NotificationsService);
//# sourceMappingURL=notifications.service.js.map
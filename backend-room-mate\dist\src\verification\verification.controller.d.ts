import { VerificationService } from './verification.service';
import { CreateVerificationRequestDto } from './dto/create-verification-request.dto';
import { QueryVerificationRequestsDto } from './dto/query-verification-requests.dto';
import { QueryVerificationsDto } from './dto/query-verifications.dto';
import { UpdateVerificationDto } from './dto/update-verification.dto';
import { UserPayload } from './interfaces/verification.interface';
export declare class VerificationController {
    private readonly verificationService;
    constructor(verificationService: VerificationService);
    createVerificationRequest(createVerificationRequestDto: CreateVerificationRequestDto, user: UserPayload): Promise<import("./interfaces/verification.interface").VerificationRequestResponse>;
    findAllVerificationRequests(query: QueryVerificationRequestsDto, user: UserPayload): Promise<import("./interfaces/verification.interface").VerificationRequestsQueryResult>;
    findMyVerificationRequests(user: UserPayload): Promise<import("./interfaces/verification.interface").VerificationRequestResponse[]>;
    findOneVerificationRequest(id: string, user: UserPayload): Promise<import("./interfaces/verification.interface").VerificationRequestResponse>;
    deleteVerificationRequest(id: string, user: UserPayload): Promise<{
        message: string;
    }>;
    processVerificationRequest(requestId: string, updateVerificationDto: UpdateVerificationDto, user: UserPayload): Promise<import("./interfaces/verification.interface").VerificationResponse>;
    findAllVerifications(query: QueryVerificationsDto, user: UserPayload): Promise<import("./interfaces/verification.interface").VerificationsQueryResult>;
    findMyVerifications(user: UserPayload): Promise<import("./interfaces/verification.interface").VerificationResponse[]>;
    findOneVerification(id: string, user: UserPayload): Promise<import("./interfaces/verification.interface").VerificationResponse>;
    revokeVerification(id: string, user: UserPayload): Promise<import("./interfaces/verification.interface").VerificationResponse>;
    getVerificationStats(): Promise<{
        totalRequests: number;
        pendingRequests: number;
        approvedVerifications: number;
        rejectedVerifications: number;
    }>;
}

import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { QueryCategoriesDto } from './dto/query-categories.dto';
export declare const createCategoryExample: CreateCategoryDto;
export declare const simpleCategoryExample: CreateCategoryDto;
export declare const updateCategoryExample: UpdateCategoryDto;
export declare const queryExamples: {
    getAllCategories: QueryCategoriesDto;
    searchCategories: QueryCategoriesDto;
};
export declare const apiEndpointExamples: {
    create: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
        body: CreateCategoryDto;
    };
    getAll: {
        method: string;
        url: string;
    };
};
export declare const responseExamples: {
    categoryResponse: {
        id: string;
        name: string;
        icon: string;
        createdAt: string;
        updatedAt: string;
        properties: {
            id: string;
            title: string;
            price: string;
            city: string;
        }[];
    };
};

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validationExamples = exports.apiEndpointExamples = exports.multipleUploadResponse = exports.singleUploadResponse = void 0;
exports.singleUploadResponse = {
    filename: '1749639980880-apartment-main.jpg',
    originalName: 'apartment-main.jpg',
    url: 'https://example.com/uploads/1749639980880-apartment-main.jpg',
    mimetype: 'image/jpeg',
    size: 2048576,
};
exports.multipleUploadResponse = {
    files: [
        {
            filename: '1749639980880-apartment-main.jpg',
            originalName: 'apartment-main.jpg',
            url: 'https://example.com/uploads/1749639980880-apartment-main.jpg',
            mimetype: 'image/jpeg',
            size: 2048576,
        },
        {
            filename: '1749639980881-apartment-living.jpg',
            originalName: 'apartment-living.jpg',
            url: 'https://example.com/uploads/1749639980881-apartment-living.jpg',
            mimetype: 'image/jpeg',
            size: 1536000,
        },
    ],
    count: 2,
};
exports.apiEndpointExamples = {
    uploadSingle: {
        method: 'POST',
        url: '/api/upload/single',
        headers: {
            Authorization: 'Bearer user-jwt-token',
            'Content-Type': 'multipart/form-data',
        },
        body: 'FormData with file field',
    },
    uploadMultiple: {
        method: 'POST',
        url: '/api/upload/multiple',
        headers: {
            Authorization: 'Bearer user-jwt-token',
            'Content-Type': 'multipart/form-data',
        },
        body: 'FormData with files field (array)',
    },
};
exports.validationExamples = {
    allowedFileTypes: [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
        'video/mp4',
        'video/mpeg',
    ],
    maxFileSize: 10485760,
    maxFiles: 5,
};
//# sourceMappingURL=upload.examples.js.map
import { PrismaService } from '../prisma.service';
import { CreateBookingDto } from './dto/create-booking.dto';
import { UpdateBookingDto } from './dto/update-booking.dto';
import { QueryBookingsDto } from './dto/query-bookings.dto';
import { UserPayload } from './interfaces/user.interface';
export declare class BookingsService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    createFromOffer(createBookingDto: CreateBookingDto, user: UserPayload): Promise<{
        success: boolean;
        data: {
            user: {
                id: string;
                name: string;
                email: string;
                phone: string | null;
            };
            property: {
                id: string;
                country: string | null;
                title: string | null;
                images: string[];
                city: string | null;
                address: string | null;
            };
            offer: {
                id: string;
                phone: string;
                message: string;
                price: string;
            };
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            userId: string;
            status: import(".prisma/client").$Enums.BookingStatus;
            propertyId: string;
            startDate: Date;
            endDate: Date | null;
            totalAmount: string;
            depositPaid: boolean;
            offerId: string;
        };
        message: string;
    }>;
    findAll(query: QueryBookingsDto, user?: UserPayload): Promise<{
        success: boolean;
        data: ({
            user: {
                id: string;
                name: string;
                email: string;
                phone: string | null;
            };
            property: {
                id: string;
                country: string | null;
                title: string | null;
                images: string[];
                city: string | null;
                address: string | null;
                price: string | null;
            };
            offer: {
                id: string;
                message: string;
                price: string;
            };
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            userId: string;
            status: import(".prisma/client").$Enums.BookingStatus;
            propertyId: string;
            startDate: Date;
            endDate: Date | null;
            totalAmount: string;
            depositPaid: boolean;
            offerId: string;
        })[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
    }>;
    findOne(id: string): Promise<{
        success: boolean;
        data: {
            user: {
                id: string;
                name: string;
                email: string;
                phone: string | null;
            };
            property: {
                id: string;
                country: string | null;
                title: string | null;
                images: string[];
                city: string | null;
                address: string | null;
                description: string | null;
                price: string | null;
                owner: {
                    id: string;
                    name: string;
                    email: string;
                    phone: string | null;
                };
            };
            offer: {
                id: string;
                phone: string;
                message: string;
                price: string;
                duration: string | null;
                deposit: boolean;
            };
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            userId: string;
            status: import(".prisma/client").$Enums.BookingStatus;
            propertyId: string;
            startDate: Date;
            endDate: Date | null;
            totalAmount: string;
            depositPaid: boolean;
            offerId: string;
        };
    }>;
    update(id: string, updateBookingDto: UpdateBookingDto, user: UserPayload): Promise<{
        success: boolean;
        data: {
            user: {
                id: string;
                name: string;
                email: string;
            };
            property: {
                id: string;
                country: string | null;
                title: string | null;
                city: string | null;
                price: string | null;
            };
            offer: {
                id: string;
                price: string;
            };
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            userId: string;
            status: import(".prisma/client").$Enums.BookingStatus;
            propertyId: string;
            startDate: Date;
            endDate: Date | null;
            totalAmount: string;
            depositPaid: boolean;
            offerId: string;
        };
        message: string;
    }>;
    cancelBooking(id: string, user: UserPayload): Promise<{
        success: boolean;
        data: {
            user: {
                id: string;
                name: string;
                email: string;
            };
            property: {
                id: string;
                country: string | null;
                title: string | null;
                city: string | null;
                price: string | null;
            };
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            userId: string;
            status: import(".prisma/client").$Enums.BookingStatus;
            propertyId: string;
            startDate: Date;
            endDate: Date | null;
            totalAmount: string;
            depositPaid: boolean;
            offerId: string;
        };
        message: string;
    }>;
    completeBooking(id: string, user: UserPayload): Promise<{
        success: boolean;
        data: {
            user: {
                id: string;
                name: string;
                email: string;
            };
            property: {
                id: string;
                country: string | null;
                title: string | null;
                city: string | null;
                price: string | null;
            };
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            userId: string;
            status: import(".prisma/client").$Enums.BookingStatus;
            propertyId: string;
            startDate: Date;
            endDate: Date | null;
            totalAmount: string;
            depositPaid: boolean;
            offerId: string;
        };
        message: string;
    }>;
    private updateBookingStatus;
    getMyBookings(userId: string, query: QueryBookingsDto): Promise<{
        success: boolean;
        data: ({
            property: {
                id: string;
                country: string | null;
                title: string | null;
                images: string[];
                city: string | null;
                address: string | null;
                price: string | null;
            };
            offer: {
                id: string;
                price: string;
            };
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            userId: string;
            status: import(".prisma/client").$Enums.BookingStatus;
            propertyId: string;
            startDate: Date;
            endDate: Date | null;
            totalAmount: string;
            depositPaid: boolean;
            offerId: string;
        })[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
    }>;
    getPropertyBookings(propertyId: string, query: QueryBookingsDto): Promise<{
        success: boolean;
        data: ({
            user: {
                id: string;
                name: string;
                email: string;
                phone: string | null;
            };
            offer: {
                id: string;
                message: string;
                price: string;
            };
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            userId: string;
            status: import(".prisma/client").$Enums.BookingStatus;
            propertyId: string;
            startDate: Date;
            endDate: Date | null;
            totalAmount: string;
            depositPaid: boolean;
            offerId: string;
        })[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
    }>;
    getBookingStats(user: UserPayload): Promise<{
        success: boolean;
        data: {
            statusBreakdown: Record<import(".prisma/client").$Enums.BookingStatus, number>;
            totalRevenue: string;
        };
    }>;
}

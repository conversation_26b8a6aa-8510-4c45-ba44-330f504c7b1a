"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BookingOwnerGuard = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma.service");
let BookingOwnerGuard = class BookingOwnerGuard {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        const bookingId = request.params.id;
        if (!user) {
            throw new common_1.ForbiddenException('Authentication required');
        }
        if (!bookingId) {
            throw new common_1.ForbiddenException('Booking ID is required');
        }
        if (user.isAdmin) {
            return true;
        }
        const booking = await this.prisma.booking.findUnique({
            where: { id: bookingId },
            include: {
                property: {
                    select: { ownerId: true },
                },
            },
        });
        if (!booking) {
            throw new common_1.NotFoundException('Booking not found');
        }
        if (booking.userId === user.sub || booking.property.ownerId === user.sub) {
            return true;
        }
        throw new common_1.ForbiddenException('Access denied');
    }
};
exports.BookingOwnerGuard = BookingOwnerGuard;
exports.BookingOwnerGuard = BookingOwnerGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], BookingOwnerGuard);
//# sourceMappingURL=booking-owner.guard.js.map
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PropertiesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
let PropertiesService = class PropertiesService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createPropertyDto, user) {
        try {
            const slug = await this.generateUniqueSlug(createPropertyDto.title);
            const property = await this.prisma.property.create({
                data: {
                    ...createPropertyDto,
                    slug,
                    ownerId: user.sub,
                },
                include: {
                    category: true,
                    owner: {
                        select: {
                            id: true,
                            name: true,
                            email: true,
                            phone: true,
                            isVerified: true,
                        },
                    },
                    _count: {
                        select: {
                            favorites: true,
                            ratings: true,
                        },
                    },
                },
            });
            return {
                success: true,
                message: 'Property created successfully',
                data: property,
            };
        }
        catch (error) {
            throw new common_1.BadRequestException('Failed to create property');
        }
    }
    async findAll(query) {
        const page = parseInt(query.page?.toString() || '1', 10);
        const limit = parseInt(query.limit?.toString() || '10', 10);
        const offset = (page - 1) * limit;
        const where = {};
        if (query.isAvailable !== undefined) {
            where.isAvailable = query.isAvailable;
        }
        else {
            where.isAvailable = true;
        }
        if (query.search) {
            where.OR = [
                { title: { contains: query.search, mode: 'insensitive' } },
                { description: { contains: query.search, mode: 'insensitive' } },
                { address: { contains: query.search, mode: 'insensitive' } },
                { city: { contains: query.search, mode: 'insensitive' } },
                { country: { contains: query.search, mode: 'insensitive' } },
                { neighborhood: { contains: query.search, mode: 'insensitive' } },
            ];
        }
        if (query.title) {
            where.title = { contains: query.title, mode: 'insensitive' };
        }
        if (query.slug) {
            where.slug = query.slug;
        }
        if (query.city) {
            where.city = { contains: query.city, mode: 'insensitive' };
        }
        if (query.country) {
            where.country = { contains: query.country, mode: 'insensitive' };
        }
        if (query.neighborhood) {
            where.neighborhood = {
                contains: query.neighborhood,
                mode: 'insensitive',
            };
        }
        if (query.address) {
            where.address = { contains: query.address, mode: 'insensitive' };
        }
        if (query.latitude) {
            where.latitude = query.latitude;
        }
        if (query.longitude) {
            where.longitude = query.longitude;
        }
        if (query.type) {
            where.type = query.type;
        }
        if (query.roomType) {
            where.roomType = query.roomType;
        }
        if (query.genderRequired) {
            where.genderRequired = query.genderRequired;
        }
        if (query.totalRooms) {
            where.totalRooms = query.totalRooms;
        }
        if (query.availableRooms) {
            where.availableRooms = query.availableRooms;
        }
        if (query.roomsToComplete) {
            where.roomsToComplete = query.roomsToComplete;
        }
        if (query.size) {
            where.size = query.size;
        }
        if (query.floor) {
            where.floor = query.floor;
        }
        if (query.bathrooms) {
            where.bathrooms = query.bathrooms;
        }
        if (query.residentsCount) {
            where.residentsCount = query.residentsCount;
        }
        if (query.availablePersons) {
            where.availablePersons = query.availablePersons;
        }
        if (query.rentTime) {
            where.rentTime = query.rentTime;
        }
        if (query.paymentTime) {
            where.paymentTime = query.paymentTime;
        }
        if (query.categoryId) {
            where.categoryId = query.categoryId;
        }
        if (query.ownerId) {
            where.ownerId = query.ownerId;
        }
        if (query.isVerified !== undefined) {
            where.isVerified = query.isVerified;
        }
        if (query.minPrice || query.maxPrice) {
            where.price = {};
            if (query.minPrice) {
                where.price.gte = query.minPrice;
            }
            if (query.maxPrice) {
                where.price.lte = query.maxPrice;
            }
        }
        if (query.minRating !== undefined) {
            where.rating = { gte: query.minRating };
        }
        if (query.minTotalRatings !== undefined) {
            where.totalRatings = { gte: query.minTotalRatings };
        }
        if (query.createdAfter || query.createdBefore) {
            where.createdAt = {};
            if (query.createdAfter) {
                where.createdAt.gte = new Date(query.createdAfter);
            }
            if (query.createdBefore) {
                where.createdAt.lte = new Date(query.createdBefore);
            }
        }
        if (query.updatedAfter || query.updatedBefore) {
            where.updatedAt = {};
            if (query.updatedAfter) {
                where.updatedAt.gte = new Date(query.updatedAfter);
            }
            if (query.updatedBefore) {
                where.updatedAt.lte = new Date(query.updatedBefore);
            }
        }
        const booleanFilters = [
            'separatedBathroom',
            'priceIncludeWaterAndElectricity',
            'allowSmoking',
            'includeFurniture',
            'airConditioning',
            'includeWaterHeater',
            'parking',
            'internet',
            'nearToMetro',
            'nearToMarket',
            'elevator',
            'trialPeriod',
            'goodForForeigners',
        ];
        booleanFilters.forEach((filter) => {
            if (query[filter] !== undefined) {
                where[filter] = query[filter];
            }
        });
        const sortBy = query.sortBy || 'createdAt';
        const sortOrder = query.sortOrder || 'desc';
        const [properties, total] = await Promise.all([
            this.prisma.property.findMany({
                where,
                skip: offset,
                take: limit,
                orderBy: { [sortBy]: sortOrder },
                include: {
                    category: true,
                    owner: {
                        select: {
                            id: true,
                            name: true,
                            email: true,
                            phone: true,
                            isVerified: true,
                        },
                    },
                    _count: {
                        select: {
                            favorites: true,
                            ratings: true,
                        },
                    },
                },
            }),
            this.prisma.property.count({ where }),
        ]);
        const totalPages = Math.ceil(total / limit);
        return {
            success: true,
            data: properties,
            pagination: {
                page,
                limit,
                total,
                totalPages,
                hasNext: page < totalPages,
                hasPrev: page > 1,
            },
        };
    }
    async findOne(id) {
        const property = await this.prisma.property.findUnique({
            where: { id },
            include: {
                category: true,
                owner: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        phone: true,
                        isVerified: true,
                    },
                },
                offers: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                name: true,
                                email: true,
                                phone: true,
                                isVerified: true,
                            },
                        },
                    },
                    orderBy: { createdAt: 'desc' },
                },
                comments: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                name: true,
                                email: true,
                                isVerified: true,
                            },
                        },
                    },
                    orderBy: { createdAt: 'desc' },
                },
                ratings: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                    },
                    orderBy: { createdAt: 'desc' },
                },
                _count: {
                    select: {
                        favorites: true,
                        ratings: true,
                        offers: true,
                        comments: true,
                    },
                },
            },
        });
        if (!property) {
            throw new common_1.NotFoundException('Property not found');
        }
        return {
            success: true,
            data: property,
        };
    }
    async findBySlug(slug) {
        const property = await this.prisma.property.findUnique({
            where: { slug },
            include: {
                category: true,
                owner: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        phone: true,
                        isVerified: true,
                    },
                },
                offers: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                name: true,
                                email: true,
                                phone: true,
                                isVerified: true,
                            },
                        },
                    },
                    orderBy: { createdAt: 'desc' },
                },
                comments: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                name: true,
                                email: true,
                                isVerified: true,
                            },
                        },
                    },
                    orderBy: { createdAt: 'desc' },
                },
                ratings: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                    },
                    orderBy: { createdAt: 'desc' },
                },
                _count: {
                    select: {
                        favorites: true,
                        ratings: true,
                        offers: true,
                        comments: true,
                    },
                },
            },
        });
        if (!property) {
            throw new common_1.NotFoundException('Property not found');
        }
        return {
            success: true,
            data: property,
        };
    }
    async update(id, updatePropertyDto, user) {
        const property = await this.prisma.property.findUnique({
            where: { id },
        });
        if (!property) {
            throw new common_1.NotFoundException('Property not found');
        }
        if (property.ownerId !== user.sub && !user.isAdmin) {
            throw new common_1.ForbiddenException('You can only update your own properties');
        }
        try {
            const updateData = { ...updatePropertyDto };
            if (updatePropertyDto.title &&
                updatePropertyDto.title !== property.title) {
                updateData.slug = await this.generateUniqueSlug(updatePropertyDto.title);
            }
            const updatedProperty = await this.prisma.property.update({
                where: { id },
                data: updateData,
                include: {
                    category: true,
                    owner: {
                        select: {
                            id: true,
                            name: true,
                            email: true,
                            phone: true,
                            isVerified: true,
                        },
                    },
                    _count: {
                        select: {
                            favorites: true,
                            ratings: true,
                        },
                    },
                },
            });
            return {
                success: true,
                message: 'Property updated successfully',
                data: updatedProperty,
            };
        }
        catch (error) {
            throw new common_1.BadRequestException('Failed to update property');
        }
    }
    async remove(id, user) {
        const property = await this.prisma.property.findUnique({
            where: { id },
        });
        if (!property) {
            throw new common_1.NotFoundException('Property not found');
        }
        if (property.ownerId !== user.sub && !user.isAdmin) {
            throw new common_1.ForbiddenException('You can only delete your own properties');
        }
        try {
            await this.prisma.property.delete({
                where: { id },
            });
            return {
                success: true,
                message: 'Property deleted successfully',
            };
        }
        catch (error) {
            throw new common_1.BadRequestException('Failed to delete property');
        }
    }
    async getMyProperties(userId, query) {
        const { page = 1, limit = 10, search, type, roomType, isVerified, isAvailable, sortBy = 'createdAt', sortOrder = 'desc', } = query;
        const offset = (page - 1) * limit;
        const where = { ownerId: userId };
        if (search) {
            where.OR = [
                { title: { contains: search, mode: 'insensitive' } },
                { description: { contains: search, mode: 'insensitive' } },
            ];
        }
        if (type)
            where.type = type;
        if (roomType)
            where.roomType = roomType;
        if (isVerified !== undefined)
            where.isVerified = isVerified;
        if (isAvailable !== undefined)
            where.isAvailable = isAvailable;
        const [properties, total] = await Promise.all([
            this.prisma.property.findMany({
                where,
                skip: offset,
                take: limit,
                orderBy: { [sortBy]: sortOrder },
                include: {
                    category: true,
                    _count: {
                        select: {
                            favorites: true,
                            ratings: true,
                            offers: true,
                        },
                    },
                },
            }),
            this.prisma.property.count({ where }),
        ]);
        const totalPages = Math.ceil(total / limit);
        return {
            success: true,
            data: properties,
            pagination: {
                page,
                limit,
                total,
                totalPages,
                hasNext: page < totalPages,
                hasPrev: page > 1,
            },
        };
    }
    async toggleFavorite(id, user) {
        const property = await this.prisma.property.findUnique({
            where: { id },
            include: {
                favorites: {
                    where: { id: user.sub },
                },
            },
        });
        if (!property) {
            throw new common_1.NotFoundException('Property not found');
        }
        const isFavorite = property.favorites.length > 0;
        if (isFavorite) {
            await this.prisma.property.update({
                where: { id },
                data: {
                    favorites: {
                        disconnect: { id: user.sub },
                    },
                },
            });
            return {
                success: true,
                message: 'Property removed from favorites',
                isFavorite: false,
            };
        }
        else {
            await this.prisma.property.update({
                where: { id },
                data: {
                    favorites: {
                        connect: { id: user.sub },
                    },
                },
            });
            return {
                success: true,
                message: 'Property added to favorites',
                isFavorite: true,
            };
        }
    }
    async getFavorites(userId, query) {
        const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc', } = query;
        const offset = (page - 1) * limit;
        const [properties, total] = await Promise.all([
            this.prisma.property.findMany({
                where: {
                    favorites: {
                        some: { id: userId },
                    },
                },
                skip: offset,
                take: limit,
                orderBy: { [sortBy]: sortOrder },
                include: {
                    category: true,
                    owner: {
                        select: {
                            id: true,
                            name: true,
                            email: true,
                            phone: true,
                            isVerified: true,
                        },
                    },
                    _count: {
                        select: {
                            favorites: true,
                            ratings: true,
                        },
                    },
                },
            }),
            this.prisma.property.count({
                where: {
                    favorites: {
                        some: { id: userId },
                    },
                },
            }),
        ]);
        const totalPages = Math.ceil(total / limit);
        return {
            success: true,
            data: properties,
            pagination: {
                page,
                limit,
                total,
                totalPages,
                hasNext: page < totalPages,
                hasPrev: page > 1,
            },
        };
    }
    async generateUniqueSlug(title) {
        const baseSlug = title
            .toLowerCase()
            .trim()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-');
        let slug = baseSlug;
        let counter = 1;
        while (await this.prisma.property.findUnique({ where: { slug } })) {
            slug = `${baseSlug}-${counter}`;
            counter++;
        }
        return slug;
    }
};
exports.PropertiesService = PropertiesService;
exports.PropertiesService = PropertiesService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], PropertiesService);
//# sourceMappingURL=properties.service.js.map
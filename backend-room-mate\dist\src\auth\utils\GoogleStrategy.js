"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GoogleStrategy = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const passport_google_oauth20_1 = require("passport-google-oauth20");
let GoogleStrategy = class GoogleStrategy extends (0, passport_1.PassportStrategy)(passport_google_oauth20_1.Strategy) {
    constructor() {
        super({
            clientID: '827911266769-4e6afmb37b338r3f4u3aap6nf6ulbqsu.apps.googleusercontent.com',
            clientSecret: '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',
            callbackURL: 'http://localhost:4000/api/auth/google/redirect',
            scope: ['email', 'profile'],
        });
    }
    async validate(accessToken, refreshToken, profile) {
        await new Promise((resolve) => setTimeout(resolve, 1000));
        console.log('GoogleStrategy');
        console.log(accessToken, refreshToken, profile);
        return {
            accessToken,
            refreshToken,
            profile,
        };
    }
};
exports.GoogleStrategy = GoogleStrategy;
exports.GoogleStrategy = GoogleStrategy = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], GoogleStrategy);
//# sourceMappingURL=GoogleStrategy.js.map
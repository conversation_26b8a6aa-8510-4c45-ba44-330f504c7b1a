{"node": {"7f7ed2832f654977865887574304abff898a3508c9": {"workers": {"app/[locale]/(dashboard)/dashboard/categories/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(dashboard)/dashboard/categories/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/auth.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/actions/categories.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/upload.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(dashboard)/dashboard/categories/page": "action-browser"}}, "7fc81d422d946583d67e965e01874ba827ddfb076a": {"workers": {"app/[locale]/(dashboard)/dashboard/categories/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(dashboard)/dashboard/categories/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/auth.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/actions/categories.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/upload.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(dashboard)/dashboard/categories/page": "action-browser"}}, "7ff5228fd53ce23c0ab89ce73ea7504be3fdad58ed": {"workers": {"app/[locale]/(dashboard)/dashboard/categories/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(dashboard)/dashboard/categories/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/auth.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/actions/categories.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/upload.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(dashboard)/dashboard/categories/page": "action-browser"}}, "7f66a103c6ddae774301f8b2622c6ae732936859ae": {"workers": {"app/[locale]/(dashboard)/dashboard/categories/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(dashboard)/dashboard/categories/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/auth.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/actions/categories.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/upload.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(dashboard)/dashboard/categories/page": "action-browser"}}, "7fce98afd99c671ec24efe38503d6078d69fdd6123": {"workers": {"app/[locale]/(dashboard)/dashboard/categories/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(dashboard)/dashboard/categories/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/auth.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/actions/categories.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/upload.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(dashboard)/dashboard/categories/page": "action-browser"}}, "7f0596f7ef66a65528bf03142f5daf26e6963a615f": {"workers": {"app/[locale]/(dashboard)/dashboard/categories/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(dashboard)/dashboard/categories/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/auth.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/actions/categories.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/upload.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(dashboard)/dashboard/categories/page": "action-browser"}}, "7f65b84ea1277151fafed13bbb1d36bbc774724ce1": {"workers": {"app/[locale]/(dashboard)/dashboard/categories/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(dashboard)/dashboard/categories/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/auth.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/actions/categories.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/upload.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(dashboard)/dashboard/categories/page": "action-browser"}}, "7f438d279e7d9390fb899045e3015546f4dc8abccc": {"workers": {"app/[locale]/(dashboard)/dashboard/categories/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(dashboard)/dashboard/categories/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/auth.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/actions/categories.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/upload.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(dashboard)/dashboard/categories/page": "action-browser"}}, "7f4b4218cc6843a8973a50edee39b6e006ef1e2787": {"workers": {"app/[locale]/(dashboard)/dashboard/categories/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(dashboard)/dashboard/categories/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/auth.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/actions/categories.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/upload.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(dashboard)/dashboard/categories/page": "action-browser"}}, "7fd53009883b215fe6cf98c96fbab772157a540260": {"workers": {"app/[locale]/(dashboard)/dashboard/categories/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(dashboard)/dashboard/categories/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/auth.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/actions/categories.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/upload.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(dashboard)/dashboard/categories/page": "action-browser"}}, "7f0f07b4d5f6fea01dce7a847de91cfb98e8cef590": {"workers": {"app/[locale]/(dashboard)/dashboard/categories/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(dashboard)/dashboard/categories/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/auth.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/actions/categories.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/upload.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(dashboard)/dashboard/categories/page": "action-browser"}}}, "edge": {}}
import { NotificationsService } from './notifications.service';
import { CreateNotificationDto, BroadcastNotificationDto, SendToUsersNotificationDto } from './dto/create-notification.dto';
import { UpdateNotificationDto } from './dto/update-notification.dto';
import { QueryNotificationsDto } from './dto/query-notifications.dto';
export declare class NotificationsController {
    private readonly notificationsService;
    constructor(notificationsService: NotificationsService);
    create(createNotificationDto: CreateNotificationDto): Promise<{
        user: {
            id: string;
            name: string;
            email: string;
        };
        admin: {
            id: string;
            name: string;
            email: string;
        };
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        title: string;
        message: string;
        read: boolean;
        adminId: string;
        userId: string;
    }>;
    broadcastToAll(broadcastNotificationDto: BroadcastNotificationDto): Promise<{
        message: string;
        count: number;
    }>;
    sendToUsers(sendToUsersNotificationDto: SendToUsersNotificationDto): Promise<{
        message: string;
        count: number;
        userIds: string[];
    }>;
    findAll(queryDto: QueryNotificationsDto): Promise<{
        notifications: ({
            user: {
                id: string;
                name: string;
                email: string;
            };
            admin: {
                id: string;
                name: string;
                email: string;
            };
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            title: string;
            message: string;
            read: boolean;
            adminId: string;
            userId: string;
        })[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    getUnreadCount(userId: string): Promise<{
        unreadCount: number;
    }>;
    findOne(id: string): Promise<{
        user: {
            id: string;
            name: string;
            email: string;
        };
        admin: {
            id: string;
            name: string;
            email: string;
        };
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        title: string;
        message: string;
        read: boolean;
        adminId: string;
        userId: string;
    }>;
    update(id: string, updateNotificationDto: UpdateNotificationDto): Promise<{
        user: {
            id: string;
            name: string;
            email: string;
        };
        admin: {
            id: string;
            name: string;
            email: string;
        };
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        title: string;
        message: string;
        read: boolean;
        adminId: string;
        userId: string;
    }>;
    markAsRead(id: string): Promise<{
        user: {
            id: string;
            name: string;
            email: string;
        };
        admin: {
            id: string;
            name: string;
            email: string;
        };
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        title: string;
        message: string;
        read: boolean;
        adminId: string;
        userId: string;
    }>;
    markAllAsRead(userId: string): Promise<{
        message: string;
        count: number;
    }>;
    remove(id: string): Promise<{
        message: string;
    }>;
}

{"version": 3, "file": "notifications.examples.js", "sourceRoot": "", "sources": ["../../../src/notifications/notifications.examples.ts"], "names": [], "mappings": ";;;AAca,QAAA,yBAAyB,GAA0B;IAC9D,KAAK,EAAE,oBAAoB;IAC3B,OAAO,EACL,gFAAgF;IAClF,MAAM,EAAE,eAAe;CACxB,CAAC;AAGW,QAAA,wBAAwB,GAA0B;IAC7D,KAAK,EAAE,uBAAuB;IAC9B,OAAO,EAAE,8DAA8D;IACvE,MAAM,EAAE,eAAe;IACvB,OAAO,EAAE,gBAAgB;CAC1B,CAAC;AAGW,QAAA,4BAA4B,GAA6B;IACpE,KAAK,EAAE,oBAAoB;IAC3B,OAAO,EACL,gGAAgG;IAClG,OAAO,EAAE,gBAAgB;CAC1B,CAAC;AAGW,QAAA,8BAA8B,GAA+B;IACxE,KAAK,EAAE,eAAe;IACtB,OAAO,EACL,wFAAwF;IAC1F,OAAO,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,CAAC;IAC7E,OAAO,EAAE,gBAAgB;CAC1B,CAAC;AAGW,QAAA,yBAAyB,GAA0B;IAC9D,IAAI,EAAE,IAAI;CACX,CAAC;AAGW,QAAA,aAAa,GAAG;IAC3B,oBAAoB,EAAE;QACpB,IAAI,EAAE,GAAG;QACT,KAAK,EAAE,IAAI;QACX,MAAM,EAAE,WAAoB;QAC5B,SAAS,EAAE,MAAe;KACF;IAE1B,sBAAsB,EAAE;QACtB,IAAI,EAAE,KAAK;QACX,IAAI,EAAE,GAAG;QACT,KAAK,EAAE,IAAI;KACa;IAE1B,mBAAmB,EAAE;QACnB,MAAM,EAAE,OAAO;QACf,IAAI,EAAE,GAAG;QACT,KAAK,EAAE,IAAI;KACa;CAC3B,CAAC;AAGW,QAAA,mBAAmB,GAAG;IACjC,MAAM,EAAE;QACN,MAAM,EAAE,MAAM;QACd,GAAG,EAAE,oBAAoB;QACzB,OAAO,EAAE,EAAE,aAAa,EAAE,wBAAwB,EAAE;QACpD,IAAI,EAAE,iCAAyB;KAChC;IACD,SAAS,EAAE;QACT,MAAM,EAAE,MAAM;QACd,GAAG,EAAE,8BAA8B;QACnC,OAAO,EAAE,EAAE,aAAa,EAAE,wBAAwB,EAAE;QACpD,IAAI,EAAE,oCAA4B;KACnC;IACD,WAAW,EAAE;QACX,MAAM,EAAE,MAAM;QACd,GAAG,EAAE,kCAAkC;QACvC,OAAO,EAAE,EAAE,aAAa,EAAE,wBAAwB,EAAE;QACpD,IAAI,EAAE,sCAA8B;KACrC;IACD,oBAAoB,EAAE;QACpB,MAAM,EAAE,KAAK;QACb,GAAG,EAAE,yCAAyC;QAC9C,OAAO,EAAE,EAAE,aAAa,EAAE,uBAAuB,EAAE;KACpD;IACD,UAAU,EAAE;QACV,MAAM,EAAE,OAAO;QACf,GAAG,EAAE,+CAA+C;QACpD,OAAO,EAAE,EAAE,aAAa,EAAE,uBAAuB,EAAE;KACpD;IACD,aAAa,EAAE;QACb,MAAM,EAAE,OAAO;QACf,GAAG,EAAE,2CAA2C;QAChD,OAAO,EAAE,EAAE,aAAa,EAAE,uBAAuB,EAAE;KACpD;IACD,cAAc,EAAE;QACd,MAAM,EAAE,KAAK;QACb,GAAG,EAAE,+CAA+C;QACpD,OAAO,EAAE,EAAE,aAAa,EAAE,uBAAuB,EAAE;KACpD;CACF,CAAC;AAGW,QAAA,gBAAgB,GAAG;IAC9B,oBAAoB,EAAE;QACpB,EAAE,EAAE,uBAAuB;QAC3B,KAAK,EAAE,oBAAoB;QAC3B,OAAO,EAAE,kDAAkD;QAC3D,IAAI,EAAE,KAAK;QACX,SAAS,EAAE,0BAA0B;QACrC,IAAI,EAAE;YACJ,EAAE,EAAE,eAAe;YACnB,IAAI,EAAE,cAAc;YACpB,KAAK,EAAE,mBAAmB;SAC3B;QACD,KAAK,EAAE;YACL,EAAE,EAAE,gBAAgB;YACpB,IAAI,EAAE,YAAY;YAClB,KAAK,EAAE,mBAAmB;SAC3B;KACF;IAED,iBAAiB,EAAE;QACjB,OAAO,EAAE,iCAAiC;QAC1C,KAAK,EAAE,GAAG;KACX;IAED,mBAAmB,EAAE;QACnB,OAAO,EAAE,+BAA+B;QACxC,KAAK,EAAE,CAAC;QACR,OAAO,EAAE;YACP,eAAe;YACf,eAAe;YACf,eAAe;YACf,eAAe;SAChB;KACF;IAED,mBAAmB,EAAE;QACnB,WAAW,EAAE,CAAC;KACf;IAED,qBAAqB,EAAE;QACrB,OAAO,EAAE,gCAAgC;QACzC,KAAK,EAAE,CAAC;KACT;IAED,yBAAyB,EAAE;QACzB,aAAa,EAAE;YACb;gBACE,EAAE,EAAE,uBAAuB;gBAC3B,KAAK,EAAE,oBAAoB;gBAC3B,OAAO,EAAE,kDAAkD;gBAC3D,IAAI,EAAE,KAAK;gBACX,SAAS,EAAE,0BAA0B;gBACrC,IAAI,EAAE;oBACJ,EAAE,EAAE,eAAe;oBACnB,IAAI,EAAE,cAAc;oBACpB,KAAK,EAAE,mBAAmB;iBAC3B;aACF;SACF;QACD,UAAU,EAAE;YACV,IAAI,EAAE,CAAC;YACP,KAAK,EAAE,EAAE;YACT,KAAK,EAAE,CAAC;YACR,UAAU,EAAE,CAAC;SACd;KACF;CACF,CAAC;AAGW,QAAA,cAAc,GAAG;IAC5B,kBAAkB,EAAE;QAClB,WAAW,EAAE,6CAA6C;QAC1D,QAAQ,EAAE,8BAA8B;QACxC,OAAO,EAAE;YACP,KAAK,EAAE,qBAAqB;YAC5B,OAAO,EACL,2EAA2E;YAC7E,OAAO,EAAE,gBAAgB;SAC1B;KACF;IAED,mBAAmB,EAAE;QACnB,WAAW,EAAE,kDAAkD;QAC/D,QAAQ,EAAE,kCAAkC;QAC5C,OAAO,EAAE;YACP,KAAK,EAAE,oBAAoB;YAC3B,OAAO,EACL,mEAAmE;YACrE,OAAO,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,gBAAgB,CAAC;YAC/D,OAAO,EAAE,sBAAsB;SAChC;KACF;IAED,oBAAoB,EAAE;QACpB,WAAW,EAAE,+CAA+C;QAC5D,QAAQ,EAAE,oBAAoB;QAC9B,OAAO,EAAE;YACP,KAAK,EAAE,mBAAmB;YAC1B,OAAO,EAAE,2DAA2D;YACpE,MAAM,EAAE,eAAe;SACxB;KACF;CACF,CAAC"}
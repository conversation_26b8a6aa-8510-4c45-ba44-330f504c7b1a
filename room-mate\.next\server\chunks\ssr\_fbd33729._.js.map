{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/services/api.ts"], "sourcesContent": ["import { cookies } from \"next/headers\";\r\n\r\n// API utility types\r\nexport interface ApiResponse<T = any> {\r\n  data?: T;\r\n  error?: string;\r\n  status: number;\r\n}\r\n\r\nexport interface ApiOptions {\r\n  headers?: Record<string, string>;\r\n  timeout?: number;\r\n}\r\n\r\nexport type RequestData = Record<string, any> | FormData | null;\r\n\r\n// Base API configuration\r\nconst BASE_URL = process.env.NEXT_PUBLIC_API_URL || \"\";\r\n\r\nif (!BASE_URL) {\r\n  console.warn(\"NEXT_PUBLIC_API_URL is not defined in environment variables\");\r\n}\r\n\r\n// Default headers\r\nconst DEFAULT_HEADERS = {\r\n  Accept: \"application/json\",\r\n};\r\n\r\n// Helper function to determine if data should be sent as FormData\r\nconst shouldSendAsFormData = (data: RequestData): data is FormData => {\r\n  return data instanceof FormData;\r\n};\r\n\r\n// Helper function to prepare request headers\r\nconst prepareHeaders = (\r\n  data: RequestData,\r\n  customHeaders: Record<string, string> = {}\r\n): Record<string, string> => {\r\n  const headers: Record<string, string> = {\r\n    ...DEFAULT_HEADERS,\r\n    ...customHeaders,\r\n  };\r\n\r\n  // Don't set Content-Type for FormData - let browser set it with boundary\r\n  if (!shouldSendAsFormData(data) && data !== null) {\r\n    headers[\"Content-Type\"] = \"application/json\";\r\n  }\r\n\r\n  return headers;\r\n};\r\n\r\n// Helper function to prepare request body\r\nconst prepareBody = (data: RequestData): string | FormData | undefined => {\r\n  if (data === null || data === undefined) {\r\n    return undefined;\r\n  }\r\n\r\n  if (shouldSendAsFormData(data)) {\r\n    return data;\r\n  }\r\n\r\n  return JSON.stringify(data);\r\n};\r\n\r\n// Generic API request function\r\nconst apiRequest = async <T = any>(\r\n  endpoint: string,\r\n  method: \"GET\" | \"POST\" | \"PATCH\" | \"DELETE\",\r\n  data: RequestData = null,\r\n  options: ApiOptions = {}\r\n): Promise<ApiResponse<T>> => {\r\n  try {\r\n    const cookieStore = await cookies();\r\n    const token = cookieStore.get(\"roommate-finder-token\")?.value;\r\n    const url = `${BASE_URL}${\r\n      endpoint.startsWith(\"/\") ? endpoint : `/${endpoint}`\r\n    }`;\r\n    const headers = prepareHeaders(data, {\r\n      ...options.headers,\r\n      Authorization: `Bearer ${token}`,\r\n    });\r\n\r\n    const requestConfig: RequestInit = {\r\n      method,\r\n      headers,\r\n      cache: \"no-store\",\r\n      ...(method !== \"GET\" && { body: prepareBody(data) }),\r\n    };\r\n\r\n    // Add timeout if specified\r\n    const controller = new AbortController();\r\n    if (options.timeout) {\r\n      setTimeout(() => controller.abort(), options.timeout);\r\n      requestConfig.signal = controller.signal;\r\n    }\r\n\r\n    const response = await fetch(url, requestConfig);\r\n\r\n    let responseData: T;\r\n    const contentType = response.headers.get(\"content-type\");\r\n\r\n    if (contentType && contentType.includes(\"application/json\")) {\r\n      responseData = await response.json();\r\n    } else {\r\n      responseData = (await response.text()) as unknown as T;\r\n    }\r\n\r\n    if (!response.ok) {\r\n      console.log(\"responseData\", responseData);\r\n      return {\r\n        error:\r\n          typeof responseData === \"string\" ? responseData : \"Request failed\",\r\n        status: response.status,\r\n      };\r\n    }\r\n\r\n    return {\r\n      data: responseData,\r\n      status: response.status,\r\n    };\r\n  } catch (error) {\r\n    if (error instanceof Error) {\r\n      return {\r\n        error: error.name === \"AbortError\" ? \"Request timeout\" : error.message,\r\n        status: 0,\r\n      };\r\n    }\r\n\r\n    return {\r\n      error: \"An unexpected error occurred\",\r\n      status: 0,\r\n    };\r\n  }\r\n};\r\n\r\n// API methods\r\nexport const api = {\r\n  /**\r\n   * GET request\r\n   * @param endpoint - API endpoint (e.g., '/users' or 'users')\r\n   * @param options - Additional request options\r\n   */\r\n  get: <T = any>(\r\n    endpoint: string,\r\n    options?: ApiOptions\r\n  ): Promise<ApiResponse<T>> => {\r\n    return apiRequest<T>(endpoint, \"GET\", null, options);\r\n  },\r\n\r\n  /**\r\n   * POST request\r\n   * @param endpoint - API endpoint (e.g., '/users' or 'users')\r\n   * @param data - Request data (JSON object or FormData)\r\n   * @param options - Additional request options\r\n   */\r\n  post: <T = any>(\r\n    endpoint: string,\r\n    data?: RequestData,\r\n    options?: ApiOptions\r\n  ): Promise<ApiResponse<T>> => {\r\n    return apiRequest<T>(endpoint, \"POST\", data, options);\r\n  },\r\n\r\n  /**\r\n   * PATCH request\r\n   * @param endpoint - API endpoint (e.g., '/users/1' or 'users/1')\r\n   * @param data - Request data (JSON object or FormData)\r\n   * @param options - Additional request options\r\n   */\r\n  patch: <T = any>(\r\n    endpoint: string,\r\n    data?: RequestData,\r\n    options?: ApiOptions\r\n  ): Promise<ApiResponse<T>> => {\r\n    console.log(\"endpoint\", endpoint);\r\n    console.log(\"data\", data);\r\n    return apiRequest<T>(endpoint, \"PATCH\", data, options);\r\n  },\r\n\r\n  /**\r\n   * DELETE request\r\n   * @param endpoint - API endpoint (e.g., '/users/1' or 'users/1')\r\n   * @param data - Optional request data\r\n   * @param options - Additional request options\r\n   */\r\n  delete: <T = any>(\r\n    endpoint: string,\r\n    data?: RequestData,\r\n    options?: ApiOptions\r\n  ): Promise<ApiResponse<T>> => {\r\n    return apiRequest<T>(endpoint, \"DELETE\", data, options);\r\n  },\r\n};\r\n\r\n// Export individual methods for convenience\r\nexport const { get, post, patch, delete: del } = api;\r\n\r\n// Default export\r\nexport default api;\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAgBA,yBAAyB;AACzB,MAAM,WAAW,oEAAmC;AAEpD,uCAAe;;AAEf;AAEA,kBAAkB;AAClB,MAAM,kBAAkB;IACtB,QAAQ;AACV;AAEA,kEAAkE;AAClE,MAAM,uBAAuB,CAAC;IAC5B,OAAO,gBAAgB;AACzB;AAEA,6CAA6C;AAC7C,MAAM,iBAAiB,CACrB,MACA,gBAAwC,CAAC,CAAC;IAE1C,MAAM,UAAkC;QACtC,GAAG,eAAe;QAClB,GAAG,aAAa;IAClB;IAEA,yEAAyE;IACzE,IAAI,CAAC,qBAAqB,SAAS,SAAS,MAAM;QAChD,OAAO,CAAC,eAAe,GAAG;IAC5B;IAEA,OAAO;AACT;AAEA,0CAA0C;AAC1C,MAAM,cAAc,CAAC;IACnB,IAAI,SAAS,QAAQ,SAAS,WAAW;QACvC,OAAO;IACT;IAEA,IAAI,qBAAqB,OAAO;QAC9B,OAAO;IACT;IAEA,OAAO,KAAK,SAAS,CAAC;AACxB;AAEA,+BAA+B;AAC/B,MAAM,aAAa,OACjB,UACA,QACA,OAAoB,IAAI,EACxB,UAAsB,CAAC,CAAC;IAExB,IAAI;QACF,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;QAChC,MAAM,QAAQ,YAAY,GAAG,CAAC,0BAA0B;QACxD,MAAM,MAAM,GAAG,WACb,SAAS,UAAU,CAAC,OAAO,WAAW,CAAC,CAAC,EAAE,UAAU,EACpD;QACF,MAAM,UAAU,eAAe,MAAM;YACnC,GAAG,QAAQ,OAAO;YAClB,eAAe,CAAC,OAAO,EAAE,OAAO;QAClC;QAEA,MAAM,gBAA6B;YACjC;YACA;YACA,OAAO;YACP,GAAI,WAAW,SAAS;gBAAE,MAAM,YAAY;YAAM,CAAC;QACrD;QAEA,2BAA2B;QAC3B,MAAM,aAAa,IAAI;QACvB,IAAI,QAAQ,OAAO,EAAE;YACnB,WAAW,IAAM,WAAW,KAAK,IAAI,QAAQ,OAAO;YACpD,cAAc,MAAM,GAAG,WAAW,MAAM;QAC1C;QAEA,MAAM,WAAW,MAAM,MAAM,KAAK;QAElC,IAAI;QACJ,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC;QAEzC,IAAI,eAAe,YAAY,QAAQ,CAAC,qBAAqB;YAC3D,eAAe,MAAM,SAAS,IAAI;QACpC,OAAO;YACL,eAAgB,MAAM,SAAS,IAAI;QACrC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,GAAG,CAAC,gBAAgB;YAC5B,OAAO;gBACL,OACE,OAAO,iBAAiB,WAAW,eAAe;gBACpD,QAAQ,SAAS,MAAM;YACzB;QACF;QAEA,OAAO;YACL,MAAM;YACN,QAAQ,SAAS,MAAM;QACzB;IACF,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,OAAO;YAC1B,OAAO;gBACL,OAAO,MAAM,IAAI,KAAK,eAAe,oBAAoB,MAAM,OAAO;gBACtE,QAAQ;YACV;QACF;QAEA,OAAO;YACL,OAAO;YACP,QAAQ;QACV;IACF;AACF;AAGO,MAAM,MAAM;IACjB;;;;GAIC,GACD,KAAK,CACH,UACA;QAEA,OAAO,WAAc,UAAU,OAAO,MAAM;IAC9C;IAEA;;;;;GAKC,GACD,MAAM,CACJ,UACA,MACA;QAEA,OAAO,WAAc,UAAU,QAAQ,MAAM;IAC/C;IAEA;;;;;GAKC,GACD,OAAO,CACL,UACA,MACA;QAEA,QAAQ,GAAG,CAAC,YAAY;QACxB,QAAQ,GAAG,CAAC,QAAQ;QACpB,OAAO,WAAc,UAAU,SAAS,MAAM;IAChD;IAEA;;;;;GAKC,GACD,QAAQ,CACN,UACA,MACA;QAEA,OAAO,WAAc,UAAU,UAAU,MAAM;IACjD;AACF;AAGO,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,GAAG,EAAE,GAAG;uCAGlC", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/services/secure-cookies.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { cookies } from \"next/headers\";\r\n\r\nexport async function setSecureCookie(\r\n  name: string,\r\n  value: string,\r\n  maxAge?: number\r\n) {\r\n  const cookieStore = await cookies();\r\n\r\n  cookieStore.set(name, value, {\r\n    httpOnly: true,\r\n    secure: process.env.NODE_ENV === \"production\",\r\n    sameSite: \"strict\",\r\n    maxAge: maxAge || 7 * 24 * 60 * 60, // 7 days default\r\n    path: \"/\",\r\n  });\r\n}\r\n\r\nexport async function getSecureCookie(name: string) {\r\n  const cookieStore = await cookies();\r\n  return cookieStore.get(name)?.value;\r\n}\r\n\r\nexport async function clearSecureCookie(name: string) {\r\n  const cookieStore = await cookies();\r\n  cookieStore.delete(name);\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAEA;;;;;AAEO,eAAe,gBACpB,IAAY,EACZ,KAAa,EACb,MAAe;IAEf,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,YAAY,GAAG,CAAC,MAAM,OAAO;QAC3B,UAAU;QACV,QAAQ,oDAAyB;QACjC,UAAU;QACV,QAAQ,UAAU,IAAI,KAAK,KAAK;QAChC,MAAM;IACR;AACF;AAEO,eAAe,gBAAgB,IAAY;IAChD,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,OAAO,YAAY,GAAG,CAAC,OAAO;AAChC;AAEO,eAAe,kBAAkB,IAAY;IAClD,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,YAAY,MAAM,CAAC;AACrB;;;IAxBsB;IAgBA;IAKA;;AArBA,+OAAA;AAgBA,+OAAA;AAKA,+OAAA", "debugId": null}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/constants/auth.ts"], "sourcesContent": ["// Allowed countries for registration\r\nexport const ALLOWED_COUNTRIES = [\r\n  {\r\n    name_en: \"Egypt\",\r\n    name_ar: \"مصر\",\r\n    code: \"EG\",\r\n  },\r\n  {\r\n    name_en: \"UAE\",\r\n    name_ar: \"الإمارة العربية المتحدة\",\r\n    code: \"AE\",\r\n  },\r\n  {\r\n    name_en: \"Saudi Arabia\",\r\n    name_ar: \"المملكة العربية السعودية\",\r\n    code: \"SA\",\r\n  },\r\n  {\r\n    name_en: \"Jordan\",\r\n    name_ar: \"الأردن\",\r\n    code: \"JO\",\r\n  },\r\n] as const;\r\n\r\nexport type AllowedCountry = (typeof ALLOWED_COUNTRIES)[number];\r\n\r\n// Country validation function\r\nexport const isCountryAllowed = (\r\n  country: string\r\n): country is AllowedCountry[\"code\"] => {\r\n  return ALLOWED_COUNTRIES.some((c) => c.name_en === country) || false;\r\n};\r\n\r\n// Registration step validation\r\nexport const REGISTRATION_STEPS = {\r\n  PERSONAL_INFO: 1,\r\n  CONTACT_INFO: 2,\r\n  ADDITIONAL_INFO: 3,\r\n  VERIFICATION: 4,\r\n} as const;\r\n\r\n// Form validation patterns\r\nexport const VALIDATION_PATTERNS = {\r\n  email: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\r\n  password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$/,\r\n} as const;\r\n\r\n// Gender options\r\nexport const GENDER_OPTIONS = [\r\n  { gender: \"Male\", gender_ar: \"ذكر\" },\r\n  { gender: \"Female\", gender_ar: \"أنثى\" },\r\n] as const;\r\n"], "names": [], "mappings": "AAAA,qCAAqC;;;;;;;;AAC9B,MAAM,oBAAoB;IAC/B;QACE,SAAS;QACT,SAAS;QACT,MAAM;IACR;IACA;QACE,SAAS;QACT,SAAS;QACT,MAAM;IACR;IACA;QACE,SAAS;QACT,SAAS;QACT,MAAM;IACR;IACA;QACE,SAAS;QACT,SAAS;QACT,MAAM;IACR;CACD;AAKM,MAAM,mBAAmB,CAC9B;IAEA,OAAO,kBAAkB,IAAI,CAAC,CAAC,IAAM,EAAE,OAAO,KAAK,YAAY;AACjE;AAGO,MAAM,qBAAqB;IAChC,eAAe;IACf,cAAc;IACd,iBAAiB;IACjB,cAAc;AAChB;AAGO,MAAM,sBAAsB;IACjC,OAAO;IACP,UAAU;AACZ;AAGO,MAAM,iBAAiB;IAC5B;QAAE,QAAQ;QAAQ,WAAW;IAAM;IACnC;QAAE,QAAQ;QAAU,WAAW;IAAO;CACvC", "debugId": null}}, {"offset": {"line": 255, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/actions/auth.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { get, post } from \"@/services/api\";\r\nimport {\r\n  setSecure<PERSON>ookie,\r\n  clearSecure<PERSON><PERSON>ie,\r\n  getSecure<PERSON>ookie,\r\n} from \"@/services/secure-cookies\";\r\nimport { isCountryAllowed } from \"@/constants/auth\";\r\n\r\ninterface RegisterUser {\r\n  name: string;\r\n  email: string;\r\n  password: string;\r\n  phone: string;\r\n  smoker: boolean;\r\n  age: string;\r\n  gender: string;\r\n  nationality: string;\r\n  country: string;\r\n  occupation: string;\r\n}\r\n\r\ninterface LoginUser {\r\n  email: string;\r\n  password: string;\r\n}\r\n\r\nexport const registerUser = async (user: RegisterUser) => {\r\n  console.log(user);\r\n  // Validate country restriction before sending to API\r\n  if (!isCountryAllowed(user.country)) {\r\n    return {\r\n      error:\r\n        \"Registration is only available for Egypt, UAE, Saudi Arabia, and Jordannnn\",\r\n      status: 400,\r\n    };\r\n  }\r\n\r\n  const response = await post(\"/auth/register\", user);\r\n\r\n  console.log(\"response\", response);\r\n\r\n  if (response.data && response.data.access_token) {\r\n    // Store token in secure httpOnly cookie\r\n    await setSecureCookie(\"roommate-finder-token\", response.data.access_token);\r\n  }\r\n\r\n  return response;\r\n};\r\n\r\nexport const loginUser = async (user: LoginUser) => {\r\n  const response = await post(\"/auth/login\", user);\r\n\r\n  if (response.data && response.data.access_token) {\r\n    // Store token in secure httpOnly cookie\r\n    await setSecureCookie(\"roommate-finder-token\", response.data.access_token);\r\n  }\r\n\r\n  return response;\r\n};\r\n\r\nexport const logoutUser = async () => {\r\n  await clearSecureCookie(\"roommate-finder-token\");\r\n  return { success: true };\r\n};\r\n\r\nexport const getUser = async () => {\r\n  const response = await get(\"/users/me\");\r\n  return response;\r\n};\r\n\r\nexport const hasValidToken = async (): Promise<boolean> => {\r\n  const token = await getSecureCookie(\"roommate-finder-token\");\r\n  return !!token;\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAKA;;;;;;;AAoBO,MAAM,eAAe,OAAO;IACjC,QAAQ,GAAG,CAAC;IACZ,qDAAqD;IACrD,IAAI,CAAC,CAAA,GAAA,wHAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,OAAO,GAAG;QACnC,OAAO;YACL,OACE;YACF,QAAQ;QACV;IACF;IAEA,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,OAAI,AAAD,EAAE,kBAAkB;IAE9C,QAAQ,GAAG,CAAC,YAAY;IAExB,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,YAAY,EAAE;QAC/C,wCAAwC;QACxC,MAAM,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD,EAAE,yBAAyB,SAAS,IAAI,CAAC,YAAY;IAC3E;IAEA,OAAO;AACT;AAEO,MAAM,YAAY,OAAO;IAC9B,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,OAAI,AAAD,EAAE,eAAe;IAE3C,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,YAAY,EAAE;QAC/C,wCAAwC;QACxC,MAAM,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD,EAAE,yBAAyB,SAAS,IAAI,CAAC,YAAY;IAC3E;IAEA,OAAO;AACT;AAEO,MAAM,aAAa;IACxB,MAAM,CAAA,GAAA,oIAAA,CAAA,oBAAiB,AAAD,EAAE;IACxB,OAAO;QAAE,SAAS;IAAK;AACzB;AAEO,MAAM,UAAU;IACrB,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,MAAG,AAAD,EAAE;IAC3B,OAAO;AACT;AAEO,MAAM,gBAAgB;IAC3B,MAAM,QAAQ,MAAM,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD,EAAE;IACpC,OAAO,CAAC,CAAC;AACX;;;IA/Ca;IAuBA;IAWA;IAKA;IAKA;;AA5CA,+OAAA;AAuBA,+OAAA;AAWA,+OAAA;AAKA,+OAAA;AAKA,+OAAA", "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/actions/categories.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { post, get, patch, del } from \"@/services/api\";\r\n\r\ninterface Category {\r\n  id?: string;\r\n  name: string;\r\n  icon: string;\r\n}\r\n\r\nexport const createCategory = async (category: Category) => {\r\n  const response = await post(`/categories`, category);\r\n  return response;\r\n};\r\n\r\nexport const getCategories = async ({\r\n  search = \"\",\r\n  page = 1,\r\n}: {\r\n  search?: string;\r\n  page?: number;\r\n}) => {\r\n  const response = await get(`/categories?search=${search}&page=${page}`);\r\n  return response?.data;\r\n};\r\n\r\nexport const updateCategory = async (category: Category) => {\r\n  console.log(\"category\", category);\r\n  const response = await patch(`/categories/${category.id}`, {\r\n    name: category.name,\r\n    icon: category.icon,\r\n  });\r\n  console.log(\"response\", response);\r\n  return response;\r\n};\r\n\r\nexport const deleteCategory = async (id: string) => {\r\n  const response = await del(`/categories/${id}`);\r\n  return response;\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;AAEA;;;;;AAQO,MAAM,iBAAiB,OAAO;IACnC,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,OAAI,AAAD,EAAE,CAAC,WAAW,CAAC,EAAE;IAC3C,OAAO;AACT;AAEO,MAAM,gBAAgB,OAAO,EAClC,SAAS,EAAE,EACX,OAAO,CAAC,EAIT;IACC,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,MAAG,AAAD,EAAE,CAAC,mBAAmB,EAAE,OAAO,MAAM,EAAE,MAAM;IACtE,OAAO,UAAU;AACnB;AAEO,MAAM,iBAAiB,OAAO;IACnC,QAAQ,GAAG,CAAC,YAAY;IACxB,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,EAAE,EAAE;QACzD,MAAM,SAAS,IAAI;QACnB,MAAM,SAAS,IAAI;IACrB;IACA,QAAQ,GAAG,CAAC,YAAY;IACxB,OAAO;AACT;AAEO,MAAM,iBAAiB,OAAO;IACnC,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,MAAG,AAAD,EAAE,CAAC,YAAY,EAAE,IAAI;IAC9C,OAAO;AACT;;;IA7Ba;IAKA;IAWA;IAUA;;AA1BA,+OAAA;AAKA,+OAAA;AAWA,+OAAA;AAUA,+OAAA", "debugId": null}}, {"offset": {"line": 382, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/actions/upload.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { post } from \"@/services/api\";\r\n\r\nexport const uploadFile = async (file: File) => {\r\n  const formData = new FormData();\r\n  formData.append(\"file\", file);\r\n  const response = await post(\"/upload\", formData);\r\n  return response;\r\n};\r\n\r\nexport const uploadFiles = async (files: File[]) => {\r\n  const formData = new FormData();\r\n  files.forEach((file) => {\r\n    formData.append(\"files\", file);\r\n  });\r\n  const response = await post(\"/upload/multiple\", formData);\r\n  return response;\r\n};\r\n"], "names": [], "mappings": ";;;;;;AAEA;;;;;AAEO,MAAM,aAAa,OAAO;IAC/B,MAAM,WAAW,IAAI;IACrB,SAAS,MAAM,CAAC,QAAQ;IACxB,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,OAAI,AAAD,EAAE,WAAW;IACvC,OAAO;AACT;AAEO,MAAM,cAAc,OAAO;IAChC,MAAM,WAAW,IAAI;IACrB,MAAM,OAAO,CAAC,CAAC;QACb,SAAS,MAAM,CAAC,SAAS;IAC3B;IACA,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,OAAI,AAAD,EAAE,oBAAoB;IAChD,OAAO;AACT;;;IAda;IAOA;;AAPA,+OAAA;AAOA,+OAAA", "debugId": null}}, {"offset": {"line": 420, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/.next-internal/server/app/%5Blocale%5D/%28dashboard%29/dashboard/categories/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {getUser as '7f7ed2832f654977865887574304abff898a3508c9'} from 'ACTIONS_MODULE0'\nexport {hasValidToken as '7fc81d422d946583d67e965e01874ba827ddfb076a'} from 'ACTIONS_MODULE0'\nexport {logoutUser as '7ff5228fd53ce23c0ab89ce73ea7504be3fdad58ed'} from 'ACTIONS_MODULE0'\nexport {loginUser as '7f66a103c6ddae774301f8b2622c6ae732936859ae'} from 'ACTIONS_MODULE0'\nexport {registerUser as '7fce98afd99c671ec24efe38503d6078d69fdd6123'} from 'ACTIONS_MODULE0'\nexport {createCategory as '7f0596f7ef66a65528bf03142f5daf26e6963a615f'} from 'ACTIONS_MODULE1'\nexport {updateCategory as '7f65b84ea1277151fafed13bbb1d36bbc774724ce1'} from 'ACTIONS_MODULE1'\nexport {getCategories as '7f438d279e7d9390fb899045e3015546f4dc8abccc'} from 'ACTIONS_MODULE1'\nexport {deleteCategory as '7f4b4218cc6843a8973a50edee39b6e006ef1e2787'} from 'ACTIONS_MODULE1'\nexport {uploadFile as '7fd53009883b215fe6cf98c96fbab772157a540260'} from 'ACTIONS_MODULE2'\nexport {uploadFiles as '7f0f07b4d5f6fea01dce7a847de91cfb98e8cef590'} from 'ACTIONS_MODULE2'\n"], "names": [], "mappings": ";AAAA;AAKA;AAIA", "debugId": null}}, {"offset": {"line": 514, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/app/%5Blocale%5D/%28dashboard%29/dashboard/categories/components/CategoriesContent.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/[locale]/(dashboard)/dashboard/categories/components/CategoriesContent.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/[locale]/(dashboard)/dashboard/categories/components/CategoriesContent.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsV,GACnX,oHACA", "debugId": null}}, {"offset": {"line": 528, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/app/%5Blocale%5D/%28dashboard%29/dashboard/categories/components/CategoriesContent.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/[locale]/(dashboard)/dashboard/categories/components/CategoriesContent.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/[locale]/(dashboard)/dashboard/categories/components/CategoriesContent.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkU,GAC/V,gGACA", "debugId": null}}, {"offset": {"line": 542, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 552, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/app/%5Blocale%5D/%28dashboard%29/dashboard/categories/page.tsx"], "sourcesContent": ["import CategoriesContent from \"./components/CategoriesContent\";\r\n\r\nconst Categories = () => {\r\n    return <CategoriesContent />;\r\n}\r\n\r\nexport default Categories;"], "names": [], "mappings": ";;;;AAAA;;;AAEA,MAAM,aAAa;IACf,qBAAO,8OAAC,wMAAA,CAAA,UAAiB;;;;;AAC7B;uCAEe", "debugId": null}}]}
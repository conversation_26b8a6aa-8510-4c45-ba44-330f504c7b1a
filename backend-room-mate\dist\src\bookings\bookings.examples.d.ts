import { CreateBookingDto } from './dto/create-booking.dto';
import { UpdateBookingDto } from './dto/update-booking.dto';
import { QueryBookingsDto } from './dto/query-bookings.dto';
export declare const createBookingExample: CreateBookingDto;
export declare const openEndedBookingExample: CreateBookingDto;
export declare const shortTermBookingExample: CreateBookingDto;
export declare const updateBookingExample: UpdateBookingDto;
export declare const queryExamples: {
    getAllBookings: QueryBookingsDto;
    filterByStatus: QueryBookingsDto;
    filterByDateRange: QueryBookingsDto;
    filterByProperty: QueryBookingsDto;
    filterByUser: QueryBookingsDto;
    filterUpcoming: QueryBookingsDto;
    filterCurrent: QueryBookingsDto;
    complexFilter: QueryBookingsDto;
};
export declare const validationExamples: {
    validStatuses: string[];
    validDates: string[];
    validAmounts: string[];
    validUUIDs: string[];
    invalidExamples: ({
        offerId: string;
        startDate?: undefined;
        endDate?: undefined;
        totalAmount?: undefined;
    } | {
        startDate: string;
        offerId?: undefined;
        endDate?: undefined;
        totalAmount?: undefined;
    } | {
        endDate: string;
        offerId?: undefined;
        startDate?: undefined;
        totalAmount?: undefined;
    } | {
        totalAmount: string;
        offerId?: undefined;
        startDate?: undefined;
        endDate?: undefined;
    } | {
        startDate: string;
        endDate: string;
        offerId?: undefined;
        totalAmount?: undefined;
    })[];
};
export declare const apiEndpointExamples: {
    create: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
        body: CreateBookingDto;
    };
    getAll: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    getUserBookings: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    getPropertyBookings: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    filterByStatus: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    getUpcoming: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    getStats: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    getById: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    update: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
        body: UpdateBookingDto;
    };
    cancel: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    confirm: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    complete: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    delete: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
};
export declare const responseExamples: {
    bookingResponse: {
        id: string;
        startDate: string;
        endDate: string;
        totalAmount: string;
        depositPaid: boolean;
        status: string;
        paymentStatus: string;
        checkInDate: string;
        checkOutDate: null;
        createdAt: string;
        updatedAt: string;
        offer: {
            id: string;
            message: string;
            price: string;
            duration: string;
            property: {
                id: string;
                title: string;
                city: string;
                country: string;
                type: string;
                images: string[];
                user: {
                    id: string;
                    name: string;
                    email: string;
                    phone: string;
                };
            };
            user: {
                id: string;
                name: string;
                email: string;
                phone: string;
            };
        };
        notifications: {
            id: string;
            title: string;
            message: string;
            createdAt: string;
        }[];
    };
    paginatedResponse: {
        data: never[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };
    statsResponse: {
        totalBookings: number;
        confirmedBookings: number;
        pendingBookings: number;
        cancelledBookings: number;
        completedBookings: number;
        totalRevenue: number;
        avgBookingDuration: number;
        avgBookingAmount: number;
        bookingsByMonth: {
            '2024-01': number;
            '2024-02': number;
            '2024-03': number;
        };
        bookingsByStatus: {
            PENDING: number;
            CONFIRMED: number;
            CANCELLED: number;
            COMPLETED: number;
        };
        topProperties: {
            id: string;
            title: string;
            bookingCount: number;
            totalRevenue: number;
        }[];
    };
    createSuccessResponse: {
        success: boolean;
        message: string;
        data: {};
    };
    statusUpdateResponse: {
        success: boolean;
        message: string;
        data: {
            id: string;
            status: string;
            updatedAt: string;
        };
    };
};

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.responseExamples = exports.apiEndpointExamples = exports.queryPersonsExamples = exports.updatePersonExample = exports.luxurySeekingPersonExample = exports.minimalPersonExample = exports.createPersonExample = void 0;
exports.createPersonExample = {
    title: 'Young Professional Seeking Shared Accommodation in New Cairo',
    images: [
        'https://example.com/images/person1-profile.jpg',
        'https://example.com/images/person1-verification.jpg',
    ],
    city: 'Cairo',
    country: 'Egypt',
    address: 'New Cairo, Fifth Settlement preferred',
    description: 'I am a 25-year-old software engineer working in New Cairo, looking for a shared apartment or room. I am clean, respectful, and prefer a quiet environment. Non-smoker and work regular hours. Looking for furnished accommodation with good internet connection.',
    type: 'house',
    roomType: 'single',
    genderRequired: 'mixed',
    totalRooms: '2',
    availableRooms: '1',
    roomsToComplete: '1',
    price: '4000',
    size: '80',
    floor: '2',
    bathrooms: '1',
    separatedBathroom: true,
    residentsCount: '1',
    availablePersons: '1',
    rentTime: 'monthly',
    paymentTime: 'monthly',
    priceIncludeWaterAndElectricity: true,
    allowSmoking: false,
    includeFurniture: true,
    airConditioning: true,
    includeWaterHeater: true,
    parking: false,
    internet: true,
    nearToMetro: true,
    nearToMarket: true,
    elevator: true,
    trialPeriod: false,
    goodForForeigners: true,
    termsAndConditions: 'Prefer minimum 6-month stay. No pets. Quiet environment required.',
    categoryId: 'cat-uuid-123',
};
exports.minimalPersonExample = {
    title: 'Student Looking for Budget Accommodation in Maadi',
    city: 'Maadi',
    country: 'Egypt',
    price: '2500',
    categoryId: 'cat-uuid-456',
};
exports.luxurySeekingPersonExample = {
    title: 'Executive Seeking Premium Furnished Apartment in Zamalek',
    images: ['https://example.com/images/exec-profile.jpg'],
    description: 'Senior executive looking for a high-end furnished apartment in Zamalek area. Prefer modern amenities, gym access, and concierge services.',
    city: 'Zamalek',
    country: 'Egypt',
    type: 'house',
    roomType: 'single',
    genderRequired: 'mixed',
    price: '15000',
    rentTime: 'monthly',
    paymentTime: 'monthly',
    includeFurniture: true,
    airConditioning: true,
    parking: true,
    internet: true,
    elevator: true,
    goodForForeigners: true,
    categoryId: 'cat-uuid-789',
};
exports.updatePersonExample = {
    title: 'Updated: Young Professional Seeking Premium Shared Accommodation',
    price: '4500',
    description: 'Updated preferences: Now looking for premium accommodation with additional amenities and flexible lease terms.',
    airConditioning: true,
    parking: true,
    images: [
        'https://example.com/images/person1-updated-profile.jpg',
        'https://example.com/images/person1-updated-verification.jpg',
    ],
};
exports.queryPersonsExamples = {
    getAllPersons: {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc',
    },
    searchPersons: {
        search: 'engineer cairo',
        page: 1,
        limit: 20,
        sortBy: 'price',
        sortOrder: 'asc',
    },
    filterByTitle: {
        title: 'professional',
        page: 1,
        limit: 10,
    },
    filterBySlug: {
        slug: 'young-professional-seeking-shared-accommodation-new-cairo',
        page: 1,
        limit: 1,
    },
    filterByLocation: {
        city: 'Cairo',
        country: 'Egypt',
        neighborhood: 'New Cairo',
        page: 1,
        limit: 15,
    },
    filterByAddress: {
        address: 'Fifth Settlement',
        page: 1,
        limit: 10,
    },
    filterByCoordinates: {
        latitude: '30.0444',
        longitude: '31.2357',
        page: 1,
        limit: 5,
    },
    filterByType: {
        type: 'house',
        roomType: 'single',
        page: 1,
        limit: 10,
    },
    filterByGender: {
        genderRequired: 'mixed',
        page: 1,
        limit: 10,
    },
    filterByRoomSpecs: {
        totalRooms: '2',
        availableRooms: '1',
        bathrooms: '1',
        separatedBathroom: true,
        page: 1,
        limit: 10,
    },
    filterBySpaceSpecs: {
        size: '80',
        floor: '2',
        residentsCount: '1',
        availablePersons: '1',
        page: 1,
        limit: 10,
    },
    filterByBudget: {
        minPrice: '3000',
        maxPrice: '8000',
        page: 1,
        limit: 10,
        sortBy: 'price',
        sortOrder: 'asc',
    },
    filterByTerms: {
        rentTime: 'monthly',
        paymentTime: 'monthly',
        priceIncludeWaterAndElectricity: true,
        page: 1,
        limit: 10,
    },
    filterByCategory: {
        categoryId: 'cat-uuid-123',
        page: 1,
        limit: 10,
    },
    filterByOwner: {
        ownerId: 'user-uuid-456',
        page: 1,
        limit: 10,
    },
    filterByRating: {
        minRating: 4.0,
        minTotalRatings: 5,
        page: 1,
        limit: 10,
        sortBy: 'rating',
        sortOrder: 'desc',
    },
    filterByDateRange: {
        createdAfter: '2024-01-01T00:00:00.000Z',
        createdBefore: '2024-12-31T23:59:59.999Z',
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc',
    },
    filterByRecentUpdates: {
        updatedAfter: '2024-12-01T00:00:00.000Z',
        page: 1,
        limit: 10,
        sortBy: 'updatedAt',
        sortOrder: 'desc',
    },
    filterByStatus: {
        isVerified: true,
        isAvailable: true,
        page: 1,
        limit: 10,
        sortBy: 'rating',
        sortOrder: 'desc',
    },
    filterByAccommodationPrefs: {
        allowSmoking: false,
        trialPeriod: true,
        goodForForeigners: true,
        page: 1,
        limit: 10,
    },
    filterByAmenities: {
        airConditioning: true,
        parking: true,
        internet: true,
        includeFurniture: true,
        includeWaterHeater: true,
        page: 1,
        limit: 10,
    },
    filterByLocationAmenities: {
        nearToMetro: true,
        nearToMarket: true,
        elevator: true,
        page: 1,
        limit: 10,
    },
    complexFilter: {
        search: 'professional furnished',
        city: 'Cairo',
        type: 'house',
        roomType: 'single',
        minPrice: '4000',
        maxPrice: '10000',
        totalRooms: '2',
        separatedBathroom: true,
        airConditioning: true,
        includeFurniture: true,
        internet: true,
        nearToMetro: true,
        goodForForeigners: true,
        isVerified: true,
        isAvailable: true,
        minRating: 3.5,
        page: 1,
        limit: 5,
        sortBy: 'rating',
        sortOrder: 'desc',
    },
    premiumFilter: {
        minPrice: '8000',
        includeFurniture: true,
        airConditioning: true,
        parking: true,
        elevator: true,
        separatedBathroom: true,
        isVerified: true,
        minRating: 4.0,
        page: 1,
        limit: 10,
        sortBy: 'price',
        sortOrder: 'desc',
    },
    budgetFilter: {
        maxPrice: '3000',
        allowSmoking: false,
        goodForForeigners: true,
        isAvailable: true,
        page: 1,
        limit: 10,
        sortBy: 'price',
        sortOrder: 'asc',
    },
    studentFilter: {
        maxPrice: '4000',
        goodForForeigners: true,
        internet: true,
        nearToMetro: true,
        nearToMarket: true,
        trialPeriod: true,
        isAvailable: true,
        page: 1,
        limit: 15,
        sortBy: 'price',
        sortOrder: 'asc',
    },
    professionalFilter: {
        minPrice: '5000',
        type: 'house',
        includeFurniture: true,
        airConditioning: true,
        internet: true,
        parking: true,
        separatedBathroom: true,
        goodForForeigners: true,
        isVerified: true,
        page: 1,
        limit: 10,
        sortBy: 'rating',
        sortOrder: 'desc',
    },
};
exports.apiEndpointExamples = {
    create: {
        method: 'POST',
        url: '/api/persons',
        headers: { Authorization: 'Bearer user-jwt-token' },
        body: exports.createPersonExample,
    },
    getAll: {
        method: 'GET',
        url: '/api/persons?page=1&limit=10',
    },
    search: {
        method: 'GET',
        url: '/api/persons?search=engineer&city=Cairo&minPrice=3000&maxPrice=8000',
    },
    getUserPersons: {
        method: 'GET',
        url: '/api/persons/my-persons',
        headers: { Authorization: 'Bearer user-jwt-token' },
    },
    getById: {
        method: 'GET',
        url: '/api/persons/person-uuid-123',
    },
    getBySlug: {
        method: 'GET',
        url: '/api/persons/slug/young-professional-seeking-accommodation',
    },
    update: {
        method: 'PATCH',
        url: '/api/persons/person-uuid-123',
        headers: { Authorization: 'Bearer user-jwt-token' },
        body: exports.updatePersonExample,
    },
    delete: {
        method: 'DELETE',
        url: '/api/persons/person-uuid-123',
        headers: { Authorization: 'Bearer user-jwt-token' },
    },
    toggleFavorite: {
        method: 'PATCH',
        url: '/api/persons/person-uuid-123/toggle-favorite',
        headers: { Authorization: 'Bearer user-jwt-token' },
    },
    getFavorites: {
        method: 'GET',
        url: '/api/persons/favorites',
        headers: { Authorization: 'Bearer user-jwt-token' },
    },
};
exports.responseExamples = {
    personResponse: {
        id: 'person-uuid-123',
        title: 'Young Professional Seeking Shared Accommodation in New Cairo',
        slug: 'young-professional-seeking-accommodation',
        images: [
            'https://example.com/images/person1-profile.jpg',
            'https://example.com/images/person1-verification.jpg',
        ],
        city: 'Cairo',
        country: 'Egypt',
        address: 'New Cairo, Fifth Settlement preferred',
        description: 'I am a 25-year-old software engineer...',
        type: 'HOUSE',
        roomType: 'SINGLE',
        genderRequired: 'mixed',
        totalRooms: '2',
        availableRooms: '1',
        price: '4000',
        size: '80',
        floor: '2',
        bathrooms: '1',
        separatedBathroom: true,
        residentsCount: '1',
        availablePersons: '1',
        rentTime: 'MONTHLY',
        paymentTime: 'MONTHLY',
        priceIncludeWaterAndElectricity: true,
        allowSmoking: false,
        includeFurniture: true,
        airConditioning: true,
        parking: false,
        internet: true,
        nearToMetro: true,
        goodForForeigners: true,
        rating: 4.5,
        totalRatings: 12,
        isVerified: true,
        isAvailable: true,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-15T10:30:00.000Z',
        owner: {
            id: 'user-uuid-456',
            name: 'Ahmed Hassan',
            email: '<EMAIL>',
            phone: '+************',
        },
        category: {
            id: 'cat-uuid-123',
            name: 'Accommodation Seekers',
            icon: 'person-icon.svg',
        },
        _count: {
            favorites: 8,
        },
    },
    paginatedResponse: {
        success: true,
        data: {
            persons: [],
            pagination: {
                page: 1,
                limit: 10,
                total: 45,
                totalPages: 5,
                hasNext: true,
                hasPrev: false,
            },
        },
    },
    toggleFavoriteResponse: {
        success: true,
        data: { isFavorited: true },
        message: 'Added to favorites',
    },
};
//# sourceMappingURL=persons.examples.js.map
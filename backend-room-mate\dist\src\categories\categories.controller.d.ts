import { CategoriesService } from './categories.service';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { QueryCategoriesDto } from './dto/query-categories.dto';
export declare class CategoriesController {
    private readonly categoriesService;
    constructor(categoriesService: CategoriesService);
    create(createCategoryDto: CreateCategoryDto): Promise<{
        success: boolean;
        message: string;
        data: {
            _count: {
                properties: number;
                Person: number;
            };
        } & {
            id: string;
            name: string;
            createdAt: Date;
            updatedAt: Date;
            icon: string | null;
        };
    }>;
    findAll(query: QueryCategoriesDto): Promise<{
        success: boolean;
        data: ({
            _count: {
                properties: number;
                Person: number;
            };
        } & {
            id: string;
            name: string;
            createdAt: Date;
            updatedAt: Date;
            icon: string | null;
        })[];
        pagination: {
            total: number;
            page: number;
            limit: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
    }>;
    getStats(): Promise<{
        success: boolean;
        data: {
            totalCategories: number;
            totalProperties: number;
            totalPersons: number;
            categories: {
                id: string;
                name: string;
                _count: {
                    properties: number;
                    Person: number;
                };
            }[];
        };
    }>;
    findOne(id: string): Promise<{
        success: boolean;
        data: {
            _count: {
                properties: number;
                Person: number;
            };
        } & {
            id: string;
            name: string;
            createdAt: Date;
            updatedAt: Date;
            icon: string | null;
        };
    }>;
    update(id: string, updateCategoryDto: UpdateCategoryDto): Promise<{
        success: boolean;
        message: string;
        data: {
            _count: {
                properties: number;
                Person: number;
            };
        } & {
            id: string;
            name: string;
            createdAt: Date;
            updatedAt: Date;
            icon: string | null;
        };
    }>;
    remove(id: string): Promise<{
        success: boolean;
        message: string;
    }>;
}

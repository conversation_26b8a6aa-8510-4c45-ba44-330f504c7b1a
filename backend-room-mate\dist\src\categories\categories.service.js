"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CategoriesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
const client_1 = require("@prisma/client");
let CategoriesService = class CategoriesService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createCategoryDto) {
        try {
            const category = await this.prisma.category.create({
                data: createCategoryDto,
                include: {
                    _count: {
                        select: {
                            properties: true,
                            Person: true,
                        },
                    },
                },
            });
            return {
                success: true,
                message: 'Category created successfully',
                data: category,
            };
        }
        catch (error) {
            if (error instanceof client_1.Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2002') {
                    throw new common_1.ConflictException('Category name already exists');
                }
            }
            throw new common_1.BadRequestException('Failed to create category');
        }
    }
    async findAll(query) {
        const { search, page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc', } = query;
        const skip = (page - 1) * limit;
        const where = {};
        if (search) {
            where.name = {
                contains: search,
                mode: 'insensitive',
            };
        }
        const orderBy = {};
        orderBy[sortBy] = sortOrder;
        const [categories, total] = await Promise.all([
            this.prisma.category.findMany({
                where,
                skip,
                take: limit,
                orderBy,
                include: {
                    _count: {
                        select: {
                            properties: true,
                            Person: true,
                        },
                    },
                },
            }),
            this.prisma.category.count({ where }),
        ]);
        const totalPages = Math.ceil(total / limit);
        return {
            success: true,
            data: categories,
            pagination: {
                total,
                page,
                limit,
                totalPages,
                hasNext: page < totalPages,
                hasPrev: page > 1,
            },
        };
    }
    async findOne(id) {
        const category = await this.prisma.category.findUnique({
            where: { id },
            include: {
                _count: {
                    select: {
                        properties: true,
                        Person: true,
                    },
                },
            },
        });
        if (!category) {
            throw new common_1.NotFoundException('Category not found');
        }
        return {
            success: true,
            data: category,
        };
    }
    async update(id, updateCategoryDto) {
        console.log(updateCategoryDto);
        try {
            const category = await this.prisma.category.update({
                where: { id },
                data: updateCategoryDto,
                include: {
                    _count: {
                        select: {
                            properties: true,
                            Person: true,
                        },
                    },
                },
            });
            return {
                success: true,
                message: 'Category updated successfully',
                data: category,
            };
        }
        catch (error) {
            if (error instanceof client_1.Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2002') {
                    throw new common_1.ConflictException('Category name already exists');
                }
                if (error.code === 'P2025') {
                    throw new common_1.NotFoundException('Category not found');
                }
            }
            throw new common_1.BadRequestException('Failed to update category');
        }
    }
    async remove(id) {
        try {
            const category = await this.prisma.category.findUnique({
                where: { id },
                include: {
                    _count: {
                        select: {
                            properties: true,
                            Person: true,
                        },
                    },
                },
            });
            if (!category) {
                throw new common_1.NotFoundException('Category not found');
            }
            if ((category._count?.properties || 0) > 0 ||
                (category._count?.Person || 0) > 0) {
                throw new common_1.ConflictException('Cannot delete category with associated properties or persons');
            }
            await this.prisma.category.delete({
                where: { id },
            });
            return {
                success: true,
                message: 'Category deleted successfully',
            };
        }
        catch (error) {
            if (error instanceof client_1.Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2025') {
                    throw new common_1.NotFoundException('Category not found');
                }
            }
            throw error;
        }
    }
    async getCategoryStats() {
        const stats = await this.prisma.category.findMany({
            select: {
                id: true,
                name: true,
                _count: {
                    select: {
                        properties: true,
                        Person: true,
                    },
                },
            },
            orderBy: {
                createdAt: 'desc',
            },
        });
        const totalCategories = stats.length;
        const totalProperties = stats.reduce((sum, category) => sum + (category._count?.properties || 0), 0);
        const totalPersons = stats.reduce((sum, category) => sum + (category._count?.Person || 0), 0);
        return {
            success: true,
            data: {
                totalCategories,
                totalProperties,
                totalPersons,
                categories: stats,
            },
        };
    }
};
exports.CategoriesService = CategoriesService;
exports.CategoriesService = CategoriesService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], CategoriesService);
//# sourceMappingURL=categories.service.js.map
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VerificationController = void 0;
const common_1 = require("@nestjs/common");
const verification_service_1 = require("./verification.service");
const create_verification_request_dto_1 = require("./dto/create-verification-request.dto");
const query_verification_requests_dto_1 = require("./dto/query-verification-requests.dto");
const query_verifications_dto_1 = require("./dto/query-verifications.dto");
const update_verification_dto_1 = require("./dto/update-verification.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../users/guards/roles.guard");
const roles_decorator_1 = require("../users/decorators/roles.decorator");
const roles_guard_2 = require("../users/guards/roles.guard");
const current_user_decorator_1 = require("../users/decorators/current-user.decorator");
let VerificationController = class VerificationController {
    verificationService;
    constructor(verificationService) {
        this.verificationService = verificationService;
    }
    createVerificationRequest(createVerificationRequestDto, user) {
        return this.verificationService.createVerificationRequest(createVerificationRequestDto, user);
    }
    findAllVerificationRequests(query, user) {
        return this.verificationService.findAllVerificationRequests(query, user);
    }
    findMyVerificationRequests(user) {
        return this.verificationService.findUserVerificationRequests(user);
    }
    findOneVerificationRequest(id, user) {
        return this.verificationService.findOneVerificationRequest(id, user);
    }
    deleteVerificationRequest(id, user) {
        return this.verificationService.deleteVerificationRequest(id, user);
    }
    processVerificationRequest(requestId, updateVerificationDto, user) {
        return this.verificationService.processVerificationRequest(requestId, updateVerificationDto, user);
    }
    findAllVerifications(query, user) {
        return this.verificationService.findAllVerifications(query, user);
    }
    findMyVerifications(user) {
        return this.verificationService.findUserVerifications(user);
    }
    findOneVerification(id, user) {
        return this.verificationService.findOneVerification(id, user);
    }
    revokeVerification(id, user) {
        return this.verificationService.revokeVerification(id, user);
    }
    getVerificationStats() {
        return this.verificationService.getVerificationStats();
    }
};
exports.VerificationController = VerificationController;
__decorate([
    (0, common_1.Post)('requests'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_verification_request_dto_1.CreateVerificationRequestDto, Object]),
    __metadata("design:returntype", void 0)
], VerificationController.prototype, "createVerificationRequest", null);
__decorate([
    (0, common_1.Get)('requests'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(roles_guard_2.Role.ADMIN),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [query_verification_requests_dto_1.QueryVerificationRequestsDto, Object]),
    __metadata("design:returntype", void 0)
], VerificationController.prototype, "findAllVerificationRequests", null);
__decorate([
    (0, common_1.Get)('requests/me'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], VerificationController.prototype, "findMyVerificationRequests", null);
__decorate([
    (0, common_1.Get)('requests/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], VerificationController.prototype, "findOneVerificationRequest", null);
__decorate([
    (0, common_1.Delete)('requests/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], VerificationController.prototype, "deleteVerificationRequest", null);
__decorate([
    (0, common_1.Post)('requests/:id/process'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(roles_guard_2.Role.ADMIN),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_verification_dto_1.UpdateVerificationDto, Object]),
    __metadata("design:returntype", void 0)
], VerificationController.prototype, "processVerificationRequest", null);
__decorate([
    (0, common_1.Get)('verifications'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(roles_guard_2.Role.ADMIN),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [query_verifications_dto_1.QueryVerificationsDto, Object]),
    __metadata("design:returntype", void 0)
], VerificationController.prototype, "findAllVerifications", null);
__decorate([
    (0, common_1.Get)('verifications/me'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], VerificationController.prototype, "findMyVerifications", null);
__decorate([
    (0, common_1.Get)('verifications/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], VerificationController.prototype, "findOneVerification", null);
__decorate([
    (0, common_1.Patch)('verifications/:id/revoke'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(roles_guard_2.Role.ADMIN),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], VerificationController.prototype, "revokeVerification", null);
__decorate([
    (0, common_1.Get)('stats'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(roles_guard_2.Role.ADMIN),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], VerificationController.prototype, "getVerificationStats", null);
exports.VerificationController = VerificationController = __decorate([
    (0, common_1.Controller)('verification'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [verification_service_1.VerificationService])
], VerificationController);
//# sourceMappingURL=verification.controller.js.map
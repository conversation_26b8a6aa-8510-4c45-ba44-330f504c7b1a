import { ConfigService } from '@nestjs/config';
import { UploadResponseDto } from './upload.dto';
interface UploadedFile {
    filename: string;
    originalname: string;
    mimetype: string;
    size: number;
}
export declare class UploadService {
    private configService;
    constructor(configService: ConfigService);
    private getBaseUrl;
    generateFilename(originalName: string): string;
    processUploadedFile(file: UploadedFile): UploadResponseDto;
    processMultipleUploadedFiles(files: UploadedFile[]): UploadResponseDto[];
    validateImageFile(file: UploadedFile): boolean;
}
export {};

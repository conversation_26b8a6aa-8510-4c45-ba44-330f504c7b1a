import { VerificationStatus } from '@prisma/client';
export interface VerificationRequestResponse {
    id: string;
    images: string[];
    userId: string;
    propertyId: string | null;
    user: {
        id: string;
        name: string;
        email: string;
    };
    property: {
        id: string;
        title: string | null;
        city: string | null;
        country: string | null;
    } | null;
    createdAt: Date;
    updatedAt: Date;
}
export interface VerificationResponse {
    id: string;
    status: VerificationStatus;
    userId: string | null;
    propertyId: string | null;
    personId: string | null;
    user: {
        id: string;
        name: string;
        email: string;
    } | null;
    property: {
        id: string;
        title: string | null;
        city: string | null;
        country: string | null;
    } | null;
    person: {
        id: string;
        title: string | null;
        city: string | null;
        country: string | null;
    } | null;
    createdAt: Date;
    updatedAt: Date;
}
export interface VerificationRequestsQueryResult {
    verificationRequests: VerificationRequestResponse[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };
}
export interface VerificationsQueryResult {
    verifications: VerificationResponse[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };
}
export interface UserPayload {
    sub: string;
    email: string;
    isAdmin: boolean;
    isVerified: boolean;
}

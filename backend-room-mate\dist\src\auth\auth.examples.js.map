{"version": 3, "file": "auth.examples.js", "sourceRoot": "", "sources": ["../../../src/auth/auth.examples.ts"], "names": [], "mappings": ";;;AASa,QAAA,YAAY,GAAa;IACpC,KAAK,EAAE,sBAAsB;IAC7B,QAAQ,EAAE,iBAAiB;CAC5B,CAAC;AAGW,QAAA,eAAe,GAAgB;IAC1C,IAAI,EAAE,UAAU;IAChB,KAAK,EAAE,sBAAsB;IAC7B,QAAQ,EAAE,iBAAiB;IAC3B,KAAK,EAAE,aAAa;IACpB,MAAM,EAAE,KAAK;IACb,GAAG,EAAE,IAAI;IACT,MAAM,EAAE,MAAM;IACd,WAAW,EAAE,UAAU;IACvB,UAAU,EAAE,mBAAmB;CAChC,CAAC;AAGW,QAAA,sBAAsB,GAAgB;IACjD,IAAI,EAAE,YAAY;IAClB,KAAK,EAAE,wBAAwB;IAC/B,QAAQ,EAAE,mBAAmB;CAC9B,CAAC;AAGW,QAAA,qBAAqB,GAAG;IACnC,IAAI,EAAE,cAAc;IACpB,KAAK,EAAE,wBAAwB;IAC/B,QAAQ,EAAE,uBAAuB;IACjC,MAAM,EAAE,gDAAgD;CACzD,CAAC;AAGW,QAAA,kBAAkB,GAAG;IAEhC,WAAW,EAAE;QACX,kBAAkB;QAClB,6BAA6B;QAC7B,qBAAqB;KACtB;IAGD,cAAc,EAAE,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,qBAAqB,CAAC;IAG7E,UAAU,EAAE,CAAC,UAAU,EAAE,iBAAiB,EAAE,cAAc,EAAE,KAAK,CAAC;IAGlE,WAAW,EAAE,CAAC,aAAa,EAAE,eAAe,EAAE,kBAAkB,CAAC;IAGjE,eAAe,EAAE;QAEf,EAAE,KAAK,EAAE,eAAe,EAAE;QAC1B,EAAE,KAAK,EAAE,OAAO,EAAE;QAClB,EAAE,KAAK,EAAE,aAAa,EAAE;QAGxB,EAAE,QAAQ,EAAE,QAAQ,EAAE;QACtB,EAAE,QAAQ,EAAE,UAAU,EAAE;QACxB,EAAE,QAAQ,EAAE,aAAa,EAAE;QAG3B,EAAE,IAAI,EAAE,GAAG,EAAE;QACb,EAAE,IAAI,EAAE,EAAE,EAAE;QAGZ,EAAE,KAAK,EAAE,KAAK,EAAE;QAChB,EAAE,KAAK,EAAE,eAAe,EAAE;KAC3B;CACF,CAAC;AAGW,QAAA,mBAAmB,GAAG;IAEjC,QAAQ,EAAE;QACR,MAAM,EAAE,MAAM;QACd,GAAG,EAAE,oBAAoB;QACzB,IAAI,EAAE,uBAAe;KACtB;IAGD,KAAK,EAAE;QACL,MAAM,EAAE,MAAM;QACd,GAAG,EAAE,iBAAiB;QACtB,IAAI,EAAE,oBAAY;KACnB;IAGD,UAAU,EAAE;QACV,MAAM,EAAE,KAAK;QACb,GAAG,EAAE,kBAAkB;KACxB;IAGD,cAAc,EAAE;QACd,MAAM,EAAE,KAAK;QACb,GAAG,EAAE,2BAA2B;KACjC;IAGD,MAAM,EAAE;QACN,MAAM,EAAE,MAAM;QACd,GAAG,EAAE,kBAAkB;QACvB,OAAO,EAAE,EAAE,aAAa,EAAE,uBAAuB,EAAE;KACpD;IAGD,OAAO,EAAE;QACP,MAAM,EAAE,MAAM;QACd,GAAG,EAAE,mBAAmB;QACxB,OAAO,EAAE,EAAE,aAAa,EAAE,2BAA2B,EAAE;KACxD;IAGD,UAAU,EAAE;QACV,MAAM,EAAE,KAAK;QACb,GAAG,EAAE,mBAAmB;QACxB,OAAO,EAAE,EAAE,aAAa,EAAE,uBAAuB,EAAE;KACpD;CACF,CAAC;AAGW,QAAA,gBAAgB,GAAG;IAE9B,aAAa,EAAE;QACb,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,kBAAkB;QAC3B,IAAI,EAAE;YACJ,IAAI,EAAE;gBACJ,EAAE,EAAE,eAAe;gBACnB,IAAI,EAAE,UAAU;gBAChB,KAAK,EAAE,sBAAsB;gBAC7B,KAAK,EAAE,aAAa;gBACpB,MAAM,EAAE,KAAK;gBACb,GAAG,EAAE,IAAI;gBACT,MAAM,EAAE,MAAM;gBACd,WAAW,EAAE,UAAU;gBACvB,UAAU,EAAE,mBAAmB;gBAC/B,MAAM,EAAE,IAAI;gBACZ,aAAa,EAAE,IAAI;gBACnB,SAAS,EAAE,0BAA0B;gBACrC,SAAS,EAAE,0BAA0B;aACtC;YACD,MAAM,EAAE;gBACN,WAAW,EAAE,yCAAyC;gBACtD,YAAY,EAAE,yCAAyC;gBACvD,SAAS,EAAE,IAAI;aAChB;SACF;KACF;IAGD,gBAAgB,EAAE;QAChB,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,yBAAyB;QAClC,IAAI,EAAE;YACJ,IAAI,EAAE;gBACJ,EAAE,EAAE,eAAe;gBACnB,IAAI,EAAE,YAAY;gBAClB,KAAK,EAAE,wBAAwB;gBAC/B,aAAa,EAAE,KAAK;gBACpB,SAAS,EAAE,0BAA0B;aACtC;YACD,MAAM,EAAE;gBACN,WAAW,EAAE,yCAAyC;gBACtD,YAAY,EAAE,yCAAyC;gBACvD,SAAS,EAAE,IAAI;aAChB;SACF;KACF;IAGD,cAAc,EAAE;QACd,kBAAkB,EAAE;YAClB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,2BAA2B;YACpC,UAAU,EAAE,GAAG;SAChB;QACD,iBAAiB,EAAE;YACjB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,qCAAqC;YAC9C,UAAU,EAAE,GAAG;SAChB;QACD,eAAe,EAAE;YACf,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,mBAAmB;YAC5B,MAAM,EAAE;gBACN,kHAAkH;aACnH;YACD,UAAU,EAAE,GAAG;SAChB;KACF;CACF,CAAC;AAGW,QAAA,WAAW,GAAG;IAEzB,kBAAkB,EAAE;QAClB,GAAG,EAAE,eAAe;QACpB,KAAK,EAAE,sBAAsB;QAC7B,IAAI,EAAE,UAAU;QAChB,IAAI,EAAE,MAAM;QACZ,GAAG,EAAE,UAAU;QACf,GAAG,EAAE,UAAU;KAChB;IAGD,mBAAmB,EAAE;QACnB,GAAG,EAAE,eAAe;QACpB,IAAI,EAAE,SAAS;QACf,GAAG,EAAE,UAAU;QACf,GAAG,EAAE,UAAU;KAChB;CACF,CAAC"}
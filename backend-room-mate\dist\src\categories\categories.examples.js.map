{"version": 3, "file": "categories.examples.js", "sourceRoot": "", "sources": ["../../../src/categories/categories.examples.ts"], "names": [], "mappings": ";;;AAUa,QAAA,qBAAqB,GAAsB;IACtD,IAAI,EAAE,YAAY;IAClB,IAAI,EAAE,oBAAoB;CAC3B,CAAC;AAGW,QAAA,qBAAqB,GAAsB;IACtD,IAAI,EAAE,iBAAiB;CACxB,CAAC;AAGW,QAAA,qBAAqB,GAAsB;IACtD,IAAI,EAAE,mBAAmB;IACzB,IAAI,EAAE,2BAA2B;CAClC,CAAC;AAGW,QAAA,aAAa,GAAG;IAC3B,gBAAgB,EAAE;QAChB,IAAI,EAAE,CAAC;QACP,KAAK,EAAE,EAAE;QACT,MAAM,EAAE,MAAe;QACvB,SAAS,EAAE,KAAc;KACJ;IAEvB,gBAAgB,EAAE;QAChB,MAAM,EAAE,WAAW;QACnB,IAAI,EAAE,CAAC;QACP,KAAK,EAAE,CAAC;KACa;CACxB,CAAC;AAGW,QAAA,mBAAmB,GAAG;IACjC,MAAM,EAAE;QACN,MAAM,EAAE,MAAM;QACd,GAAG,EAAE,iBAAiB;QACtB,OAAO,EAAE,EAAE,aAAa,EAAE,wBAAwB,EAAE;QACpD,IAAI,EAAE,6BAAqB;KAC5B;IACD,MAAM,EAAE;QACN,MAAM,EAAE,KAAK;QACb,GAAG,EAAE,iBAAiB;KACvB;CACF,CAAC;AAGW,QAAA,gBAAgB,GAAG;IAC9B,gBAAgB,EAAE;QAChB,EAAE,EAAE,cAAc;QAClB,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE,oBAAoB;QAC1B,SAAS,EAAE,0BAA0B;QACrC,SAAS,EAAE,0BAA0B;QACrC,UAAU,EAAE;YACV;gBACE,EAAE,EAAE,mBAAmB;gBACvB,KAAK,EAAE,8BAA8B;gBACrC,KAAK,EAAE,MAAM;gBACb,IAAI,EAAE,OAAO;aACd;SACF;KACF;CACF,CAAC"}
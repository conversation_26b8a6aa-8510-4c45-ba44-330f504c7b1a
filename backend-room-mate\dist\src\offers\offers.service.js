"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OffersService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
let OffersService = class OffersService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createOfferDto, user) {
        const { propertyId, personId, ...offerData } = createOfferDto;
        if (!propertyId && !personId) {
            throw new common_1.BadRequestException('Either propertyId or personId is required');
        }
        if (propertyId && personId) {
            throw new common_1.BadRequestException('Cannot specify both propertyId and personId');
        }
        let targetOwner;
        if (propertyId) {
            const property = await this.prisma.property.findUnique({
                where: { id: propertyId },
                select: {
                    id: true,
                    title: true,
                    isAvailable: true,
                    ownerId: true,
                },
            });
            if (!property) {
                throw new common_1.NotFoundException('Property not found');
            }
            if (!property.isAvailable) {
                throw new common_1.BadRequestException('Property is not available for offers');
            }
            targetOwner = property.ownerId;
        }
        else {
            const person = await this.prisma.person.findUnique({
                where: { id: personId },
                select: {
                    id: true,
                    title: true,
                    isAvailable: true,
                    ownerId: true,
                },
            });
            if (!person) {
                throw new common_1.NotFoundException('Person not found');
            }
            if (!person.isAvailable) {
                throw new common_1.BadRequestException('Person is not available for offers');
            }
            targetOwner = person.ownerId;
        }
        if (targetOwner === user.sub) {
            throw new common_1.ForbiddenException('You cannot make offers on your own listing');
        }
        const existingOffer = await this.prisma.offer.findFirst({
            where: {
                userId: user.sub,
                ...(propertyId ? { propertyId } : { personId }),
                status: 'pending',
            },
        });
        if (existingOffer) {
            throw new common_1.ConflictException(`You already have a pending offer for this ${propertyId ? 'property' : 'person'}`);
        }
        const offer = await this.prisma.offer.create({
            data: {
                ...offerData,
                ...(propertyId ? { propertyId } : { personId }),
                userId: user.sub,
            },
            include: {
                property: propertyId
                    ? {
                        select: {
                            id: true,
                            title: true,
                            city: true,
                            country: true,
                            price: true,
                            owner: {
                                select: {
                                    id: true,
                                    name: true,
                                    email: true,
                                },
                            },
                        },
                    }
                    : undefined,
                person: personId
                    ? {
                        select: {
                            id: true,
                            title: true,
                            city: true,
                            country: true,
                            price: true,
                            owner: {
                                select: {
                                    id: true,
                                    name: true,
                                    email: true,
                                },
                            },
                        },
                    }
                    : undefined,
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                    },
                },
            },
        });
        return {
            success: true,
            data: offer,
            message: 'Offer created successfully',
        };
    }
    async findAll(query, user) {
        const { page = 1, limit = 10, search, status, propertyId, personId, userId, deposit, sortBy = 'createdAt', sortOrder = 'desc', } = query;
        const offset = (page - 1) * limit;
        const where = {};
        if (user && !user.isAdmin) {
            where.OR = [
                { userId: user.sub },
                {
                    property: {
                        ownerId: user.sub,
                    },
                },
                {
                    person: {
                        ownerId: user.sub,
                    },
                },
            ];
        }
        if (search) {
            const searchConditions = [
                { message: { contains: search, mode: 'insensitive' } },
                { phone: { contains: search, mode: 'insensitive' } },
                {
                    property: {
                        title: { contains: search, mode: 'insensitive' },
                    },
                },
                {
                    person: {
                        title: { contains: search, mode: 'insensitive' },
                    },
                },
            ];
            if (where.OR) {
                where.AND = [{ OR: where.OR }, { OR: searchConditions }];
                delete where.OR;
            }
            else {
                where.OR = searchConditions;
            }
        }
        if (status)
            where.status = status;
        if (propertyId)
            where.propertyId = propertyId;
        if (personId)
            where.personId = personId;
        if (userId)
            where.userId = userId;
        if (deposit !== undefined)
            where.deposit = deposit;
        const [offers, total] = await Promise.all([
            this.prisma.offer.findMany({
                where,
                skip: offset,
                take: limit,
                orderBy: { [sortBy]: sortOrder },
                include: {
                    property: {
                        select: {
                            id: true,
                            title: true,
                            city: true,
                            country: true,
                            price: true,
                            images: true,
                        },
                    },
                    person: {
                        select: {
                            id: true,
                            title: true,
                            city: true,
                            country: true,
                            price: true,
                            images: true,
                        },
                    },
                    user: {
                        select: {
                            id: true,
                            name: true,
                            email: true,
                        },
                    },
                },
            }),
            this.prisma.offer.count({ where }),
        ]);
        const totalPages = Math.ceil(total / limit);
        return {
            success: true,
            data: offers,
            pagination: {
                page,
                limit,
                total,
                totalPages,
                hasNext: page < totalPages,
                hasPrev: page > 1,
            },
        };
    }
    async findOne(id) {
        const offer = await this.prisma.offer.findUnique({
            where: { id },
            include: {
                property: {
                    select: {
                        id: true,
                        title: true,
                        city: true,
                        country: true,
                        address: true,
                        price: true,
                        images: true,
                        description: true,
                        owner: {
                            select: {
                                id: true,
                                name: true,
                                email: true,
                                phone: true,
                            },
                        },
                    },
                },
                person: {
                    select: {
                        id: true,
                        title: true,
                        city: true,
                        country: true,
                        address: true,
                        price: true,
                        images: true,
                        description: true,
                        owner: {
                            select: {
                                id: true,
                                name: true,
                                email: true,
                                phone: true,
                            },
                        },
                    },
                },
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        phone: true,
                    },
                },
                booking: {
                    select: {
                        id: true,
                        status: true,
                        startDate: true,
                        endDate: true,
                    },
                },
            },
        });
        if (!offer) {
            throw new common_1.NotFoundException('Offer not found');
        }
        return {
            success: true,
            data: offer,
        };
    }
    async update(id, updateOfferDto, user) {
        const offer = await this.prisma.offer.findUnique({
            where: { id },
            include: {
                property: {
                    select: { ownerId: true },
                },
                person: {
                    select: { ownerId: true },
                },
            },
        });
        if (!offer) {
            throw new common_1.NotFoundException('Offer not found');
        }
        const isOfferOwner = offer.userId === user.sub;
        const isPropertyOwner = offer.property?.ownerId === user.sub;
        const isPersonOwner = offer.person?.ownerId === user.sub;
        const isTargetOwner = isPropertyOwner || isPersonOwner;
        const isAdmin = user.isAdmin;
        if (!isOfferOwner && !isTargetOwner && !isAdmin) {
            throw new common_1.ForbiddenException('Access denied');
        }
        if (updateOfferDto.status && !isTargetOwner && !isAdmin) {
            throw new common_1.ForbiddenException('Only listing owner can update offer status');
        }
        if (offer.status !== 'pending' && !isAdmin) {
            throw new common_1.ConflictException('Cannot update non-pending offers');
        }
        const updatedOffer = await this.prisma.offer.update({
            where: { id },
            data: updateOfferDto,
            include: {
                property: offer.propertyId
                    ? {
                        select: {
                            id: true,
                            title: true,
                            city: true,
                            country: true,
                            price: true,
                            owner: {
                                select: {
                                    id: true,
                                    name: true,
                                    email: true,
                                },
                            },
                        },
                    }
                    : undefined,
                person: offer.personId
                    ? {
                        select: {
                            id: true,
                            title: true,
                            city: true,
                            country: true,
                            price: true,
                            owner: {
                                select: {
                                    id: true,
                                    name: true,
                                    email: true,
                                },
                            },
                        },
                    }
                    : undefined,
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                    },
                },
            },
        });
        return {
            success: true,
            data: updatedOffer,
            message: 'Offer updated successfully',
        };
    }
    async acceptOffer(id, user) {
        return this.updateOfferStatus(id, 'accepted', user);
    }
    async rejectOffer(id, user) {
        return this.updateOfferStatus(id, 'rejected', user);
    }
    async cancelOffer(id, user) {
        return this.updateOfferStatus(id, 'cancelled', user);
    }
    async updateOfferStatus(id, status, user) {
        const offer = await this.prisma.offer.findUnique({
            where: { id },
            include: {
                property: {
                    select: { ownerId: true },
                },
                person: {
                    select: { ownerId: true },
                },
            },
        });
        if (!offer) {
            throw new common_1.NotFoundException('Offer not found');
        }
        const isOfferOwner = offer.userId === user.sub;
        const isPropertyOwner = offer.property?.ownerId === user.sub;
        const isPersonOwner = offer.person?.ownerId === user.sub;
        const isTargetOwner = isPropertyOwner || isPersonOwner;
        const isAdmin = user.isAdmin;
        if (status === 'cancelled' && !isOfferOwner && !isAdmin) {
            throw new common_1.ForbiddenException('Only offer creator can cancel offers');
        }
        if ((status === 'accepted' || status === 'rejected') &&
            !isTargetOwner &&
            !isAdmin) {
            throw new common_1.ForbiddenException('Only listing owner can accept or reject offers');
        }
        if (offer.status !== 'pending') {
            throw new common_1.ConflictException(`Cannot ${status} offer that is already ${offer.status}`);
        }
        const updatedOffer = await this.prisma.offer.update({
            where: { id },
            data: { status },
            include: {
                property: offer.propertyId
                    ? {
                        select: {
                            id: true,
                            title: true,
                            city: true,
                            country: true,
                            price: true,
                            owner: {
                                select: {
                                    id: true,
                                    name: true,
                                    email: true,
                                },
                            },
                        },
                    }
                    : undefined,
                person: offer.personId
                    ? {
                        select: {
                            id: true,
                            title: true,
                            city: true,
                            country: true,
                            price: true,
                            owner: {
                                select: {
                                    id: true,
                                    name: true,
                                    email: true,
                                },
                            },
                        },
                    }
                    : undefined,
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                    },
                },
            },
        });
        return {
            success: true,
            data: updatedOffer,
            message: `Offer ${status} successfully`,
        };
    }
    async remove(id, user) {
        const offer = await this.prisma.offer.findUnique({
            where: { id },
            include: {
                property: {
                    select: { ownerId: true },
                },
                person: {
                    select: { ownerId: true },
                },
                booking: true,
            },
        });
        if (!offer) {
            throw new common_1.NotFoundException('Offer not found');
        }
        const isOfferOwner = offer.userId === user.sub;
        const isPropertyOwner = offer.property?.ownerId === user.sub;
        const isPersonOwner = offer.person?.ownerId === user.sub;
        const isTargetOwner = isPropertyOwner || isPersonOwner;
        const isAdmin = user.isAdmin;
        if (!isOfferOwner && !isTargetOwner && !isAdmin) {
            throw new common_1.ForbiddenException('Access denied');
        }
        if (offer.booking) {
            throw new common_1.ConflictException('Cannot delete offer with associated booking');
        }
        await this.prisma.offer.delete({
            where: { id },
        });
        return {
            success: true,
            message: 'Offer deleted successfully',
        };
    }
    async getMyOffers(userId, query) {
        const { page = 1, limit = 10, status, sortBy = 'createdAt', sortOrder = 'desc', } = query;
        const offset = (page - 1) * limit;
        const where = { userId };
        if (status)
            where.status = status;
        const [offers, total] = await Promise.all([
            this.prisma.offer.findMany({
                where,
                skip: offset,
                take: limit,
                orderBy: { [sortBy]: sortOrder },
                include: {
                    property: {
                        select: {
                            id: true,
                            title: true,
                            city: true,
                            country: true,
                            price: true,
                            images: true,
                        },
                    },
                    person: {
                        select: {
                            id: true,
                            title: true,
                            city: true,
                            country: true,
                            price: true,
                            images: true,
                        },
                    },
                },
            }),
            this.prisma.offer.count({ where }),
        ]);
        const totalPages = Math.ceil(total / limit);
        return {
            success: true,
            data: offers,
            pagination: {
                page,
                limit,
                total,
                totalPages,
                hasNext: page < totalPages,
                hasPrev: page > 1,
            },
        };
    }
    async getPropertyOffers(propertyId, query) {
        const { page = 1, limit = 10, status, sortBy = 'createdAt', sortOrder = 'desc', } = query;
        const offset = (page - 1) * limit;
        const where = { propertyId };
        if (status)
            where.status = status;
        const [offers, total] = await Promise.all([
            this.prisma.offer.findMany({
                where,
                skip: offset,
                take: limit,
                orderBy: { [sortBy]: sortOrder },
                include: {
                    user: {
                        select: {
                            id: true,
                            name: true,
                            email: true,
                        },
                    },
                },
            }),
            this.prisma.offer.count({ where }),
        ]);
        const totalPages = Math.ceil(total / limit);
        return {
            success: true,
            data: offers,
            pagination: {
                page,
                limit,
                total,
                totalPages,
                hasNext: page < totalPages,
                hasPrev: page > 1,
            },
        };
    }
    async getPersonOffers(personId, query) {
        const { page = 1, limit = 10, status, sortBy = 'createdAt', sortOrder = 'desc', } = query;
        const offset = (page - 1) * limit;
        const where = { personId };
        if (status)
            where.status = status;
        const [offers, total] = await Promise.all([
            this.prisma.offer.findMany({
                where,
                skip: offset,
                take: limit,
                orderBy: { [sortBy]: sortOrder },
                include: {
                    user: {
                        select: {
                            id: true,
                            name: true,
                            email: true,
                        },
                    },
                },
            }),
            this.prisma.offer.count({ where }),
        ]);
        const totalPages = Math.ceil(total / limit);
        return {
            success: true,
            data: offers,
            pagination: {
                page,
                limit,
                total,
                totalPages,
                hasNext: page < totalPages,
                hasPrev: page > 1,
            },
        };
    }
};
exports.OffersService = OffersService;
exports.OffersService = OffersService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], OffersService);
//# sourceMappingURL=offers.service.js.map
{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_e4d10b84._.js", "server/edge/chunks/[root-of-the-server]__6e947228._.js", "server/edge/chunks/edge-wrapper_248d3c1f.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|trpc|_next|_vercel|.*\\..*).*){(\\\\.json)}?", "originalSource": "/((?!api|trpc|_next|_vercel|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "R/oltUjaDIKpnKOdmz/MZdAlO+ID1g2d+s/OfLClTjI=", "__NEXT_PREVIEW_MODE_ID": "3d4e63ae6a02eaf0440cf4e835943621", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "482f04f1f6bda67926b14c568018fc7bbd0679bdca39094805670d13a42ec054", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0bcbbf9f06fc873f20c6f873225ab5dd835c4a8edbcaf1cd4378fa3a1480e059"}}}, "instrumentation": null, "functions": {}}
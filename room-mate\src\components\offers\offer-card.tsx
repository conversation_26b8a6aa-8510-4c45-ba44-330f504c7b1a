'use client'

import { useTranslations } from 'next-intl'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Phone, MessageCircle, Calendar, DollarSign } from 'lucide-react'
import { Offer } from '@/lib/types'
import { cn } from '@/lib/utils'

interface OfferCardProps {
    offer: Offer
    onContact?: (offerId: string) => void
    className?: string
    viewType?: 'properties' | 'persons'
}

export function OfferCard({ offer, onContact, className, viewType }: OfferCardProps) {
    const t = useTranslations('offers')

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'pending':
                return 'bg-yellow-100 text-yellow-800'
            case 'accepted':
                return 'bg-green-100 text-green-800'
            case 'rejected':
                return 'bg-red-100 text-red-800'
            case 'cancelled':
                return 'bg-gray-100 text-gray-800'
            default:
                return 'bg-gray-100 text-gray-800'
        }
    }

    return (
        <Card className={cn("h-full transition-all hover:shadow-md", className)}>
            <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                    <div className="space-y-1">
                        <Badge className={getStatusColor(offer.status)}>
                            {offer.status.charAt(0).toUpperCase() + offer.status.slice(1)}
                        </Badge>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <Calendar className="h-4 w-4" />
                            {new Date(offer.createdAt).toLocaleDateString()}
                        </div>
                    </div>
                    <div className="text-right">
                        <div className="flex items-center gap-1 text-lg font-semibold text-primary">
                            <DollarSign className="h-4 w-4" />
                            {offer.price}
                        </div>
                        {offer.duration && (
                            <div className="text-sm text-muted-foreground">
                                {offer.duration}
                            </div>
                        )}
                    </div>
                </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
                <div className="space-y-2">
                    <p className="text-sm text-muted-foreground line-clamp-3">
                        {offer.message}
                    </p>
                </div>

                {/* Property or Person Info */}
                {viewType === 'properties' && offer.property && (
                    <div className="space-y-2 p-3 bg-muted/50 rounded-lg">
                        <h4 className="font-medium text-sm">Property</h4>
                        <p className="text-sm text-muted-foreground">
                            {offer.property.title}
                        </p>
                        <p className="text-xs text-muted-foreground">
                            {offer.property.city}, {offer.property.country}
                        </p>
                    </div>
                )}

                {viewType === 'persons' && offer.person && (
                    <div className="space-y-2 p-3 bg-muted/50 rounded-lg">
                        <h4 className="font-medium text-sm">Person</h4>
                        <p className="text-sm text-muted-foreground">
                            {offer.person.name}
                        </p>
                        <p className="text-xs text-muted-foreground">
                            {offer.person.occupation}
                        </p>
                    </div>
                )}

                {/* User Info */}
                <div className="space-y-2 p-3 bg-muted/50 rounded-lg">
                    <h4 className="font-medium text-sm">Contact</h4>
                    <p className="text-sm text-muted-foreground">
                        {offer.user.name}
                    </p>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <Phone className="h-3 w-3" />
                        {offer.phone}
                    </div>
                </div>

                {/* Actions */}
                <div className="flex gap-2 pt-2">
                    <Button
                        variant="outline"
                        size="sm"
                        className="flex-1"
                        onClick={() => onContact?.(offer.id)}
                    >
                        <MessageCircle className="h-4 w-4 mr-2" />
                        Contact
                    </Button>
                    {offer.deposit && (
                        <Badge variant="secondary" className="self-center">
                            Deposit Required
                        </Badge>
                    )}
                </div>
            </CardContent>
        </Card>
    )
}

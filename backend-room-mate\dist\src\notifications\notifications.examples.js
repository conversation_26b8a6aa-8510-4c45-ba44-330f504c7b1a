"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.usageScenarios = exports.responseExamples = exports.apiEndpointExamples = exports.queryExamples = exports.updateNotificationExample = exports.sendToUsersNotificationExample = exports.broadcastNotificationExample = exports.adminNotificationExample = exports.createNotificationExample = void 0;
exports.createNotificationExample = {
    title: 'New Offer Received',
    message: 'You have received a new offer for your property "Spacious Apartment in Cairo".',
    userId: 'user-uuid-123',
};
exports.adminNotificationExample = {
    title: 'New User Registration',
    message: 'A new user has registered and requires account verification.',
    userId: 'user-uuid-456',
    adminId: 'admin-uuid-789',
};
exports.broadcastNotificationExample = {
    title: 'System Maintenance',
    message: 'The system will be under maintenance on Sunday from 2:00 AM to 4:00 AM. Please save your work.',
    adminId: 'admin-uuid-789',
};
exports.sendToUsersNotificationExample = {
    title: 'Special Offer',
    message: 'You have been selected for our premium membership discount. Check your offers section.',
    userIds: ['user-uuid-123', 'user-uuid-456', 'user-uuid-111', 'user-uuid-222'],
    adminId: 'admin-uuid-789',
};
exports.updateNotificationExample = {
    read: true,
};
exports.queryExamples = {
    getUserNotifications: {
        page: '1',
        limit: '20',
        sortBy: 'createdAt',
        sortOrder: 'desc',
    },
    getUnreadNotifications: {
        read: false,
        page: '1',
        limit: '10',
    },
    searchNotifications: {
        search: 'offer',
        page: '1',
        limit: '15',
    },
};
exports.apiEndpointExamples = {
    create: {
        method: 'POST',
        url: '/api/notifications',
        headers: { Authorization: 'Bearer admin-jwt-token' },
        body: exports.createNotificationExample,
    },
    broadcast: {
        method: 'POST',
        url: '/api/notifications/broadcast',
        headers: { Authorization: 'Bearer admin-jwt-token' },
        body: exports.broadcastNotificationExample,
    },
    sendToUsers: {
        method: 'POST',
        url: '/api/notifications/send-to-users',
        headers: { Authorization: 'Bearer admin-jwt-token' },
        body: exports.sendToUsersNotificationExample,
    },
    getUserNotifications: {
        method: 'GET',
        url: '/api/notifications?userId=user-uuid-123',
        headers: { Authorization: 'Bearer user-jwt-token' },
    },
    markAsRead: {
        method: 'PATCH',
        url: '/api/notifications/notification-uuid-123/read',
        headers: { Authorization: 'Bearer user-jwt-token' },
    },
    markAllAsRead: {
        method: 'PATCH',
        url: '/api/notifications/read-all/user-uuid-123',
        headers: { Authorization: 'Bearer user-jwt-token' },
    },
    getUnreadCount: {
        method: 'GET',
        url: '/api/notifications/unread-count/user-uuid-123',
        headers: { Authorization: 'Bearer user-jwt-token' },
    },
};
exports.responseExamples = {
    notificationResponse: {
        id: 'notification-uuid-123',
        title: 'New Offer Received',
        message: 'You have received a new offer for your property.',
        read: false,
        createdAt: '2024-01-01T00:00:00.000Z',
        user: {
            id: 'user-uuid-123',
            name: 'Ahmed Hassan',
            email: '<EMAIL>',
        },
        admin: {
            id: 'admin-uuid-789',
            name: 'Admin User',
            email: '<EMAIL>',
        },
    },
    broadcastResponse: {
        message: 'Notifications sent to 150 users',
        count: 150,
    },
    sendToUsersResponse: {
        message: 'Notifications sent to 4 users',
        count: 4,
        userIds: [
            'user-uuid-123',
            'user-uuid-456',
            'user-uuid-111',
            'user-uuid-222',
        ],
    },
    unreadCountResponse: {
        unreadCount: 5,
    },
    markAllAsReadResponse: {
        message: '3 notifications marked as read',
        count: 3,
    },
    notificationsListResponse: {
        notifications: [
            {
                id: 'notification-uuid-123',
                title: 'New Offer Received',
                message: 'You have received a new offer for your property.',
                read: false,
                createdAt: '2024-01-01T00:00:00.000Z',
                user: {
                    id: 'user-uuid-123',
                    name: 'Ahmed Hassan',
                    email: '<EMAIL>',
                },
            },
        ],
        pagination: {
            page: 1,
            limit: 10,
            total: 1,
            totalPages: 1,
        },
    },
};
exports.usageScenarios = {
    systemAnnouncement: {
        description: 'Send system-wide announcements to all users',
        endpoint: '/api/notifications/broadcast',
        example: {
            title: 'New Feature Release',
            message: 'We have released a new feature for property search filters. Check it out!',
            adminId: 'admin-uuid-789',
        },
    },
    promotionalCampaign: {
        description: 'Send promotional notifications to selected users',
        endpoint: '/api/notifications/send-to-users',
        example: {
            title: 'Limited Time Offer',
            message: 'Get 20% off on your next booking. Offer valid until end of month.',
            userIds: ['premium-user-1', 'premium-user-2', 'premium-user-3'],
            adminId: 'marketing-admin-uuid',
        },
    },
    personalNotification: {
        description: 'Send personal notification to individual user',
        endpoint: '/api/notifications',
        example: {
            title: 'Booking Confirmed',
            message: 'Your booking for "Downtown Apartment" has been confirmed.',
            userId: 'user-uuid-123',
        },
    },
};
//# sourceMappingURL=notifications.examples.js.map
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma.service';
import { JwtPayload } from '../interfaces/auth.interface';
import { Strategy } from 'passport-jwt';
declare const JwtStrategy_base: new (...args: [opt: import("passport-jwt").StrategyOptionsWithRequest] | [opt: import("passport-jwt").StrategyOptionsWithoutRequest]) => Strategy & {
    validate(...args: any[]): unknown;
};
export declare class JwtStrategy extends JwtStrategy_base {
    private configService;
    private prisma;
    constructor(configService: ConfigService, prisma: PrismaService);
    validate(payload: JwtPayload): Promise<{
        sub: string;
        email: string;
        isAdmin: boolean;
        iat: number | undefined;
        exp: number | undefined;
    }>;
}
export {};

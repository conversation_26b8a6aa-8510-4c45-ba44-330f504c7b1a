import { PrismaService } from '../prisma.service';
import { CreateVerificationRequestDto } from './dto/create-verification-request.dto';
import { QueryVerificationRequestsDto } from './dto/query-verification-requests.dto';
import { QueryVerificationsDto } from './dto/query-verifications.dto';
import { UpdateVerificationDto } from './dto/update-verification.dto';
import { VerificationRequestResponse, VerificationResponse, VerificationRequestsQueryResult, VerificationsQueryResult, UserPayload } from './interfaces/verification.interface';
export declare class VerificationService {
    private prisma;
    constructor(prisma: PrismaService);
    private readonly verificationRequestSelectFields;
    private readonly verificationSelectFields;
    createVerificationRequest(createVerificationRequestDto: CreateVerificationRequestDto, currentUser: UserPayload): Promise<VerificationRequestResponse>;
    findAllVerificationRequests(query: QueryVerificationRequestsDto, currentUser: UserPayload): Promise<VerificationRequestsQueryResult>;
    findUserVerificationRequests(currentUser: UserPayload): Promise<VerificationRequestResponse[]>;
    findOneVerificationRequest(id: string, currentUser: UserPayload): Promise<VerificationRequestResponse>;
    deleteVerificationRequest(id: string, currentUser: UserPayload): Promise<{
        message: string;
    }>;
    processVerificationRequest(requestId: string, updateVerificationDto: UpdateVerificationDto, currentUser: UserPayload): Promise<VerificationResponse>;
    findAllVerifications(query: QueryVerificationsDto, currentUser: UserPayload): Promise<VerificationsQueryResult>;
    findUserVerifications(currentUser: UserPayload): Promise<VerificationResponse[]>;
    findOneVerification(id: string, currentUser: UserPayload): Promise<VerificationResponse>;
    getVerificationStats(): Promise<{
        totalRequests: number;
        pendingRequests: number;
        approvedVerifications: number;
        rejectedVerifications: number;
    }>;
    revokeVerification(id: string, currentUser: UserPayload): Promise<VerificationResponse>;
}

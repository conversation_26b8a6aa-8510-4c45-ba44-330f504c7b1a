import { PrismaService } from '../prisma.service';
import { CreateNotificationDto, BroadcastNotificationDto, SendToUsersNotificationDto } from './dto/create-notification.dto';
import { UpdateNotificationDto } from './dto/update-notification.dto';
import { QueryNotificationsDto } from './dto/query-notifications.dto';
export declare class NotificationsService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createNotificationDto: CreateNotificationDto): Promise<{
        user: {
            id: string;
            name: string;
            email: string;
        };
        admin: {
            id: string;
            name: string;
            email: string;
        };
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        title: string;
        message: string;
        read: boolean;
        adminId: string;
        userId: string;
    }>;
    findAll(queryDto: QueryNotificationsDto): Promise<{
        notifications: ({
            user: {
                id: string;
                name: string;
                email: string;
            };
            admin: {
                id: string;
                name: string;
                email: string;
            };
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            title: string;
            message: string;
            read: boolean;
            adminId: string;
            userId: string;
        })[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    findOne(id: string): Promise<{
        user: {
            id: string;
            name: string;
            email: string;
        };
        admin: {
            id: string;
            name: string;
            email: string;
        };
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        title: string;
        message: string;
        read: boolean;
        adminId: string;
        userId: string;
    }>;
    update(id: string, updateNotificationDto: UpdateNotificationDto): Promise<{
        user: {
            id: string;
            name: string;
            email: string;
        };
        admin: {
            id: string;
            name: string;
            email: string;
        };
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        title: string;
        message: string;
        read: boolean;
        adminId: string;
        userId: string;
    }>;
    remove(id: string): Promise<{
        message: string;
    }>;
    markAsRead(id: string): Promise<{
        user: {
            id: string;
            name: string;
            email: string;
        };
        admin: {
            id: string;
            name: string;
            email: string;
        };
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        title: string;
        message: string;
        read: boolean;
        adminId: string;
        userId: string;
    }>;
    markAllAsRead(userId: string): Promise<{
        message: string;
        count: number;
    }>;
    getUnreadCount(userId: string): Promise<{
        unreadCount: number;
    }>;
    broadcastToAllUsers(broadcastNotificationDto: BroadcastNotificationDto): Promise<{
        message: string;
        count: number;
    }>;
    sendToSpecificUsers(sendToUsersNotificationDto: SendToUsersNotificationDto): Promise<{
        message: string;
        count: number;
        userIds: string[];
    }>;
}

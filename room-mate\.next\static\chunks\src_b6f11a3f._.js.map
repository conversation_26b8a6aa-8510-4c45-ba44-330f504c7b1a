{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/actions/properties.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { del, get, patch, post } from \"@/services/api\";\r\n\r\ninterface Property {\r\n  title: string;\r\n  images: string[];\r\n  city: string;\r\n  country: string;\r\n  neighborhood: string;\r\n  latitude: string;\r\n  longitude: string;\r\n  address: string;\r\n  description: string;\r\n  type: string;\r\n  roomType: string;\r\n  genderRequired: string;\r\n  totalRooms: string;\r\n  availableRooms: string;\r\n  price: string;\r\n  size: string;\r\n  floor: string;\r\n  bathrooms: string;\r\n  separatedBathroom: boolean;\r\n  residentsCount: string;\r\n  availablePersons: string;\r\n  rentTime: string;\r\n  paymentTime: string;\r\n  priceIncludeWaterAndElectricity: boolean;\r\n  allowSmoking: boolean;\r\n  includeFurniture: boolean;\r\n  airConditioning: boolean;\r\n  includeWaterHeater: boolean;\r\n  parking: boolean;\r\n  internet: boolean;\r\n  nearToMetro: boolean;\r\n  nearToMarket: boolean;\r\n  elevator: boolean;\r\n  trialPeriod: boolean;\r\n  goodForForeigners: boolean;\r\n  categoryId: string;\r\n  termsAndConditions: string;\r\n}\r\n\r\nexport const createProperty = async (data: Property) => {\r\n  console.log(\"create property data\", data);\r\n  const response = await post(\"/properties\", data);\r\n  console.log(\"create property response\", response);\r\n  return response;\r\n};\r\n\r\nexport const getProperties = async ({\r\n  page = 1,\r\n  limit = 12,\r\n  title = \"\",\r\n  city = \"\",\r\n  country = \"\",\r\n  neighborhood = \"\",\r\n  address = \"\",\r\n  type = \"\",\r\n  roomType = \"\",\r\n  genderRequired = \"\",\r\n  totalRooms = \"\",\r\n  availableRooms = \"\",\r\n  size = \"\",\r\n  floor = \"\",\r\n  bathrooms = \"\",\r\n  separatedBathroom = false,\r\n  residentsCount = \"\",\r\n  availablePersons = \"\",\r\n  minPrice = \"\",\r\n  maxPrice = \"\",\r\n  rentTime = \"\",\r\n  paymentTime = \"\",\r\n  priceIncludeWaterAndElectricity = false,\r\n  minRating = 0,\r\n  minTotalRatings = 0,\r\n  isVerified = false,\r\n  isAvailable = false,\r\n  categoryId = \"\",\r\n  allowSmoking = false,\r\n  includeFurniture = false,\r\n  airConditioning = false,\r\n  includeWaterHeater = false,\r\n  parking = false,\r\n  internet = false,\r\n  nearToMetro = false,\r\n  nearToMarket = false,\r\n  elevator = false,\r\n  trialPeriod = false,\r\n  goodForForeigners = false,\r\n  sortBy = \"createdAt\",\r\n  sortOrder = \"asc\",\r\n}: {\r\n  page?: number;\r\n  limit?: number;\r\n  title?: string;\r\n  city?: string;\r\n  country?: string;\r\n  neighborhood?: string;\r\n  address?: string;\r\n  latitude?: string;\r\n  longitude?: string;\r\n  type?: string;\r\n  roomType?: string;\r\n  genderRequired?: string;\r\n  totalRooms?: string;\r\n  availableRooms?: string;\r\n  size?: string;\r\n  floor?: string;\r\n  bathrooms?: string;\r\n  separatedBathroom?: boolean;\r\n  residentsCount?: string;\r\n  availablePersons?: string;\r\n  minPrice?: string;\r\n  maxPrice?: string;\r\n  rentTime?: string;\r\n  paymentTime?: string;\r\n  priceIncludeWaterAndElectricity?: boolean;\r\n  minRating?: number;\r\n  minTotalRatings?: number;\r\n  isVerified?: boolean;\r\n  isAvailable?: boolean;\r\n  categoryId?: string;\r\n  allowSmoking?: boolean;\r\n  includeFurniture?: boolean;\r\n  airConditioning?: boolean;\r\n  includeWaterHeater?: boolean;\r\n  parking?: boolean;\r\n  internet?: boolean;\r\n  nearToMetro?: boolean;\r\n  nearToMarket?: boolean;\r\n  elevator?: boolean;\r\n  trialPeriod?: boolean;\r\n  goodForForeigners?: boolean;\r\n  sortBy?: \"createdAt\" | \"updatedAt\" | \"price\" | \"rating\" | \"title\";\r\n  sortOrder?: \"asc\" | \"desc\";\r\n}) => {\r\n  const response = await get(\r\n    `/properties?page=${page}&limit=${limit}&${title ? `title=${title}&` : \"\"}${\r\n      city ? `city=${city}&` : \"\"\r\n    }${country ? `country=${country}&` : \"\"}${\r\n      neighborhood ? `neighborhood=${neighborhood}&` : \"\"\r\n    }${address ? `address=${address}&` : \"\"}${type ? `type=${type}&` : \"\"}${\r\n      roomType ? `roomType=${roomType}&` : \"\"\r\n    }${genderRequired ? `genderRequired=${genderRequired}&` : \"\"}${\r\n      totalRooms ? `totalRooms=${totalRooms}&` : \"\"\r\n    }${availableRooms ? `availableRooms=${availableRooms}&` : \"\"}${\r\n      size ? `size=${size}&` : \"\"\r\n    }${floor ? `floor=${floor}&` : \"\"}${\r\n      bathrooms ? `bathrooms=${bathrooms}&` : \"\"\r\n    }${separatedBathroom ? `separatedBathroom=${separatedBathroom}&` : \"\"}${\r\n      residentsCount ? `residentsCount=${residentsCount}&` : \"\"\r\n    }${availablePersons ? `availablePersons=${availablePersons}&` : \"\"}${\r\n      rentTime ? `rentTime=${rentTime}&` : \"\"\r\n    }${paymentTime ? `paymentTime=${paymentTime}&` : \"\"}${\r\n      priceIncludeWaterAndElectricity\r\n        ? `priceIncludeWaterAndElectricity=${priceIncludeWaterAndElectricity}&`\r\n        : \"\"\r\n    }${allowSmoking ? `allowSmoking=${allowSmoking}&` : \"\"}${\r\n      includeFurniture ? `includeFurniture=${includeFurniture}&` : \"\"\r\n    }${airConditioning ? `airConditioning=${airConditioning}&` : \"\"}${\r\n      includeWaterHeater ? `includeWaterHeater=${includeWaterHeater}&` : \"\"\r\n    }${parking ? `parking=${parking}&` : \"\"}${\r\n      internet ? `internet=${internet}&` : \"\"\r\n    }${nearToMetro ? `nearToMetro=${nearToMetro}&` : \"\"}${\r\n      nearToMarket ? `nearToMarket=${nearToMarket}&` : \"\"\r\n    }${elevator ? `elevator=${elevator}&` : \"\"}${\r\n      trialPeriod ? `trialPeriod=${trialPeriod}&` : \"\"\r\n    }${goodForForeigners ? `goodForForeigners=${goodForForeigners}&` : \"\"}${\r\n      categoryId ? `categoryId=${categoryId}&` : \"\"\r\n    }${minRating ? `minRating=${minRating}&` : \"\"}${\r\n      minTotalRatings ? `minTotalRatings=${minTotalRatings}&` : \"\"\r\n    }${isVerified ? `isVerified=${isVerified}&` : \"\"}${\r\n      minPrice ? `minPrice=${minPrice}&` : \"\"\r\n    }${maxPrice ? `maxPrice=${maxPrice}&` : \"\"}${\r\n      isAvailable ? `isAvailable=${isAvailable}&` : \"\"\r\n    }${sortBy ? `sortBy=${sortBy}&` : \"\"}${\r\n      sortOrder ? `sortOrder=${sortOrder}` : \"\"\r\n    }`\r\n  );\r\n\r\n  return response;\r\n};\r\n\r\nexport const getMyProperties = async () => {\r\n  const response = await get(`/properties/my-properties`);\r\n  return response;\r\n};\r\n\r\nexport const getProperty = async (slug: string) => {\r\n  const response = await get(`/properties/slug/${slug}`);\r\n  return response;\r\n};\r\n\r\nexport const getPropertiesByUserId = async (userId: string) => {\r\n  const response = await get(`/properties/user/${userId}`);\r\n  return response;\r\n};\r\n\r\nexport const getFavoriteProperties = async () => {\r\n  const response = await get(`/properties/favorites`);\r\n  return response;\r\n};\r\n\r\nexport const toggleFavorite = async (id: string) => {\r\n  const response = await patch(`/properties/${id}/toggle-favorite`);\r\n  console.log(\"toggle favorite response\", response);\r\n  return response;\r\n};\r\n\r\nexport const updateProperty = async (data: Property) => {\r\n  const response = await patch(\"/properties\", data);\r\n  return response;\r\n};\r\n\r\nexport const deleteProperty = async (id: string) => {\r\n  const response = await del(`/properties/${id}`);\r\n  return response;\r\n};\r\n"], "names": [], "mappings": ";;;;;;IA8La,cAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/shared/image-slider.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport Image from 'next/image'\r\nimport { ChevronLeft, ChevronRight } from 'lucide-react'\r\nimport { Button } from '@/components/ui/button'\r\nimport { cn } from '@/lib/utils'\r\n\r\ninterface ImageSliderProps {\r\n    images: string[]\r\n    alt: string\r\n    className?: string\r\n    aspectRatio?: 'square' | 'video' | 'auto'\r\n    showNavigation?: boolean\r\n    showDots?: boolean\r\n}\r\n\r\nexport function ImageSlider({\r\n    images,\r\n    alt,\r\n    className,\r\n    aspectRatio = 'video',\r\n    showNavigation = true,\r\n    showDots = true,\r\n}: ImageSliderProps) {\r\n    const [currentIndex, setCurrentIndex] = useState(0)\r\n\r\n    if (!images || images.length === 0) {\r\n        return (\r\n            <div\r\n                className={cn(\r\n                    'relative overflow-hidden rounded-lg bg-muted flex items-center justify-center',\r\n                    aspectRatio === 'square' && 'aspect-square',\r\n                    aspectRatio === 'video' && 'aspect-video',\r\n                    className\r\n                )}\r\n            >\r\n                <p className=\"text-muted-foreground text-sm\">No images available</p>\r\n            </div>\r\n        )\r\n    }\r\n\r\n    const goToPrevious = () => {\r\n        setCurrentIndex((prevIndex) =>\r\n            prevIndex === 0 ? images.length - 1 : prevIndex - 1\r\n        )\r\n    }\r\n\r\n    const goToNext = () => {\r\n        setCurrentIndex((prevIndex) =>\r\n            prevIndex === images.length - 1 ? 0 : prevIndex + 1\r\n        )\r\n    }\r\n\r\n    const goToSlide = (index: number) => {\r\n        setCurrentIndex(index)\r\n    }\r\n\r\n    return (\r\n        <div\r\n            className={cn(\r\n                'relative overflow-hidden rounded-lg group',\r\n                aspectRatio === 'square' && 'aspect-square',\r\n                aspectRatio === 'video' && 'aspect-video',\r\n                className\r\n            )}\r\n        >\r\n            {/* Main Image */}\r\n            <div className=\"relative w-full h-full\">\r\n                <Image\r\n                    src={images[currentIndex]}\r\n                    alt={`${alt} ${currentIndex + 1}`}\r\n                    fill\r\n                    className=\"object-cover transition-opacity duration-300\"\r\n                    sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\r\n                />\r\n            </div>\r\n\r\n            {/* Navigation Arrows */}\r\n            {showNavigation && images.length > 1 && (\r\n                <>\r\n                    <Button\r\n                        variant=\"secondary\"\r\n                        size=\"icon\"\r\n                        className=\"absolute left-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-black/60 hover:bg-black/80 text-white border-0 backdrop-blur-sm\"\r\n                        onClick={(e) => {\r\n                            e.preventDefault()\r\n                            e.stopPropagation()\r\n                            goToPrevious()\r\n                        }}\r\n                    >\r\n                        <ChevronLeft className=\"h-4 w-4\" />\r\n                    </Button>\r\n                    <Button\r\n                        variant=\"secondary\"\r\n                        size=\"icon\"\r\n                        className=\"absolute right-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-black/60 hover:bg-black/80 text-white border-0 backdrop-blur-sm\"\r\n                        onClick={(e) => {\r\n                            e.preventDefault()\r\n                            e.stopPropagation()\r\n                            goToNext()\r\n                        }}\r\n                    >\r\n                        <ChevronRight className=\"h-4 w-4\" />\r\n                    </Button>\r\n                </>\r\n            )}\r\n\r\n            {/* Image Counter */}\r\n            {images.length > 1 && (\r\n                <div className=\"absolute top-2 right-2 bg-black/60 text-white text-xs px-2 py-1 rounded backdrop-blur-sm\">\r\n                    {currentIndex + 1}/{images.length}\r\n                </div>\r\n            )}\r\n\r\n            {/* Navigation Dots */}\r\n            {showDots && images.length > 1 && (\r\n                <div className=\"absolute bottom-2 left-1/2 -translate-x-1/2 flex gap-1\">\r\n                    {images.map((_, index) => (\r\n                        <button\r\n                            key={index}\r\n                            className={cn(\r\n                                'w-2 h-2 rounded-full transition-all duration-200',\r\n                                index === currentIndex\r\n                                    ? 'bg-white scale-110'\r\n                                    : 'bg-white/60 hover:bg-white/80'\r\n                            )}\r\n                            onClick={(e) => {\r\n                                e.preventDefault()\r\n                                e.stopPropagation()\r\n                                goToSlide(index)\r\n                            }}\r\n                        />\r\n                    ))}\r\n                </div>\r\n            )}\r\n        </div>\r\n    )\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;;;AANA;;;;;;AAiBO,SAAS,YAAY,EACxB,MAAM,EACN,GAAG,EACH,SAAS,EACT,cAAc,OAAO,EACrB,iBAAiB,IAAI,EACrB,WAAW,IAAI,EACA;;IACf,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,IAAI,CAAC,UAAU,OAAO,MAAM,KAAK,GAAG;QAChC,qBACI,6LAAC;YACG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,iFACA,gBAAgB,YAAY,iBAC5B,gBAAgB,WAAW,gBAC3B;sBAGJ,cAAA,6LAAC;gBAAE,WAAU;0BAAgC;;;;;;;;;;;IAGzD;IAEA,MAAM,eAAe;QACjB,gBAAgB,CAAC,YACb,cAAc,IAAI,OAAO,MAAM,GAAG,IAAI,YAAY;IAE1D;IAEA,MAAM,WAAW;QACb,gBAAgB,CAAC,YACb,cAAc,OAAO,MAAM,GAAG,IAAI,IAAI,YAAY;IAE1D;IAEA,MAAM,YAAY,CAAC;QACf,gBAAgB;IACpB;IAEA,qBACI,6LAAC;QACG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,6CACA,gBAAgB,YAAY,iBAC5B,gBAAgB,WAAW,gBAC3B;;0BAIJ,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC,gIAAA,CAAA,UAAK;oBACF,KAAK,MAAM,CAAC,aAAa;oBACzB,KAAK,GAAG,IAAI,CAAC,EAAE,eAAe,GAAG;oBACjC,IAAI;oBACJ,WAAU;oBACV,OAAM;;;;;;;;;;;YAKb,kBAAkB,OAAO,MAAM,GAAG,mBAC/B;;kCACI,6LAAC,qIAAA,CAAA,SAAM;wBACH,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,CAAC;4BACN,EAAE,cAAc;4BAChB,EAAE,eAAe;4BACjB;wBACJ;kCAEA,cAAA,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;kCAE3B,6LAAC,qIAAA,CAAA,SAAM;wBACH,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,CAAC;4BACN,EAAE,cAAc;4BAChB,EAAE,eAAe;4BACjB;wBACJ;kCAEA,cAAA,6LAAC,yNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;;;;;;YAMnC,OAAO,MAAM,GAAG,mBACb,6LAAC;gBAAI,WAAU;;oBACV,eAAe;oBAAE;oBAAE,OAAO,MAAM;;;;;;;YAKxC,YAAY,OAAO,MAAM,GAAG,mBACzB,6LAAC;gBAAI,WAAU;0BACV,OAAO,GAAG,CAAC,CAAC,GAAG,sBACZ,6LAAC;wBAEG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,oDACA,UAAU,eACJ,uBACA;wBAEV,SAAS,CAAC;4BACN,EAAE,cAAc;4BAChB,EAAE,eAAe;4BACjB,UAAU;wBACd;uBAXK;;;;;;;;;;;;;;;;AAkBjC;GAzHgB;KAAA", "debugId": null}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/property-details/sections/property-gallery.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport Image from 'next/image'\r\nimport { <PERSON><PERSON>, DialogContent } from '@/components/ui/dialog'\r\nimport { Button } from '@/components/ui/button'\r\nimport { ImageSlider } from '@/components/shared/image-slider'\r\nimport { Expand, Camera, Play, Grid3X3, ChevronLeft, ChevronRight, X } from 'lucide-react'\r\nimport { cn } from '@/lib/utils'\r\n\r\ninterface PropertyGalleryProps {\r\n    property: any\r\n    onImageClick: (index: number) => void\r\n    isModalOpen: boolean\r\n    setIsModalOpen: (open: boolean) => void\r\n    selectedImageIndex: number\r\n    setSelectedImageIndex: (index: number) => void\r\n}\r\n\r\nexport default function PropertyGallery({\r\n    property,\r\n    onImageClick,\r\n    isModalOpen,\r\n    setIsModalOpen,\r\n    selectedImageIndex,\r\n    setSelectedImageIndex\r\n}: PropertyGalleryProps) {\r\n    const [showAllImages, setShowAllImages] = useState(false)\r\n\r\n    const images = property.images || []\r\n    const hasVirtualTour = property.virtualTourUrl // Assuming this field exists\r\n    const hasVideo = property.videoUrl // Assuming this field exists\r\n\r\n    const goToPrevious = () => {\r\n        setSelectedImageIndex(selectedImageIndex === 0 ? images.length - 1 : selectedImageIndex - 1)\r\n    }\r\n\r\n    const goToNext = () => {\r\n        setSelectedImageIndex(selectedImageIndex === images.length - 1 ? 0 : selectedImageIndex + 1)\r\n    }\r\n\r\n    const handleSliderImageClick = () => {\r\n        // Get current slide index from the ImageSlider component\r\n        // For now, we'll start from the first image when clicking\r\n        onImageClick(0)\r\n    }\r\n\r\n    return (\r\n        <>\r\n            {/* Main Gallery */}\r\n            <div className=\"relative bg-white\">\r\n                {images.length > 0 ? (\r\n                    <div className=\"max-w-7xl mx-auto\">\r\n                        {/* Unified Slider Layout for All Devices */}\r\n                        <div className=\"relative\">\r\n                            {/* Desktop Slider */}\r\n                            <div className=\"hidden lg:block h-[500px]\">\r\n                                <div\r\n                                    className=\"h-full cursor-pointer\"\r\n                                    onClick={handleSliderImageClick}\r\n                                >\r\n                                    <ImageSlider\r\n                                        images={images}\r\n                                        alt={property.title}\r\n                                        className=\"h-full rounded-lg\"\r\n                                        showNavigation={true}\r\n                                        showDots={true}\r\n                                        aspectRatio=\"auto\"\r\n                                    />\r\n                                </div>\r\n                            </div>\r\n\r\n                            {/* Mobile Slider */}\r\n                            <div className=\"lg:hidden h-[300px] sm:h-[400px]\">\r\n                                <div\r\n                                    className=\"h-full cursor-pointer\"\r\n                                    onClick={handleSliderImageClick}\r\n                                >\r\n                                    <ImageSlider\r\n                                        images={images}\r\n                                        alt={property.title}\r\n                                        className=\"h-full rounded-lg\"\r\n                                        showNavigation={true}\r\n                                        showDots={true}\r\n                                        aspectRatio=\"auto\"\r\n                                    />\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        {/* Action Buttons */}\r\n                        {/* <div className=\"absolute bottom-4 right-4 flex gap-2 z-10\">\r\n                            <Button\r\n                                variant=\"secondary\"\r\n                                size=\"sm\"\r\n                                onClick={() => onImageClick(0)}\r\n                                className=\"bg-white/90 hover:bg-white\"\r\n                            >\r\n                                <Camera className=\"h-4 w-4 mr-1\" />\r\n                                <span className=\"hidden sm:inline\">{images.length} Photos</span>\r\n                                <span className=\"sm:hidden\">{images.length}</span>\r\n                            </Button>\r\n\r\n                            {images.length > 4 && (\r\n                                <Button\r\n                                    variant=\"secondary\"\r\n                                    size=\"sm\"\r\n                                    onClick={() => setShowAllImages(true)}\r\n                                    className=\"bg-white/90 hover:bg-white\"\r\n                                >\r\n                                    <Grid3X3 className=\"h-4 w-4 mr-1\" />\r\n                                    <span className=\"hidden sm:inline\">View All</span>\r\n                                    <span className=\"sm:hidden\">All</span>\r\n                                </Button>\r\n                            )}\r\n\r\n                            {hasVirtualTour && (\r\n                                <Button\r\n                                    variant=\"secondary\"\r\n                                    size=\"sm\"\r\n                                    className=\"bg-white/90 hover:bg-white\"\r\n                                    onClick={() => window.open(property.virtualTourUrl, '_blank')}\r\n                                >\r\n                                    <div className=\"h-4 w-4 mr-1 rounded-full bg-blue-600 flex items-center justify-center\">\r\n                                        <div className=\"h-2 w-2 bg-white rounded-full\" />\r\n                                    </div>\r\n                                    <span className=\"hidden sm:inline\">Virtual Tour</span>\r\n                                    <span className=\"sm:hidden\">Tour</span>\r\n                                </Button>\r\n                            )}\r\n\r\n                            {hasVideo && (\r\n                                <Button\r\n                                    variant=\"secondary\"\r\n                                    size=\"sm\"\r\n                                    className=\"bg-white/90 hover:bg-white\"\r\n                                    onClick={() => window.open(property.videoUrl, '_blank')}\r\n                                >\r\n                                    <Play className=\"h-4 w-4 mr-1\" />\r\n                                    <span className=\"hidden sm:inline\">Video</span>\r\n                                </Button>\r\n                            )}\r\n                        </div> */}\r\n                    </div>\r\n                ) : (\r\n                    <div className=\"h-[400px] bg-gray-200 flex items-center justify-center rounded-lg\">\r\n                        <div className=\"text-center\">\r\n                            <Camera className=\"h-12 w-12 mx-auto text-gray-400 mb-2\" />\r\n                            <p className=\"text-gray-500\">No images available</p>\r\n                        </div>\r\n                    </div>\r\n                )}\r\n            </div>\r\n\r\n            {/* Full Screen Image Modal */}\r\n            <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>\r\n                <DialogContent className=\"max-w-[95vw] max-h-[95vh] p-0 bg-black\">\r\n                    <div className=\"relative w-full h-[95vh] flex items-center justify-center\">\r\n                        {/* Close Button */}\r\n                        <Button\r\n                            variant=\"ghost\"\r\n                            size=\"icon\"\r\n                            className=\"absolute top-4 right-4 z-10 text-white hover:bg-white/20\"\r\n                            onClick={() => setIsModalOpen(false)}\r\n                        >\r\n                            <X className=\"h-6 w-6\" />\r\n                        </Button>\r\n\r\n                        {/* Image Counter */}\r\n                        <div className=\"absolute top-4 left-4 z-10 bg-black/60 text-white px-3 py-1 rounded backdrop-blur-sm\">\r\n                            {selectedImageIndex + 1} / {images.length}\r\n                        </div>\r\n\r\n                        {/* Main Image */}\r\n                        <div className=\"relative w-full h-full\">\r\n                            <Image\r\n                                src={images[selectedImageIndex]}\r\n                                alt={`${property.title} - Image ${selectedImageIndex + 1}`}\r\n                                fill\r\n                                className=\"object-contain\"\r\n                                quality={100}\r\n                            />\r\n                        </div>\r\n\r\n                        {/* Navigation */}\r\n                        {images.length > 1 && (\r\n                            <>\r\n                                <Button\r\n                                    variant=\"ghost\"\r\n                                    size=\"icon\"\r\n                                    className=\"absolute left-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20\"\r\n                                    onClick={goToPrevious}\r\n                                >\r\n                                    <ChevronLeft className=\"h-8 w-8\" />\r\n                                </Button>\r\n                                <Button\r\n                                    variant=\"ghost\"\r\n                                    size=\"icon\"\r\n                                    className=\"absolute right-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20\"\r\n                                    onClick={goToNext}\r\n                                >\r\n                                    <ChevronRight className=\"h-8 w-8\" />\r\n                                </Button>\r\n                            </>\r\n                        )}\r\n\r\n                        {/* Thumbnail Strip */}\r\n                        <div className=\"absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2 max-w-[90vw] overflow-x-auto\">\r\n                            {images.map((image: string, index: number) => (\r\n                                <button\r\n                                    key={index}\r\n                                    className={cn(\r\n                                        \"relative flex-shrink-0 w-16 h-16 rounded overflow-hidden border-2 transition-all\",\r\n                                        index === selectedImageIndex\r\n                                            ? \"border-white scale-110\"\r\n                                            : \"border-transparent opacity-70 hover:opacity-100\"\r\n                                    )}\r\n                                    onClick={() => setSelectedImageIndex(index)}\r\n                                >\r\n                                    <Image\r\n                                        src={image}\r\n                                        alt={`Thumbnail ${index + 1}`}\r\n                                        fill\r\n                                        className=\"object-cover\"\r\n                                    />\r\n                                </button>\r\n                            ))}\r\n                        </div>\r\n                    </div>\r\n                </DialogContent>\r\n            </Dialog>\r\n\r\n            {/* All Images Grid Modal */}\r\n            <Dialog open={showAllImages} onOpenChange={setShowAllImages}>\r\n                <DialogContent className=\"max-w-6xl max-h-[90vh] overflow-y-auto\">\r\n                    <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 p-4\">\r\n                        {images.map((image: string, index: number) => (\r\n                            <div\r\n                                key={index}\r\n                                className=\"relative aspect-square group cursor-pointer rounded-lg overflow-hidden\"\r\n                                onClick={() => {\r\n                                    setShowAllImages(false)\r\n                                    onImageClick(index)\r\n                                }}\r\n                            >\r\n                                <Image\r\n                                    src={image}\r\n                                    alt={`${property.title} - Image ${index + 1}`}\r\n                                    fill\r\n                                    className=\"object-cover group-hover:scale-105 transition-transform\"\r\n                                />\r\n                                <div className=\"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors\" />\r\n                            </div>\r\n                        ))}\r\n                    </div>\r\n                </DialogContent>\r\n            </Dialog>\r\n        </>\r\n    )\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;AARA;;;;;;;;AAmBe,SAAS,gBAAgB,EACpC,QAAQ,EACR,YAAY,EACZ,WAAW,EACX,cAAc,EACd,kBAAkB,EAClB,qBAAqB,EACF;;IACnB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,SAAS,SAAS,MAAM,IAAI,EAAE;IACpC,MAAM,iBAAiB,SAAS,cAAc,CAAC,6BAA6B;;IAC5E,MAAM,WAAW,SAAS,QAAQ,CAAC,6BAA6B;;IAEhE,MAAM,eAAe;QACjB,sBAAsB,uBAAuB,IAAI,OAAO,MAAM,GAAG,IAAI,qBAAqB;IAC9F;IAEA,MAAM,WAAW;QACb,sBAAsB,uBAAuB,OAAO,MAAM,GAAG,IAAI,IAAI,qBAAqB;IAC9F;IAEA,MAAM,yBAAyB;QAC3B,yDAAyD;QACzD,0DAA0D;QAC1D,aAAa;IACjB;IAEA,qBACI;;0BAEI,6LAAC;gBAAI,WAAU;0BACV,OAAO,MAAM,GAAG,kBACb,6LAAC;oBAAI,WAAU;8BAEX,cAAA,6LAAC;wBAAI,WAAU;;0CAEX,6LAAC;gCAAI,WAAU;0CACX,cAAA,6LAAC;oCACG,WAAU;oCACV,SAAS;8CAET,cAAA,6LAAC,kJAAA,CAAA,cAAW;wCACR,QAAQ;wCACR,KAAK,SAAS,KAAK;wCACnB,WAAU;wCACV,gBAAgB;wCAChB,UAAU;wCACV,aAAY;;;;;;;;;;;;;;;;0CAMxB,6LAAC;gCAAI,WAAU;0CACX,cAAA,6LAAC;oCACG,WAAU;oCACV,SAAS;8CAET,cAAA,6LAAC,kJAAA,CAAA,cAAW;wCACR,QAAQ;wCACR,KAAK,SAAS,KAAK;wCACnB,WAAU;wCACV,gBAAgB;wCAChB,UAAU;wCACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;yCA6DhC,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBAAI,WAAU;;0CACX,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;0BAO7C,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAa,cAAc;0BACrC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CAEX,6LAAC,qIAAA,CAAA,SAAM;gCACH,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;0CAIjB,6LAAC;gCAAI,WAAU;;oCACV,qBAAqB;oCAAE;oCAAI,OAAO,MAAM;;;;;;;0CAI7C,6LAAC;gCAAI,WAAU;0CACX,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCACF,KAAK,MAAM,CAAC,mBAAmB;oCAC/B,KAAK,GAAG,SAAS,KAAK,CAAC,SAAS,EAAE,qBAAqB,GAAG;oCAC1D,IAAI;oCACJ,WAAU;oCACV,SAAS;;;;;;;;;;;4BAKhB,OAAO,MAAM,GAAG,mBACb;;kDACI,6LAAC,qIAAA,CAAA,SAAM;wCACH,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS;kDAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAE3B,6LAAC,qIAAA,CAAA,SAAM;wCACH,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS;kDAET,cAAA,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;0CAMpC,6LAAC;gCAAI,WAAU;0CACV,OAAO,GAAG,CAAC,CAAC,OAAe,sBACxB,6LAAC;wCAEG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,oFACA,UAAU,qBACJ,2BACA;wCAEV,SAAS,IAAM,sBAAsB;kDAErC,cAAA,6LAAC,gIAAA,CAAA,UAAK;4CACF,KAAK;4CACL,KAAK,CAAC,UAAU,EAAE,QAAQ,GAAG;4CAC7B,IAAI;4CACJ,WAAU;;;;;;uCAbT;;;;;;;;;;;;;;;;;;;;;;;;;;0BAuB7B,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAe,cAAc;0BACvC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;kCACV,OAAO,GAAG,CAAC,CAAC,OAAe,sBACxB,6LAAC;gCAEG,WAAU;gCACV,SAAS;oCACL,iBAAiB;oCACjB,aAAa;gCACjB;;kDAEA,6LAAC,gIAAA,CAAA,UAAK;wCACF,KAAK;wCACL,KAAK,GAAG,SAAS,KAAK,CAAC,SAAS,EAAE,QAAQ,GAAG;wCAC7C,IAAI;wCACJ,WAAU;;;;;;kDAEd,6LAAC;wCAAI,WAAU;;;;;;;+BAbV;;;;;;;;;;;;;;;;;;;;;;AAqBrC;GAhPwB;KAAA", "debugId": null}}, {"offset": {"line": 542, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-2 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 657, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/lib/currency.ts"], "sourcesContent": ["// Currency utilities for handling location-based pricing\r\n\r\ninterface CurrencyConfig {\r\n  code: string;\r\n  symbol: string;\r\n  locale: string;\r\n  exchangeRate: number; // Rate to USD\r\n}\r\n\r\n// Currency configurations for supported countries\r\nconst CURRENCY_CONFIGS: Record<string, CurrencyConfig> = {\r\n  EG: {\r\n    code: \"EGP\",\r\n    symbol: \"ج.م\",\r\n    locale: \"ar-EG\",\r\n    exchangeRate: 50.0, // Example rate - 31 EGP = 1 USD\r\n  },\r\n  AE: {\r\n    code: \"AED\",\r\n    symbol: \"د.إ\",\r\n    locale: \"ar-AE\",\r\n    exchangeRate: 3.67, // Example rate - 3.67 AED = 1 USD\r\n  },\r\n  SA: {\r\n    code: \"SAR\",\r\n    symbol: \"ر.س\",\r\n    locale: \"ar-SA\",\r\n    exchangeRate: 3.75, // Example rate - 3.75 SAR = 1 USD\r\n  },\r\n  JO: {\r\n    code: \"JOD\",\r\n    symbol: \"د.أ\",\r\n    locale: \"ar-JO\",\r\n    exchangeRate: 0.71, // Example rate - 0.71 JOD = 1 USD\r\n  },\r\n  US: {\r\n    code: \"USD\",\r\n    symbol: \"$\",\r\n    locale: \"en-US\",\r\n    exchangeRate: 1.0,\r\n  },\r\n  DEFAULT: {\r\n    code: \"USD\",\r\n    symbol: \"$\",\r\n    locale: \"en-US\",\r\n    exchangeRate: 1.0,\r\n  },\r\n};\r\n\r\n// Country code mapping based on browser location\r\nconst COUNTRY_CODE_MAPPING: Record<string, string> = {\r\n  Egypt: \"EG\",\r\n  \"United Arab Emirates\": \"AE\",\r\n  UAE: \"AE\",\r\n  \"Saudi Arabia\": \"SA\",\r\n  Jordan: \"JO\",\r\n  \"United States\": \"US\",\r\n  USA: \"US\",\r\n};\r\n\r\ninterface UserLocation {\r\n  country: string;\r\n  countryCode: string;\r\n  currency: CurrencyConfig;\r\n}\r\n\r\ninterface CurrencyFormatOptions {\r\n  showSymbol?: boolean;\r\n  showCode?: boolean;\r\n  locale?: \"en\" | \"ar\";\r\n}\r\n\r\nclass CurrencyManager {\r\n  private userLocation: UserLocation | null = null;\r\n  private exchangeRates: Record<string, number> = {};\r\n  private lastRateUpdate: Date | null = null;\r\n\r\n  // Cache for 1 hour\r\n  private readonly CACHE_DURATION = 60 * 60 * 1000;\r\n\r\n  /**\r\n   * Initialize currency manager and detect user location\r\n   */\r\n  async init(): Promise<void> {\r\n    try {\r\n      // Try to get cached location first\r\n      const cachedLocation = this.getCachedLocation();\r\n      if (cachedLocation) {\r\n        this.userLocation = cachedLocation;\r\n        return;\r\n      }\r\n\r\n      // Detect user location\r\n      const location = await this.detectUserLocation();\r\n      this.userLocation = location;\r\n\r\n      // Cache the location\r\n      this.setCachedLocation(location);\r\n\r\n      // Update exchange rates\r\n      await this.updateExchangeRates();\r\n    } catch (error) {\r\n      console.error(\"Failed to initialize currency manager:\", error);\r\n      // Fallback to default currency\r\n      this.userLocation = {\r\n        country: \"Default\",\r\n        countryCode: \"DEFAULT\",\r\n        currency: CURRENCY_CONFIGS.DEFAULT,\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Detect user location using multiple methods\r\n   */\r\n  private async detectUserLocation(): Promise<UserLocation> {\r\n    // Method 1: Try geolocation API with reverse geocoding\r\n    try {\r\n      const geoLocation = await this.getGeolocation();\r\n      const country = await this.reverseGeocode(\r\n        geoLocation.latitude,\r\n        geoLocation.longitude\r\n      );\r\n      return this.createLocationFromCountry(country);\r\n    } catch (error) {\r\n      console.log(\"Geolocation failed, trying IP-based detection\");\r\n    }\r\n\r\n    // Method 2: Try IP-based location detection\r\n    try {\r\n      const ipLocation = await this.getLocationFromIP();\r\n      return this.createLocationFromCountry(ipLocation.country);\r\n    } catch (error) {\r\n      console.log(\"IP-based location failed, trying browser locale\");\r\n    }\r\n\r\n    // Method 3: Fallback to browser locale\r\n    try {\r\n      const browserCountry = this.getCountryFromBrowserLocale();\r\n      return this.createLocationFromCountry(browserCountry);\r\n    } catch (error) {\r\n      console.log(\"Browser locale failed, using default\");\r\n    }\r\n\r\n    // Method 4: Default fallback\r\n    return {\r\n      country: \"Default\",\r\n      countryCode: \"DEFAULT\",\r\n      currency: CURRENCY_CONFIGS.DEFAULT,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get user location using browser geolocation API\r\n   */\r\n  private getGeolocation(): Promise<{ latitude: number; longitude: number }> {\r\n    return new Promise((resolve, reject) => {\r\n      if (!navigator.geolocation) {\r\n        reject(new Error(\"Geolocation is not supported\"));\r\n        return;\r\n      }\r\n\r\n      navigator.geolocation.getCurrentPosition(\r\n        (position) => {\r\n          resolve({\r\n            latitude: position.coords.latitude,\r\n            longitude: position.coords.longitude,\r\n          });\r\n        },\r\n        (error) => {\r\n          reject(error);\r\n        },\r\n        {\r\n          timeout: 10000,\r\n          maximumAge: 600000, // 10 minutes\r\n          enableHighAccuracy: false,\r\n        }\r\n      );\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Reverse geocode coordinates to get country\r\n   */\r\n  private async reverseGeocode(lat: number, lng: number): Promise<string> {\r\n    const response = await fetch(\r\n      `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${lat}&longitude=${lng}&localityLanguage=en`\r\n    );\r\n\r\n    if (!response.ok) {\r\n      throw new Error(\"Reverse geocoding failed\");\r\n    }\r\n\r\n    const data = await response.json();\r\n    return data.countryName || \"Default\";\r\n  }\r\n\r\n  /**\r\n   * Get location from IP address\r\n   */\r\n  private async getLocationFromIP(): Promise<{ country: string }> {\r\n    const response = await fetch(\"https://ipapi.co/json/\");\r\n\r\n    if (!response.ok) {\r\n      throw new Error(\"IP-based location detection failed\");\r\n    }\r\n\r\n    const data = await response.json();\r\n    return { country: data.country_name || \"Default\" };\r\n  }\r\n\r\n  /**\r\n   * Get country from browser locale\r\n   */\r\n  private getCountryFromBrowserLocale(): string {\r\n    const locale = navigator.language || navigator.languages?.[0] || \"en-US\";\r\n\r\n    // Extract country code from locale (e.g., 'ar-SA' -> 'SA')\r\n    const countryCode = locale.split(\"-\")[1];\r\n\r\n    if (countryCode) {\r\n      // Map country codes to full names\r\n      const countryMap: Record<string, string> = {\r\n        EG: \"Egypt\",\r\n        AE: \"United Arab Emirates\",\r\n        SA: \"Saudi Arabia\",\r\n        JO: \"Jordan\",\r\n        US: \"United States\",\r\n      };\r\n\r\n      return countryMap[countryCode] || \"Default\";\r\n    }\r\n\r\n    return \"Default\";\r\n  }\r\n\r\n  /**\r\n   * Create location object from country name\r\n   */\r\n  private createLocationFromCountry(country: string): UserLocation {\r\n    const countryCode = COUNTRY_CODE_MAPPING[country] || \"DEFAULT\";\r\n    const currency = CURRENCY_CONFIGS[countryCode] || CURRENCY_CONFIGS.DEFAULT;\r\n\r\n    return {\r\n      country,\r\n      countryCode,\r\n      currency,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Update exchange rates from external API\r\n   */\r\n  private async updateExchangeRates(): Promise<void> {\r\n    try {\r\n      // Check if rates are still fresh\r\n      if (\r\n        this.lastRateUpdate &&\r\n        Date.now() - this.lastRateUpdate.getTime() < this.CACHE_DURATION\r\n      ) {\r\n        return;\r\n      }\r\n\r\n      // Use a free exchange rate API\r\n      const response = await fetch(\r\n        \"https://api.exchangerate-api.com/v4/latest/USD\"\r\n      );\r\n\r\n      if (!response.ok) {\r\n        throw new Error(\"Failed to fetch exchange rates\");\r\n      }\r\n\r\n      const data = await response.json();\r\n      this.exchangeRates = data.rates;\r\n      this.lastRateUpdate = new Date();\r\n\r\n      // Update currency configs with live rates\r\n      Object.keys(CURRENCY_CONFIGS).forEach((countryCode) => {\r\n        const config = CURRENCY_CONFIGS[countryCode];\r\n        if (this.exchangeRates[config.code]) {\r\n          config.exchangeRate = this.exchangeRates[config.code];\r\n        }\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Failed to update exchange rates:\", error);\r\n      // Continue with cached/default rates\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Convert price from USD to user's local currency\r\n   */\r\n  convertPrice(usdPrice: number): number {\r\n    if (!this.userLocation) {\r\n      return usdPrice;\r\n    }\r\n\r\n    return usdPrice * this.userLocation.currency.exchangeRate;\r\n  }\r\n\r\n  /**\r\n   * Convert price from property's local currency to user's location currency\r\n   */\r\n  formatPropertyPrice(\r\n    localPrice: number | string,\r\n    propertyCountryCode: string,\r\n    options: CurrencyFormatOptions = {}\r\n  ): string {\r\n    const { showSymbol = true, showCode = false, locale = \"en\" } = options;\r\n\r\n    const numPrice =\r\n      typeof localPrice === \"string\" ? parseFloat(localPrice) : localPrice;\r\n    if (isNaN(numPrice)) return localPrice?.toString() || \"0\";\r\n\r\n    // Initialize exchange rates if not already done (async, non-blocking)\r\n    if (!this.lastRateUpdate) {\r\n      this.updateExchangeRates().catch(console.error);\r\n    }\r\n\r\n    // Get property's currency config\r\n    const propertyCurrency =\r\n      CURRENCY_CONFIGS[propertyCountryCode] || CURRENCY_CONFIGS.DEFAULT;\r\n\r\n    // Determine target currency based on user location\r\n    let targetCurrency: CurrencyConfig;\r\n    let formatLocale: string;\r\n\r\n    if (\r\n      this.userLocation &&\r\n      [\"EG\", \"AE\", \"SA\", \"JO\"].includes(this.userLocation.countryCode)\r\n    ) {\r\n      // User is in one of our supported countries\r\n      targetCurrency = this.userLocation.currency;\r\n      formatLocale = locale === \"ar\" ? targetCurrency.locale : \"en-US\";\r\n    } else {\r\n      // User location not in supported countries, use USD\r\n      targetCurrency = CURRENCY_CONFIGS.DEFAULT;\r\n      formatLocale = \"en-US\";\r\n    }\r\n\r\n    // Convert: Local Currency → USD → Target Currency\r\n    let convertedPrice: number;\r\n\r\n    if (propertyCurrency.code === targetCurrency.code) {\r\n      // Same currency, no conversion needed\r\n      convertedPrice = numPrice;\r\n    } else {\r\n      // Convert property local currency to USD first\r\n      const usdPrice = numPrice / propertyCurrency.exchangeRate;\r\n      // Then convert USD to target currency\r\n      convertedPrice = usdPrice * targetCurrency.exchangeRate;\r\n    }\r\n\r\n    if (showSymbol || showCode) {\r\n      // Format with currency\r\n      try {\r\n        const formatted = new Intl.NumberFormat(formatLocale, {\r\n          style: \"currency\",\r\n          currency: targetCurrency.code,\r\n          currencyDisplay: showCode ? \"code\" : \"symbol\",\r\n        }).format(convertedPrice);\r\n\r\n        return formatted;\r\n      } catch (error) {\r\n        // Fallback if currency is not supported\r\n        const numberFormatted = new Intl.NumberFormat(formatLocale).format(\r\n          convertedPrice\r\n        );\r\n        return showSymbol\r\n          ? `${targetCurrency.symbol}${numberFormatted}`\r\n          : numberFormatted;\r\n      }\r\n    } else {\r\n      // Format number only\r\n      return new Intl.NumberFormat(formatLocale).format(convertedPrice);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Format price according to user's locale and currency (for backward compatibility)\r\n   */\r\n  formatPrice(\r\n    usdPrice: number | string,\r\n    options: CurrencyFormatOptions = {}\r\n  ): string {\r\n    const { showSymbol = true, showCode = false, locale = \"en\" } = options;\r\n\r\n    if (!this.userLocation) {\r\n      // Fallback formatting\r\n      const numPrice =\r\n        typeof usdPrice === \"string\" ? parseFloat(usdPrice) : usdPrice;\r\n      if (isNaN(numPrice)) return usdPrice.toString();\r\n\r\n      return new Intl.NumberFormat(\"en-US\", {\r\n        style: \"currency\",\r\n        currency: \"USD\",\r\n      }).format(numPrice);\r\n    }\r\n\r\n    const numPrice =\r\n      typeof usdPrice === \"string\" ? parseFloat(usdPrice) : usdPrice;\r\n    if (isNaN(numPrice)) return usdPrice.toString();\r\n\r\n    // Convert to local currency\r\n    const localPrice = this.convertPrice(numPrice);\r\n    const currency = this.userLocation.currency;\r\n\r\n    // Use appropriate locale for formatting\r\n    const formatLocale = locale === \"ar\" ? currency.locale : \"en-US\";\r\n\r\n    if (showSymbol || showCode) {\r\n      // Format with currency\r\n      try {\r\n        const formatted = new Intl.NumberFormat(formatLocale, {\r\n          style: \"currency\",\r\n          currency: currency.code,\r\n          currencyDisplay: showCode ? \"code\" : \"symbol\",\r\n        }).format(localPrice);\r\n\r\n        return formatted;\r\n      } catch (error) {\r\n        // Fallback if currency is not supported\r\n        const numberFormatted = new Intl.NumberFormat(formatLocale).format(\r\n          localPrice\r\n        );\r\n        return showSymbol\r\n          ? `${currency.symbol}${numberFormatted}`\r\n          : numberFormatted;\r\n      }\r\n    } else {\r\n      // Format number only\r\n      return new Intl.NumberFormat(formatLocale).format(localPrice);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get user's currency information\r\n   */\r\n  getUserCurrency(): CurrencyConfig | null {\r\n    return this.userLocation?.currency || null;\r\n  }\r\n\r\n  /**\r\n   * Get user's location information\r\n   */\r\n  getUserLocation(): UserLocation | null {\r\n    return this.userLocation;\r\n  }\r\n\r\n  /**\r\n   * Cache location in localStorage\r\n   */\r\n  private setCachedLocation(location: UserLocation): void {\r\n    try {\r\n      const cacheData = {\r\n        location,\r\n        timestamp: Date.now(),\r\n      };\r\n      localStorage.setItem(\"userLocation\", JSON.stringify(cacheData));\r\n    } catch (error) {\r\n      console.error(\"Failed to cache location:\", error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get cached location from localStorage\r\n   */\r\n  private getCachedLocation(): UserLocation | null {\r\n    try {\r\n      const cached = localStorage.getItem(\"userLocation\");\r\n      if (!cached) return null;\r\n\r\n      const cacheData = JSON.parse(cached);\r\n\r\n      // Check if cache is still valid (24 hours)\r\n      if (Date.now() - cacheData.timestamp > 24 * 60 * 60 * 1000) {\r\n        localStorage.removeItem(\"userLocation\");\r\n        return null;\r\n      }\r\n\r\n      return cacheData.location;\r\n    } catch (error) {\r\n      console.error(\"Failed to get cached location:\", error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Force refresh location and rates\r\n   */\r\n  async refresh(): Promise<void> {\r\n    localStorage.removeItem(\"userLocation\");\r\n    this.userLocation = null;\r\n    this.lastRateUpdate = null;\r\n    await this.init();\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const currencyManager = new CurrencyManager();\r\n\r\n/**\r\n * Map property country name to currency country code\r\n */\r\nexport function getCountryCodeFromName(countryName: string): string {\r\n  // Handle common variations and mappings\r\n  const normalizedCountry = countryName?.toUpperCase().trim();\r\n\r\n  const countryMappings: Record<string, string> = {\r\n    EGYPT: \"EG\",\r\n    EG: \"EG\",\r\n    \"UNITED ARAB EMIRATES\": \"AE\",\r\n    UAE: \"AE\",\r\n    AE: \"AE\",\r\n    \"SAUDI ARABIA\": \"SA\",\r\n    SA: \"SA\",\r\n    JORDAN: \"JO\",\r\n    JO: \"JO\",\r\n    \"UNITED STATES\": \"US\",\r\n    USA: \"US\",\r\n    US: \"US\",\r\n  };\r\n\r\n  return countryMappings[normalizedCountry] || \"DEFAULT\";\r\n}\r\n\r\n// Export hook for React components\r\nexport function useCurrency() {\r\n  return {\r\n    formatPrice: (price: number | string, options?: CurrencyFormatOptions) =>\r\n      currencyManager.formatPrice(price, options),\r\n    formatPropertyPrice: (\r\n      price: number | string,\r\n      countryCode: string,\r\n      options?: CurrencyFormatOptions\r\n    ) => currencyManager.formatPropertyPrice(price, countryCode, options),\r\n    getUserCurrency: () => currencyManager.getUserCurrency(),\r\n    getUserLocation: () => currencyManager.getUserLocation(),\r\n    refresh: () => currencyManager.refresh(),\r\n  };\r\n}\r\n\r\n// Auto-initialize when module loads\r\nif (typeof window !== \"undefined\") {\r\n  currencyManager.init().catch(console.error);\r\n}\r\n"], "names": [], "mappings": "AAAA,yDAAyD;;;;;;AASzD,kDAAkD;AAClD,MAAM,mBAAmD;IACvD,IAAI;QACF,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,cAAc;IAChB;IACA,IAAI;QACF,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,cAAc;IAChB;IACA,IAAI;QACF,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,cAAc;IAChB;IACA,IAAI;QACF,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,cAAc;IAChB;IACA,IAAI;QACF,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,cAAc;IAChB;IACA,SAAS;QACP,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,cAAc;IAChB;AACF;AAEA,iDAAiD;AACjD,MAAM,uBAA+C;IACnD,OAAO;IACP,wBAAwB;IACxB,KAAK;IACL,gBAAgB;IAChB,QAAQ;IACR,iBAAiB;IACjB,KAAK;AACP;AAcA,MAAM;IACI,eAAoC,KAAK;IACzC,gBAAwC,CAAC,EAAE;IAC3C,iBAA8B,KAAK;IAE3C,mBAAmB;IACF,iBAAiB,KAAK,KAAK,KAAK;IAEjD;;GAEC,GACD,MAAM,OAAsB;QAC1B,IAAI;YACF,mCAAmC;YACnC,MAAM,iBAAiB,IAAI,CAAC,iBAAiB;YAC7C,IAAI,gBAAgB;gBAClB,IAAI,CAAC,YAAY,GAAG;gBACpB;YACF;YAEA,uBAAuB;YACvB,MAAM,WAAW,MAAM,IAAI,CAAC,kBAAkB;YAC9C,IAAI,CAAC,YAAY,GAAG;YAEpB,qBAAqB;YACrB,IAAI,CAAC,iBAAiB,CAAC;YAEvB,wBAAwB;YACxB,MAAM,IAAI,CAAC,mBAAmB;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,+BAA+B;YAC/B,IAAI,CAAC,YAAY,GAAG;gBAClB,SAAS;gBACT,aAAa;gBACb,UAAU,iBAAiB,OAAO;YACpC;QACF;IACF;IAEA;;GAEC,GACD,MAAc,qBAA4C;QACxD,uDAAuD;QACvD,IAAI;YACF,MAAM,cAAc,MAAM,IAAI,CAAC,cAAc;YAC7C,MAAM,UAAU,MAAM,IAAI,CAAC,cAAc,CACvC,YAAY,QAAQ,EACpB,YAAY,SAAS;YAEvB,OAAO,IAAI,CAAC,yBAAyB,CAAC;QACxC,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC;QACd;QAEA,4CAA4C;QAC5C,IAAI;YACF,MAAM,aAAa,MAAM,IAAI,CAAC,iBAAiB;YAC/C,OAAO,IAAI,CAAC,yBAAyB,CAAC,WAAW,OAAO;QAC1D,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC;QACd;QAEA,uCAAuC;QACvC,IAAI;YACF,MAAM,iBAAiB,IAAI,CAAC,2BAA2B;YACvD,OAAO,IAAI,CAAC,yBAAyB,CAAC;QACxC,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC;QACd;QAEA,6BAA6B;QAC7B,OAAO;YACL,SAAS;YACT,aAAa;YACb,UAAU,iBAAiB,OAAO;QACpC;IACF;IAEA;;GAEC,GACD,AAAQ,iBAAmE;QACzE,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,IAAI,CAAC,UAAU,WAAW,EAAE;gBAC1B,OAAO,IAAI,MAAM;gBACjB;YACF;YAEA,UAAU,WAAW,CAAC,kBAAkB,CACtC,CAAC;gBACC,QAAQ;oBACN,UAAU,SAAS,MAAM,CAAC,QAAQ;oBAClC,WAAW,SAAS,MAAM,CAAC,SAAS;gBACtC;YACF,GACA,CAAC;gBACC,OAAO;YACT,GACA;gBACE,SAAS;gBACT,YAAY;gBACZ,oBAAoB;YACtB;QAEJ;IACF;IAEA;;GAEC,GACD,MAAc,eAAe,GAAW,EAAE,GAAW,EAAmB;QACtE,MAAM,WAAW,MAAM,MACrB,CAAC,kEAAkE,EAAE,IAAI,WAAW,EAAE,IAAI,oBAAoB,CAAC;QAGjH,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,WAAW,IAAI;IAC7B;IAEA;;GAEC,GACD,MAAc,oBAAkD;QAC9D,MAAM,WAAW,MAAM,MAAM;QAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;YAAE,SAAS,KAAK,YAAY,IAAI;QAAU;IACnD;IAEA;;GAEC,GACD,AAAQ,8BAAsC;QAC5C,MAAM,SAAS,UAAU,QAAQ,IAAI,UAAU,SAAS,EAAE,CAAC,EAAE,IAAI;QAEjE,2DAA2D;QAC3D,MAAM,cAAc,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE;QAExC,IAAI,aAAa;YACf,kCAAkC;YAClC,MAAM,aAAqC;gBACzC,IAAI;gBACJ,IAAI;gBACJ,IAAI;gBACJ,IAAI;gBACJ,IAAI;YACN;YAEA,OAAO,UAAU,CAAC,YAAY,IAAI;QACpC;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,0BAA0B,OAAe,EAAgB;QAC/D,MAAM,cAAc,oBAAoB,CAAC,QAAQ,IAAI;QACrD,MAAM,WAAW,gBAAgB,CAAC,YAAY,IAAI,iBAAiB,OAAO;QAE1E,OAAO;YACL;YACA;YACA;QACF;IACF;IAEA;;GAEC,GACD,MAAc,sBAAqC;QACjD,IAAI;YACF,iCAAiC;YACjC,IACE,IAAI,CAAC,cAAc,IACnB,KAAK,GAAG,KAAK,IAAI,CAAC,cAAc,CAAC,OAAO,KAAK,IAAI,CAAC,cAAc,EAChE;gBACA;YACF;YAEA,+BAA+B;YAC/B,MAAM,WAAW,MAAM,MACrB;YAGF,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,CAAC,aAAa,GAAG,KAAK,KAAK;YAC/B,IAAI,CAAC,cAAc,GAAG,IAAI;YAE1B,0CAA0C;YAC1C,OAAO,IAAI,CAAC,kBAAkB,OAAO,CAAC,CAAC;gBACrC,MAAM,SAAS,gBAAgB,CAAC,YAAY;gBAC5C,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,IAAI,CAAC,EAAE;oBACnC,OAAO,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,IAAI,CAAC;gBACvD;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,qCAAqC;QACvC;IACF;IAEA;;GAEC,GACD,aAAa,QAAgB,EAAU;QACrC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,OAAO;QACT;QAEA,OAAO,WAAW,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,YAAY;IAC3D;IAEA;;GAEC,GACD,oBACE,UAA2B,EAC3B,mBAA2B,EAC3B,UAAiC,CAAC,CAAC,EAC3B;QACR,MAAM,EAAE,aAAa,IAAI,EAAE,WAAW,KAAK,EAAE,SAAS,IAAI,EAAE,GAAG;QAE/D,MAAM,WACJ,OAAO,eAAe,WAAW,WAAW,cAAc;QAC5D,IAAI,MAAM,WAAW,OAAO,YAAY,cAAc;QAEtD,sEAAsE;QACtE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC,QAAQ,KAAK;QAChD;QAEA,iCAAiC;QACjC,MAAM,mBACJ,gBAAgB,CAAC,oBAAoB,IAAI,iBAAiB,OAAO;QAEnE,mDAAmD;QACnD,IAAI;QACJ,IAAI;QAEJ,IACE,IAAI,CAAC,YAAY,IACjB;YAAC;YAAM;YAAM;YAAM;SAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,GAC/D;YACA,4CAA4C;YAC5C,iBAAiB,IAAI,CAAC,YAAY,CAAC,QAAQ;YAC3C,eAAe,WAAW,OAAO,eAAe,MAAM,GAAG;QAC3D,OAAO;YACL,oDAAoD;YACpD,iBAAiB,iBAAiB,OAAO;YACzC,eAAe;QACjB;QAEA,kDAAkD;QAClD,IAAI;QAEJ,IAAI,iBAAiB,IAAI,KAAK,eAAe,IAAI,EAAE;YACjD,sCAAsC;YACtC,iBAAiB;QACnB,OAAO;YACL,+CAA+C;YAC/C,MAAM,WAAW,WAAW,iBAAiB,YAAY;YACzD,sCAAsC;YACtC,iBAAiB,WAAW,eAAe,YAAY;QACzD;QAEA,IAAI,cAAc,UAAU;YAC1B,uBAAuB;YACvB,IAAI;gBACF,MAAM,YAAY,IAAI,KAAK,YAAY,CAAC,cAAc;oBACpD,OAAO;oBACP,UAAU,eAAe,IAAI;oBAC7B,iBAAiB,WAAW,SAAS;gBACvC,GAAG,MAAM,CAAC;gBAEV,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,wCAAwC;gBACxC,MAAM,kBAAkB,IAAI,KAAK,YAAY,CAAC,cAAc,MAAM,CAChE;gBAEF,OAAO,aACH,GAAG,eAAe,MAAM,GAAG,iBAAiB,GAC5C;YACN;QACF,OAAO;YACL,qBAAqB;YACrB,OAAO,IAAI,KAAK,YAAY,CAAC,cAAc,MAAM,CAAC;QACpD;IACF;IAEA;;GAEC,GACD,YACE,QAAyB,EACzB,UAAiC,CAAC,CAAC,EAC3B;QACR,MAAM,EAAE,aAAa,IAAI,EAAE,WAAW,KAAK,EAAE,SAAS,IAAI,EAAE,GAAG;QAE/D,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,sBAAsB;YACtB,MAAM,WACJ,OAAO,aAAa,WAAW,WAAW,YAAY;YACxD,IAAI,MAAM,WAAW,OAAO,SAAS,QAAQ;YAE7C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;gBACpC,OAAO;gBACP,UAAU;YACZ,GAAG,MAAM,CAAC;QACZ;QAEA,MAAM,WACJ,OAAO,aAAa,WAAW,WAAW,YAAY;QACxD,IAAI,MAAM,WAAW,OAAO,SAAS,QAAQ;QAE7C,4BAA4B;QAC5B,MAAM,aAAa,IAAI,CAAC,YAAY,CAAC;QACrC,MAAM,WAAW,IAAI,CAAC,YAAY,CAAC,QAAQ;QAE3C,wCAAwC;QACxC,MAAM,eAAe,WAAW,OAAO,SAAS,MAAM,GAAG;QAEzD,IAAI,cAAc,UAAU;YAC1B,uBAAuB;YACvB,IAAI;gBACF,MAAM,YAAY,IAAI,KAAK,YAAY,CAAC,cAAc;oBACpD,OAAO;oBACP,UAAU,SAAS,IAAI;oBACvB,iBAAiB,WAAW,SAAS;gBACvC,GAAG,MAAM,CAAC;gBAEV,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,wCAAwC;gBACxC,MAAM,kBAAkB,IAAI,KAAK,YAAY,CAAC,cAAc,MAAM,CAChE;gBAEF,OAAO,aACH,GAAG,SAAS,MAAM,GAAG,iBAAiB,GACtC;YACN;QACF,OAAO;YACL,qBAAqB;YACrB,OAAO,IAAI,KAAK,YAAY,CAAC,cAAc,MAAM,CAAC;QACpD;IACF;IAEA;;GAEC,GACD,kBAAyC;QACvC,OAAO,IAAI,CAAC,YAAY,EAAE,YAAY;IACxC;IAEA;;GAEC,GACD,kBAAuC;QACrC,OAAO,IAAI,CAAC,YAAY;IAC1B;IAEA;;GAEC,GACD,AAAQ,kBAAkB,QAAsB,EAAQ;QACtD,IAAI;YACF,MAAM,YAAY;gBAChB;gBACA,WAAW,KAAK,GAAG;YACrB;YACA,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;QACtD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA;;GAEC,GACD,AAAQ,oBAAyC;QAC/C,IAAI;YACF,MAAM,SAAS,aAAa,OAAO,CAAC;YACpC,IAAI,CAAC,QAAQ,OAAO;YAEpB,MAAM,YAAY,KAAK,KAAK,CAAC;YAE7B,2CAA2C;YAC3C,IAAI,KAAK,GAAG,KAAK,UAAU,SAAS,GAAG,KAAK,KAAK,KAAK,MAAM;gBAC1D,aAAa,UAAU,CAAC;gBACxB,OAAO;YACT;YAEA,OAAO,UAAU,QAAQ;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAM,UAAyB;QAC7B,aAAa,UAAU,CAAC;QACxB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,cAAc,GAAG;QACtB,MAAM,IAAI,CAAC,IAAI;IACjB;AACF;AAGO,MAAM,kBAAkB,IAAI;AAK5B,SAAS,uBAAuB,WAAmB;IACxD,wCAAwC;IACxC,MAAM,oBAAoB,aAAa,cAAc;IAErD,MAAM,kBAA0C;QAC9C,OAAO;QACP,IAAI;QACJ,wBAAwB;QACxB,KAAK;QACL,IAAI;QACJ,gBAAgB;QAChB,IAAI;QACJ,QAAQ;QACR,IAAI;QACJ,iBAAiB;QACjB,KAAK;QACL,IAAI;IACN;IAEA,OAAO,eAAe,CAAC,kBAAkB,IAAI;AAC/C;AAGO,SAAS;IACd,OAAO;QACL,aAAa,CAAC,OAAwB,UACpC,gBAAgB,WAAW,CAAC,OAAO;QACrC,qBAAqB,CACnB,OACA,aACA,UACG,gBAAgB,mBAAmB,CAAC,OAAO,aAAa;QAC7D,iBAAiB,IAAM,gBAAgB,eAAe;QACtD,iBAAiB,IAAM,gBAAgB,eAAe;QACtD,SAAS,IAAM,gBAAgB,OAAO;IACxC;AACF;AAEA,oCAAoC;AACpC,wCAAmC;IACjC,gBAAgB,IAAI,GAAG,KAAK,CAAC,QAAQ,KAAK;AAC5C", "debugId": null}}, {"offset": {"line": 1077, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/hooks/useCurrencyState.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useCallback } from \"react\";\r\nimport { currencyManager, getCountryCodeFromName } from \"@/lib/currency\";\r\n\r\ninterface CurrencyConfig {\r\n  code: string;\r\n  symbol: string;\r\n  locale: string;\r\n  exchangeRate: number;\r\n}\r\n\r\ninterface UserLocation {\r\n  country: string;\r\n  countryCode: string;\r\n  currency: CurrencyConfig;\r\n}\r\n\r\ninterface CurrencyFormatOptions {\r\n  showSymbol?: boolean;\r\n  showCode?: boolean;\r\n  locale?: \"en\" | \"ar\";\r\n}\r\n\r\ninterface CurrencyState {\r\n  isLoading: boolean;\r\n  userLocation: UserLocation | null;\r\n  userCurrency: CurrencyConfig | null;\r\n  error: string | null;\r\n}\r\n\r\n/**\r\n * React hook for managing currency state and formatting\r\n */\r\nexport function useCurrencyState() {\r\n  const [state, setState] = useState<CurrencyState>({\r\n    isLoading: true,\r\n    userLocation: null,\r\n    userCurrency: null,\r\n    error: null,\r\n  });\r\n\r\n  // Initialize currency manager and update state\r\n  const initializeCurrency = useCallback(async () => {\r\n    try {\r\n      setState((prev) => ({ ...prev, isLoading: true, error: null }));\r\n\r\n      // Initialize currency manager\r\n      await currencyManager.init();\r\n\r\n      // Get location and currency info\r\n      const location = currencyManager.getUserLocation();\r\n      const currency = currencyManager.getUserCurrency();\r\n\r\n      setState({\r\n        isLoading: false,\r\n        userLocation: location,\r\n        userCurrency: currency,\r\n        error: null,\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Failed to initialize currency:\", error);\r\n      setState({\r\n        isLoading: false,\r\n        userLocation: null,\r\n        userCurrency: null,\r\n        error:\r\n          error instanceof Error\r\n            ? error.message\r\n            : \"Failed to initialize currency\",\r\n      });\r\n    }\r\n  }, []);\r\n\r\n  // Format price using the currency manager (user location-based)\r\n  const formatPrice = useCallback(\r\n    (price: number | string, options: CurrencyFormatOptions = {}): string => {\r\n      return currencyManager.formatPrice(price, options);\r\n    },\r\n    []\r\n  );\r\n\r\n  // Format price based on property country (property currency -> user currency)\r\n  const formatPropertyPrice = useCallback(\r\n    (\r\n      price: number | string,\r\n      countryName: string,\r\n      options: CurrencyFormatOptions = {}\r\n    ): string => {\r\n      const countryCode = getCountryCodeFromName(countryName);\r\n      return currencyManager.formatPropertyPrice(price, countryCode, options);\r\n    },\r\n    []\r\n  );\r\n\r\n  // Refresh currency data\r\n  const refresh = useCallback(async () => {\r\n    await currencyManager.refresh();\r\n    await initializeCurrency();\r\n  }, [initializeCurrency]);\r\n\r\n  // Initialize on mount\r\n  useEffect(() => {\r\n    initializeCurrency();\r\n  }, [initializeCurrency]);\r\n\r\n  return {\r\n    // State\r\n    isLoading: state.isLoading,\r\n    userLocation: state.userLocation,\r\n    userCurrency: state.userCurrency,\r\n    error: state.error,\r\n\r\n    // Methods\r\n    formatPrice,\r\n    formatPropertyPrice,\r\n    refresh,\r\n\r\n    // Utility getters\r\n    isReady: !state.isLoading && !state.error && state.userCurrency !== null,\r\n    currencyCode: state.userCurrency?.code || \"USD\",\r\n    currencySymbol: state.userCurrency?.symbol || \"$\",\r\n    countryCode: state.userLocation?.countryCode || \"DEFAULT\",\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AACA;;AAHA;;;AAkCO,SAAS;;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QAChD,WAAW;QACX,cAAc;QACd,cAAc;QACd,OAAO;IACT;IAEA,+CAA+C;IAC/C,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YACrC,IAAI;gBACF;wEAAS,CAAC,OAAS,CAAC;4BAAE,GAAG,IAAI;4BAAE,WAAW;4BAAM,OAAO;wBAAK,CAAC;;gBAE7D,8BAA8B;gBAC9B,MAAM,yHAAA,CAAA,kBAAe,CAAC,IAAI;gBAE1B,iCAAiC;gBACjC,MAAM,WAAW,yHAAA,CAAA,kBAAe,CAAC,eAAe;gBAChD,MAAM,WAAW,yHAAA,CAAA,kBAAe,CAAC,eAAe;gBAEhD,SAAS;oBACP,WAAW;oBACX,cAAc;oBACd,cAAc;oBACd,OAAO;gBACT;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,SAAS;oBACP,WAAW;oBACX,cAAc;oBACd,cAAc;oBACd,OACE,iBAAiB,QACb,MAAM,OAAO,GACb;gBACR;YACF;QACF;2DAAG,EAAE;IAEL,gEAAgE;IAChE,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAC5B,CAAC,OAAwB,UAAiC,CAAC,CAAC;YAC1D,OAAO,yHAAA,CAAA,kBAAe,CAAC,WAAW,CAAC,OAAO;QAC5C;oDACA,EAAE;IAGJ,8EAA8E;IAC9E,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DACpC,CACE,OACA,aACA,UAAiC,CAAC,CAAC;YAEnC,MAAM,cAAc,CAAA,GAAA,yHAAA,CAAA,yBAAsB,AAAD,EAAE;YAC3C,OAAO,yHAAA,CAAA,kBAAe,CAAC,mBAAmB,CAAC,OAAO,aAAa;QACjE;4DACA,EAAE;IAGJ,wBAAwB;IACxB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YAC1B,MAAM,yHAAA,CAAA,kBAAe,CAAC,OAAO;YAC7B,MAAM;QACR;gDAAG;QAAC;KAAmB;IAEvB,sBAAsB;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR;QACF;qCAAG;QAAC;KAAmB;IAEvB,OAAO;QACL,QAAQ;QACR,WAAW,MAAM,SAAS;QAC1B,cAAc,MAAM,YAAY;QAChC,cAAc,MAAM,YAAY;QAChC,OAAO,MAAM,KAAK;QAElB,UAAU;QACV;QACA;QACA;QAEA,kBAAkB;QAClB,SAAS,CAAC,MAAM,SAAS,IAAI,CAAC,MAAM,KAAK,IAAI,MAAM,YAAY,KAAK;QACpE,cAAc,MAAM,YAAY,EAAE,QAAQ;QAC1C,gBAAgB,MAAM,YAAY,EAAE,UAAU;QAC9C,aAAa,MAAM,YAAY,EAAE,eAAe;IAClD;AACF;GA1FgB", "debugId": null}}, {"offset": {"line": 1184, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/actions/properties.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { del, get, patch, post } from \"@/services/api\";\r\n\r\ninterface Property {\r\n  title: string;\r\n  images: string[];\r\n  city: string;\r\n  country: string;\r\n  neighborhood: string;\r\n  latitude: string;\r\n  longitude: string;\r\n  address: string;\r\n  description: string;\r\n  type: string;\r\n  roomType: string;\r\n  genderRequired: string;\r\n  totalRooms: string;\r\n  availableRooms: string;\r\n  price: string;\r\n  size: string;\r\n  floor: string;\r\n  bathrooms: string;\r\n  separatedBathroom: boolean;\r\n  residentsCount: string;\r\n  availablePersons: string;\r\n  rentTime: string;\r\n  paymentTime: string;\r\n  priceIncludeWaterAndElectricity: boolean;\r\n  allowSmoking: boolean;\r\n  includeFurniture: boolean;\r\n  airConditioning: boolean;\r\n  includeWaterHeater: boolean;\r\n  parking: boolean;\r\n  internet: boolean;\r\n  nearToMetro: boolean;\r\n  nearToMarket: boolean;\r\n  elevator: boolean;\r\n  trialPeriod: boolean;\r\n  goodForForeigners: boolean;\r\n  categoryId: string;\r\n  termsAndConditions: string;\r\n}\r\n\r\nexport const createProperty = async (data: Property) => {\r\n  console.log(\"create property data\", data);\r\n  const response = await post(\"/properties\", data);\r\n  console.log(\"create property response\", response);\r\n  return response;\r\n};\r\n\r\nexport const getProperties = async ({\r\n  page = 1,\r\n  limit = 12,\r\n  title = \"\",\r\n  city = \"\",\r\n  country = \"\",\r\n  neighborhood = \"\",\r\n  address = \"\",\r\n  type = \"\",\r\n  roomType = \"\",\r\n  genderRequired = \"\",\r\n  totalRooms = \"\",\r\n  availableRooms = \"\",\r\n  size = \"\",\r\n  floor = \"\",\r\n  bathrooms = \"\",\r\n  separatedBathroom = false,\r\n  residentsCount = \"\",\r\n  availablePersons = \"\",\r\n  minPrice = \"\",\r\n  maxPrice = \"\",\r\n  rentTime = \"\",\r\n  paymentTime = \"\",\r\n  priceIncludeWaterAndElectricity = false,\r\n  minRating = 0,\r\n  minTotalRatings = 0,\r\n  isVerified = false,\r\n  isAvailable = false,\r\n  categoryId = \"\",\r\n  allowSmoking = false,\r\n  includeFurniture = false,\r\n  airConditioning = false,\r\n  includeWaterHeater = false,\r\n  parking = false,\r\n  internet = false,\r\n  nearToMetro = false,\r\n  nearToMarket = false,\r\n  elevator = false,\r\n  trialPeriod = false,\r\n  goodForForeigners = false,\r\n  sortBy = \"createdAt\",\r\n  sortOrder = \"asc\",\r\n}: {\r\n  page?: number;\r\n  limit?: number;\r\n  title?: string;\r\n  city?: string;\r\n  country?: string;\r\n  neighborhood?: string;\r\n  address?: string;\r\n  latitude?: string;\r\n  longitude?: string;\r\n  type?: string;\r\n  roomType?: string;\r\n  genderRequired?: string;\r\n  totalRooms?: string;\r\n  availableRooms?: string;\r\n  size?: string;\r\n  floor?: string;\r\n  bathrooms?: string;\r\n  separatedBathroom?: boolean;\r\n  residentsCount?: string;\r\n  availablePersons?: string;\r\n  minPrice?: string;\r\n  maxPrice?: string;\r\n  rentTime?: string;\r\n  paymentTime?: string;\r\n  priceIncludeWaterAndElectricity?: boolean;\r\n  minRating?: number;\r\n  minTotalRatings?: number;\r\n  isVerified?: boolean;\r\n  isAvailable?: boolean;\r\n  categoryId?: string;\r\n  allowSmoking?: boolean;\r\n  includeFurniture?: boolean;\r\n  airConditioning?: boolean;\r\n  includeWaterHeater?: boolean;\r\n  parking?: boolean;\r\n  internet?: boolean;\r\n  nearToMetro?: boolean;\r\n  nearToMarket?: boolean;\r\n  elevator?: boolean;\r\n  trialPeriod?: boolean;\r\n  goodForForeigners?: boolean;\r\n  sortBy?: \"createdAt\" | \"updatedAt\" | \"price\" | \"rating\" | \"title\";\r\n  sortOrder?: \"asc\" | \"desc\";\r\n}) => {\r\n  const response = await get(\r\n    `/properties?page=${page}&limit=${limit}&${title ? `title=${title}&` : \"\"}${\r\n      city ? `city=${city}&` : \"\"\r\n    }${country ? `country=${country}&` : \"\"}${\r\n      neighborhood ? `neighborhood=${neighborhood}&` : \"\"\r\n    }${address ? `address=${address}&` : \"\"}${type ? `type=${type}&` : \"\"}${\r\n      roomType ? `roomType=${roomType}&` : \"\"\r\n    }${genderRequired ? `genderRequired=${genderRequired}&` : \"\"}${\r\n      totalRooms ? `totalRooms=${totalRooms}&` : \"\"\r\n    }${availableRooms ? `availableRooms=${availableRooms}&` : \"\"}${\r\n      size ? `size=${size}&` : \"\"\r\n    }${floor ? `floor=${floor}&` : \"\"}${\r\n      bathrooms ? `bathrooms=${bathrooms}&` : \"\"\r\n    }${separatedBathroom ? `separatedBathroom=${separatedBathroom}&` : \"\"}${\r\n      residentsCount ? `residentsCount=${residentsCount}&` : \"\"\r\n    }${availablePersons ? `availablePersons=${availablePersons}&` : \"\"}${\r\n      rentTime ? `rentTime=${rentTime}&` : \"\"\r\n    }${paymentTime ? `paymentTime=${paymentTime}&` : \"\"}${\r\n      priceIncludeWaterAndElectricity\r\n        ? `priceIncludeWaterAndElectricity=${priceIncludeWaterAndElectricity}&`\r\n        : \"\"\r\n    }${allowSmoking ? `allowSmoking=${allowSmoking}&` : \"\"}${\r\n      includeFurniture ? `includeFurniture=${includeFurniture}&` : \"\"\r\n    }${airConditioning ? `airConditioning=${airConditioning}&` : \"\"}${\r\n      includeWaterHeater ? `includeWaterHeater=${includeWaterHeater}&` : \"\"\r\n    }${parking ? `parking=${parking}&` : \"\"}${\r\n      internet ? `internet=${internet}&` : \"\"\r\n    }${nearToMetro ? `nearToMetro=${nearToMetro}&` : \"\"}${\r\n      nearToMarket ? `nearToMarket=${nearToMarket}&` : \"\"\r\n    }${elevator ? `elevator=${elevator}&` : \"\"}${\r\n      trialPeriod ? `trialPeriod=${trialPeriod}&` : \"\"\r\n    }${goodForForeigners ? `goodForForeigners=${goodForForeigners}&` : \"\"}${\r\n      categoryId ? `categoryId=${categoryId}&` : \"\"\r\n    }${minRating ? `minRating=${minRating}&` : \"\"}${\r\n      minTotalRatings ? `minTotalRatings=${minTotalRatings}&` : \"\"\r\n    }${isVerified ? `isVerified=${isVerified}&` : \"\"}${\r\n      minPrice ? `minPrice=${minPrice}&` : \"\"\r\n    }${maxPrice ? `maxPrice=${maxPrice}&` : \"\"}${\r\n      isAvailable ? `isAvailable=${isAvailable}&` : \"\"\r\n    }${sortBy ? `sortBy=${sortBy}&` : \"\"}${\r\n      sortOrder ? `sortOrder=${sortOrder}` : \"\"\r\n    }`\r\n  );\r\n\r\n  return response;\r\n};\r\n\r\nexport const getMyProperties = async () => {\r\n  const response = await get(`/properties/my-properties`);\r\n  return response;\r\n};\r\n\r\nexport const getProperty = async (slug: string) => {\r\n  const response = await get(`/properties/slug/${slug}`);\r\n  return response;\r\n};\r\n\r\nexport const getPropertiesByUserId = async (userId: string) => {\r\n  const response = await get(`/properties/user/${userId}`);\r\n  return response;\r\n};\r\n\r\nexport const getFavoriteProperties = async () => {\r\n  const response = await get(`/properties/favorites`);\r\n  return response;\r\n};\r\n\r\nexport const toggleFavorite = async (id: string) => {\r\n  const response = await patch(`/properties/${id}/toggle-favorite`);\r\n  console.log(\"toggle favorite response\", response);\r\n  return response;\r\n};\r\n\r\nexport const updateProperty = async (data: Property) => {\r\n  const response = await patch(\"/properties\", data);\r\n  return response;\r\n};\r\n\r\nexport const deleteProperty = async (id: string) => {\r\n  const response = await del(`/properties/${id}`);\r\n  return response;\r\n};\r\n"], "names": [], "mappings": ";;;;;;IAwMa,wBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1200, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/hooks/useFavoriteProperties.ts"], "sourcesContent": ["import { useQuery } from \"@tanstack/react-query\";\r\nimport { getFavoriteProperties } from \"@/actions/properties\";\r\n\r\nexport function useFavoriteProperties() {\r\n  const {\r\n    data: favoritesResponse,\r\n    isLoading,\r\n    isError,\r\n    error,\r\n  } = useQuery({\r\n    queryKey: [\"favorites\", \"properties\"],\r\n    queryFn: getFavoriteProperties,\r\n    staleTime: 5 * 60 * 1000, // 5 minutes\r\n  });\r\n\r\n  // Extract the actual favorites data from the nested response structure\r\n  const favorites = favoritesResponse?.data?.data || [];\r\n\r\n  // Function to check if a property is in favorites\r\n  const isPropertyFavorite = (propertyId: string): boolean => {\r\n    return favorites.some((favorite: any) => favorite.id === propertyId);\r\n  };\r\n\r\n  // Function to get favorite property by ID\r\n  const getFavoriteProperty = (propertyId: string) => {\r\n    return favorites.find((favorite: any) => favorite.id === propertyId);\r\n  };\r\n\r\n  return {\r\n    favorites,\r\n    favoritesCount: favorites.length,\r\n    isLoading,\r\n    isError,\r\n    error,\r\n    isPropertyFavorite,\r\n    getFavoriteProperty,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;;AAEO,SAAS;;IACd,MAAM,EACJ,MAAM,iBAAiB,EACvB,SAAS,EACT,OAAO,EACP,KAAK,EACN,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACX,UAAU;YAAC;YAAa;SAAa;QACrC,SAAS,yJAAA,CAAA,wBAAqB;QAC9B,WAAW,IAAI,KAAK;IACtB;IAEA,uEAAuE;IACvE,MAAM,YAAY,mBAAmB,MAAM,QAAQ,EAAE;IAErD,kDAAkD;IAClD,MAAM,qBAAqB,CAAC;QAC1B,OAAO,UAAU,IAAI,CAAC,CAAC,WAAkB,SAAS,EAAE,KAAK;IAC3D;IAEA,0CAA0C;IAC1C,MAAM,sBAAsB,CAAC;QAC3B,OAAO,UAAU,IAAI,CAAC,CAAC,WAAkB,SAAS,EAAE,KAAK;IAC3D;IAEA,OAAO;QACL;QACA,gBAAgB,UAAU,MAAM;QAChC;QACA;QACA;QACA;QACA;IACF;AACF;GAlCgB;;QAMV,8KAAA,CAAA,WAAQ", "debugId": null}}, {"offset": {"line": 1252, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/actions/properties.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { del, get, patch, post } from \"@/services/api\";\r\n\r\ninterface Property {\r\n  title: string;\r\n  images: string[];\r\n  city: string;\r\n  country: string;\r\n  neighborhood: string;\r\n  latitude: string;\r\n  longitude: string;\r\n  address: string;\r\n  description: string;\r\n  type: string;\r\n  roomType: string;\r\n  genderRequired: string;\r\n  totalRooms: string;\r\n  availableRooms: string;\r\n  price: string;\r\n  size: string;\r\n  floor: string;\r\n  bathrooms: string;\r\n  separatedBathroom: boolean;\r\n  residentsCount: string;\r\n  availablePersons: string;\r\n  rentTime: string;\r\n  paymentTime: string;\r\n  priceIncludeWaterAndElectricity: boolean;\r\n  allowSmoking: boolean;\r\n  includeFurniture: boolean;\r\n  airConditioning: boolean;\r\n  includeWaterHeater: boolean;\r\n  parking: boolean;\r\n  internet: boolean;\r\n  nearToMetro: boolean;\r\n  nearToMarket: boolean;\r\n  elevator: boolean;\r\n  trialPeriod: boolean;\r\n  goodForForeigners: boolean;\r\n  categoryId: string;\r\n  termsAndConditions: string;\r\n}\r\n\r\nexport const createProperty = async (data: Property) => {\r\n  console.log(\"create property data\", data);\r\n  const response = await post(\"/properties\", data);\r\n  console.log(\"create property response\", response);\r\n  return response;\r\n};\r\n\r\nexport const getProperties = async ({\r\n  page = 1,\r\n  limit = 12,\r\n  title = \"\",\r\n  city = \"\",\r\n  country = \"\",\r\n  neighborhood = \"\",\r\n  address = \"\",\r\n  type = \"\",\r\n  roomType = \"\",\r\n  genderRequired = \"\",\r\n  totalRooms = \"\",\r\n  availableRooms = \"\",\r\n  size = \"\",\r\n  floor = \"\",\r\n  bathrooms = \"\",\r\n  separatedBathroom = false,\r\n  residentsCount = \"\",\r\n  availablePersons = \"\",\r\n  minPrice = \"\",\r\n  maxPrice = \"\",\r\n  rentTime = \"\",\r\n  paymentTime = \"\",\r\n  priceIncludeWaterAndElectricity = false,\r\n  minRating = 0,\r\n  minTotalRatings = 0,\r\n  isVerified = false,\r\n  isAvailable = false,\r\n  categoryId = \"\",\r\n  allowSmoking = false,\r\n  includeFurniture = false,\r\n  airConditioning = false,\r\n  includeWaterHeater = false,\r\n  parking = false,\r\n  internet = false,\r\n  nearToMetro = false,\r\n  nearToMarket = false,\r\n  elevator = false,\r\n  trialPeriod = false,\r\n  goodForForeigners = false,\r\n  sortBy = \"createdAt\",\r\n  sortOrder = \"asc\",\r\n}: {\r\n  page?: number;\r\n  limit?: number;\r\n  title?: string;\r\n  city?: string;\r\n  country?: string;\r\n  neighborhood?: string;\r\n  address?: string;\r\n  latitude?: string;\r\n  longitude?: string;\r\n  type?: string;\r\n  roomType?: string;\r\n  genderRequired?: string;\r\n  totalRooms?: string;\r\n  availableRooms?: string;\r\n  size?: string;\r\n  floor?: string;\r\n  bathrooms?: string;\r\n  separatedBathroom?: boolean;\r\n  residentsCount?: string;\r\n  availablePersons?: string;\r\n  minPrice?: string;\r\n  maxPrice?: string;\r\n  rentTime?: string;\r\n  paymentTime?: string;\r\n  priceIncludeWaterAndElectricity?: boolean;\r\n  minRating?: number;\r\n  minTotalRatings?: number;\r\n  isVerified?: boolean;\r\n  isAvailable?: boolean;\r\n  categoryId?: string;\r\n  allowSmoking?: boolean;\r\n  includeFurniture?: boolean;\r\n  airConditioning?: boolean;\r\n  includeWaterHeater?: boolean;\r\n  parking?: boolean;\r\n  internet?: boolean;\r\n  nearToMetro?: boolean;\r\n  nearToMarket?: boolean;\r\n  elevator?: boolean;\r\n  trialPeriod?: boolean;\r\n  goodForForeigners?: boolean;\r\n  sortBy?: \"createdAt\" | \"updatedAt\" | \"price\" | \"rating\" | \"title\";\r\n  sortOrder?: \"asc\" | \"desc\";\r\n}) => {\r\n  const response = await get(\r\n    `/properties?page=${page}&limit=${limit}&${title ? `title=${title}&` : \"\"}${\r\n      city ? `city=${city}&` : \"\"\r\n    }${country ? `country=${country}&` : \"\"}${\r\n      neighborhood ? `neighborhood=${neighborhood}&` : \"\"\r\n    }${address ? `address=${address}&` : \"\"}${type ? `type=${type}&` : \"\"}${\r\n      roomType ? `roomType=${roomType}&` : \"\"\r\n    }${genderRequired ? `genderRequired=${genderRequired}&` : \"\"}${\r\n      totalRooms ? `totalRooms=${totalRooms}&` : \"\"\r\n    }${availableRooms ? `availableRooms=${availableRooms}&` : \"\"}${\r\n      size ? `size=${size}&` : \"\"\r\n    }${floor ? `floor=${floor}&` : \"\"}${\r\n      bathrooms ? `bathrooms=${bathrooms}&` : \"\"\r\n    }${separatedBathroom ? `separatedBathroom=${separatedBathroom}&` : \"\"}${\r\n      residentsCount ? `residentsCount=${residentsCount}&` : \"\"\r\n    }${availablePersons ? `availablePersons=${availablePersons}&` : \"\"}${\r\n      rentTime ? `rentTime=${rentTime}&` : \"\"\r\n    }${paymentTime ? `paymentTime=${paymentTime}&` : \"\"}${\r\n      priceIncludeWaterAndElectricity\r\n        ? `priceIncludeWaterAndElectricity=${priceIncludeWaterAndElectricity}&`\r\n        : \"\"\r\n    }${allowSmoking ? `allowSmoking=${allowSmoking}&` : \"\"}${\r\n      includeFurniture ? `includeFurniture=${includeFurniture}&` : \"\"\r\n    }${airConditioning ? `airConditioning=${airConditioning}&` : \"\"}${\r\n      includeWaterHeater ? `includeWaterHeater=${includeWaterHeater}&` : \"\"\r\n    }${parking ? `parking=${parking}&` : \"\"}${\r\n      internet ? `internet=${internet}&` : \"\"\r\n    }${nearToMetro ? `nearToMetro=${nearToMetro}&` : \"\"}${\r\n      nearToMarket ? `nearToMarket=${nearToMarket}&` : \"\"\r\n    }${elevator ? `elevator=${elevator}&` : \"\"}${\r\n      trialPeriod ? `trialPeriod=${trialPeriod}&` : \"\"\r\n    }${goodForForeigners ? `goodForForeigners=${goodForForeigners}&` : \"\"}${\r\n      categoryId ? `categoryId=${categoryId}&` : \"\"\r\n    }${minRating ? `minRating=${minRating}&` : \"\"}${\r\n      minTotalRatings ? `minTotalRatings=${minTotalRatings}&` : \"\"\r\n    }${isVerified ? `isVerified=${isVerified}&` : \"\"}${\r\n      minPrice ? `minPrice=${minPrice}&` : \"\"\r\n    }${maxPrice ? `maxPrice=${maxPrice}&` : \"\"}${\r\n      isAvailable ? `isAvailable=${isAvailable}&` : \"\"\r\n    }${sortBy ? `sortBy=${sortBy}&` : \"\"}${\r\n      sortOrder ? `sortOrder=${sortOrder}` : \"\"\r\n    }`\r\n  );\r\n\r\n  return response;\r\n};\r\n\r\nexport const getMyProperties = async () => {\r\n  const response = await get(`/properties/my-properties`);\r\n  return response;\r\n};\r\n\r\nexport const getProperty = async (slug: string) => {\r\n  const response = await get(`/properties/slug/${slug}`);\r\n  return response;\r\n};\r\n\r\nexport const getPropertiesByUserId = async (userId: string) => {\r\n  const response = await get(`/properties/user/${userId}`);\r\n  return response;\r\n};\r\n\r\nexport const getFavoriteProperties = async () => {\r\n  const response = await get(`/properties/favorites`);\r\n  return response;\r\n};\r\n\r\nexport const toggleFavorite = async (id: string) => {\r\n  const response = await patch(`/properties/${id}/toggle-favorite`);\r\n  console.log(\"toggle favorite response\", response);\r\n  return response;\r\n};\r\n\r\nexport const updateProperty = async (data: Property) => {\r\n  const response = await patch(\"/properties\", data);\r\n  return response;\r\n};\r\n\r\nexport const deleteProperty = async (id: string) => {\r\n  const response = await del(`/properties/${id}`);\r\n  return response;\r\n};\r\n"], "names": [], "mappings": ";;;;;;IA6Ma,iBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1268, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/actions/ratings.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { post } from \"@/services/api\";\r\n\r\ninterface Rating {\r\n  score: number;\r\n  propertyId?: string;\r\n  personId?: string;\r\n}\r\n\r\nexport async function createRating(rating: Rating) {\r\n  const response = await post(\"/ratings\", rating);\r\n  return response.data;\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAUsB,eAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1284, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/property-details/sections/property-overview.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { useMutation, useQueryClient } from '@tanstack/react-query'\r\nimport { useTranslations } from 'next-intl'\r\nimport { Card, CardContent } from '@/components/ui/card'\r\nimport { Button } from '@/components/ui/button'\r\nimport {\r\n    MapPin,\r\n    Home,\r\n    Users,\r\n    Bath,\r\n    Bed,\r\n    Square,\r\n    Star,\r\n    Heart,\r\n    Share2,\r\n    Wifi,\r\n    Car,\r\n    Wind,\r\n    Building,\r\n    ShieldCheck,\r\n    Clock,\r\n    Loader2,\r\n    Check\r\n} from 'lucide-react'\r\nimport { useCurrencyState } from '@/hooks/useCurrencyState'\r\nimport { useFavoriteProperties } from '@/hooks/useFavoriteProperties'\r\nimport { toggleFavorite } from '@/actions/properties'\r\nimport { createRating } from '@/actions/ratings'\r\nimport { useUserStore } from '@/store/useUserStore'\r\nimport { usePathname } from '@/i18n/navigation'\r\nimport { cn } from '@/lib/utils'\r\n\r\ninterface PropertyOverviewProps {\r\n    property: any\r\n}\r\n\r\n// Custom Gender Icons (reusing from property card)\r\nconst MaleIcon = ({ className }: { className?: string }) => (\r\n    <svg\r\n        className={className}\r\n        viewBox=\"0 0 24 24\"\r\n        fill=\"none\"\r\n        stroke=\"currentColor\"\r\n        strokeWidth=\"2\"\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"round\"\r\n    >\r\n        <circle cx=\"10\" cy=\"14\" r=\"8\" />\r\n        <path d=\"M16.93 6.07l4-4\" />\r\n        <path d=\"M21 2h-5v5\" />\r\n    </svg>\r\n)\r\n\r\nconst FemaleIcon = ({ className }: { className?: string }) => (\r\n    <svg\r\n        className={className}\r\n        viewBox=\"0 0 24 24\"\r\n        fill=\"none\"\r\n        stroke=\"currentColor\"\r\n        strokeWidth=\"2\"\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"round\"\r\n    >\r\n        <circle cx=\"12\" cy=\"8\" r=\"7\" />\r\n        <path d=\"M12 15v7\" />\r\n        <path d=\"M8 19h8\" />\r\n    </svg>\r\n)\r\n\r\n// Simple Badge Component\r\nconst Badge = ({ children, variant = 'default', className = '' }: {\r\n    children: React.ReactNode\r\n    variant?: 'default' | 'secondary' | 'outline' | 'destructive'\r\n    className?: string\r\n}) => {\r\n    const variants = {\r\n        default: 'bg-blue-100 text-blue-800 border-blue-200',\r\n        secondary: 'bg-gray-100 text-gray-800 border-gray-200',\r\n        outline: 'bg-white text-gray-700 border-gray-300',\r\n        destructive: 'bg-red-100 text-red-800 border-red-200'\r\n    }\r\n\r\n    return (\r\n        <span className={cn(\r\n            'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border',\r\n            variants[variant],\r\n            className\r\n        )}>\r\n            {children}\r\n        </span>\r\n    )\r\n}\r\n\r\nexport default function PropertyOverview({ property }: PropertyOverviewProps) {\r\n    const t = useTranslations('propertyDetails.overview')\r\n    const [shareStatus, setShareStatus] = useState<'idle' | 'sharing' | 'success' | 'error'>('idle')\r\n    const [ratingStatus, setRatingStatus] = useState<'idle' | 'submitting' | 'success' | 'error'>('idle')\r\n    const [hoveredRating, setHoveredRating] = useState<number | null>(null)\r\n    const { formatPropertyPrice, isReady } = useCurrencyState()\r\n    const { isPropertyFavorite, isLoading: favoritesLoading } = useFavoriteProperties()\r\n    const { user } = useUserStore()\r\n    const pathname = usePathname()\r\n    const queryClient = useQueryClient()\r\n\r\n    // Determine if we're on a persons page or properties page\r\n    const isPersonsPage = pathname.includes('/persons/')\r\n    const isPropertiesPage = pathname.includes('/properties/')\r\n\r\n    // Check if property is currently favorited\r\n    const isFavorite = isPropertyFavorite(property.id)\r\n\r\n    // Rating mutation\r\n    const ratingMutation = useMutation({\r\n        mutationFn: (rating: number) => {\r\n            const ratingData = {\r\n                score: rating,\r\n                ...(isPersonsPage ? { personId: property.id } : { propertyId: property.id })\r\n            }\r\n            return createRating(ratingData)\r\n        },\r\n        onSuccess: () => {\r\n            setRatingStatus('success')\r\n            // Invalidate and refetch the current item to get updated rating\r\n            if (isPropertiesPage) {\r\n                queryClient.invalidateQueries({ queryKey: ['property', property.slug] })\r\n            } else if (isPersonsPage) {\r\n                queryClient.invalidateQueries({ queryKey: ['person', property.slug] })\r\n            }\r\n\r\n            // Reset status after 3 seconds\r\n            setTimeout(() => setRatingStatus('idle'), 3000)\r\n        },\r\n        onError: (error) => {\r\n            console.error('Error creating rating:', error)\r\n            setRatingStatus('error')\r\n            setTimeout(() => setRatingStatus('idle'), 3000)\r\n        }\r\n    })\r\n\r\n    // Favorite toggle mutation\r\n    const favoriteMutation = useMutation({\r\n        mutationFn: () => toggleFavorite(property.id),\r\n        onSuccess: () => {\r\n            // Invalidate and refetch favorites\r\n            queryClient.invalidateQueries({ queryKey: ['favorites', 'properties'] })\r\n        },\r\n        onError: (error) => {\r\n            console.error('Error toggling favorite:', error)\r\n        }\r\n    })\r\n\r\n    // Handle rating submission (admin only)\r\n    const handleRatingClick = (rating: number) => {\r\n        if (!user?.isAdmin || ratingMutation.isPending) return\r\n\r\n        setRatingStatus('submitting')\r\n        ratingMutation.mutate(rating)\r\n    }\r\n\r\n    // Handle favorite toggle\r\n    const handleFavoriteToggle = async () => {\r\n        if (favoriteMutation.isPending) return\r\n\r\n        try {\r\n            await favoriteMutation.mutateAsync()\r\n        } catch (error) {\r\n            console.error('Failed to toggle favorite:', error)\r\n        }\r\n    }\r\n\r\n    // Handle share functionality\r\n    const handleShare = async () => {\r\n        if (shareStatus === 'sharing') return\r\n\r\n        setShareStatus('sharing')\r\n\r\n        const shareData = {\r\n            title: property.title,\r\n            text: `Check out this property: ${property.title}`,\r\n            url: window.location.href\r\n        }\r\n\r\n        try {\r\n            // Try Web Share API first (mobile browsers)\r\n            if ('share' in navigator && /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {\r\n                await navigator.share(shareData)\r\n                setShareStatus('success')\r\n            } else {\r\n                // Fall back to copying to clipboard\r\n                await navigator.clipboard.writeText(window.location.href)\r\n                setShareStatus('success')\r\n            }\r\n\r\n            // Reset status after 2 seconds\r\n            setTimeout(() => setShareStatus('idle'), 2000)\r\n        } catch (error) {\r\n            console.error('Error sharing:', error)\r\n            setShareStatus('error')\r\n            setTimeout(() => setShareStatus('idle'), 2000)\r\n        }\r\n    }\r\n\r\n    // Get gender icon and text\r\n    const getGenderDisplay = () => {\r\n        switch (property.genderRequired) {\r\n            case 'male':\r\n                return { icon: MaleIcon, text: t('maleOnly') }\r\n            case 'female':\r\n                return { icon: FemaleIcon, text: t('femaleOnly') }\r\n            default:\r\n                return { icon: Users, text: t('mixedGender') }\r\n        }\r\n    }\r\n\r\n    // Get price period text\r\n    const getPricePeriod = () => {\r\n        const period = property.rentTime || property.paymentTime || 'monthly'\r\n        const periodMap: Record<string, string> = {\r\n            'daily': t('pricePerDay'),\r\n            'weekly': t('pricePerWeek'),\r\n            'monthly': t('pricePerMonth'),\r\n            'quarterly': t('pricePerQuarter'),\r\n            'semiannual': t('pricePerSemiannual'),\r\n            'annually': t('pricePerYear')\r\n        }\r\n        return periodMap[period] || t('pricePerMonth')\r\n    }\r\n\r\n    // Get country flag\r\n    const getCountryFlag = () => {\r\n        switch (property.country?.toUpperCase()) {\r\n            case 'UAE':\r\n            case 'AE':\r\n                return 'https://flagcdn.com/w20/ae.png'\r\n            case 'SAUDI ARABIA':\r\n            case 'SA':\r\n                return 'https://flagcdn.com/w20/sa.png'\r\n            case 'EGYPT':\r\n            case 'EG':\r\n                return 'https://flagcdn.com/w20/eg.png'\r\n            case 'JORDAN':\r\n            case 'JO':\r\n                return 'https://flagcdn.com/w20/jo.png'\r\n            default:\r\n                return null\r\n        }\r\n    }\r\n\r\n    const genderDisplay = getGenderDisplay()\r\n    const countryFlag = getCountryFlag()\r\n\r\n    // Generate star rating with admin interactivity\r\n    const renderStars = (rating: number) => {\r\n        return Array.from({ length: 5 }, (_, i) => {\r\n            const starValue = i + 1\r\n            const isSelected = starValue <= (hoveredRating || rating)\r\n            const isHovered = hoveredRating !== null && starValue <= hoveredRating\r\n\r\n            return (\r\n                <Star\r\n                    key={i}\r\n                    className={cn(\r\n                        \"h-4 w-4 transition-all duration-200\",\r\n                        user?.isAdmin ? \"cursor-pointer hover:scale-110\" : \"cursor-default\",\r\n                        isSelected ? \"fill-yellow-400 text-yellow-400\" : \"text-gray-300\",\r\n                        isHovered && user?.isAdmin ? \"fill-yellow-300 text-yellow-300\" : \"\"\r\n                    )}\r\n                    onClick={() => user?.isAdmin && handleRatingClick(starValue)}\r\n                    onMouseEnter={() => user?.isAdmin && setHoveredRating(starValue)}\r\n                    onMouseLeave={() => user?.isAdmin && setHoveredRating(null)}\r\n                />\r\n            )\r\n        })\r\n    }\r\n\r\n    // Key amenities for quick display\r\n    const quickAmenities = [\r\n        { key: 'internet', icon: Wifi, label: 'WiFi', value: property.internet },\r\n        { key: 'parking', icon: Car, label: 'Parking', value: property.parking },\r\n        { key: 'airConditioning', icon: Wind, label: 'AC', value: property.airConditioning },\r\n        { key: 'elevator', icon: Building, label: 'Elevator', value: property.elevator },\r\n    ].filter(amenity => amenity.value)\r\n\r\n    return (\r\n        <Card className=\"border-0 shadow-sm\">\r\n            <CardContent className=\"p-6\">\r\n                {/* Header Section */}\r\n                <div className=\"flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6 mb-8\">\r\n                    <div className=\"flex-1\">\r\n                        {/* Property Type & Verification */}\r\n                        <div className=\"flex items-center gap-2 mb-2\">\r\n                            <Badge variant=\"secondary\" className=\"capitalize\">\r\n                                {property.type}\r\n                            </Badge>\r\n                            {/* {property.roomType && (\r\n                                <Badge variant=\"outline\" className=\"capitalize\">\r\n                                    {property.roomType} Room\r\n                                </Badge>\r\n                            )} */}\r\n                            {property.isVerified && (\r\n                                <Badge variant=\"default\" className=\"bg-green-100 text-green-800 border-green-200\">\r\n                                    <ShieldCheck className=\"h-3 w-3 mr-1\" />\r\n                                    {t('verified')}\r\n                                </Badge>\r\n                            )}\r\n                            {!property.isAvailable && (\r\n                                <Badge variant=\"destructive\">\r\n                                    {t('notAvailable')}\r\n                                </Badge>\r\n                            )}\r\n                        </div>\r\n\r\n                        {/* Title */}\r\n                        <h1 className=\"text-2xl lg:text-3xl font-bold text-gray-900 mb-2\">\r\n                            {property.title}\r\n                        </h1>\r\n\r\n                        {/* Location */}\r\n                        <div className=\"flex items-center gap-2 text-gray-600 mb-4\">\r\n                            <MapPin className=\"h-4 w-4\" />\r\n                            <span>{property.address}</span>\r\n                            {countryFlag && (\r\n                                <img src={countryFlag} alt={property.country} className=\"h-4 w-6 object-cover rounded-sm\" />\r\n                            )}\r\n                        </div>\r\n\r\n                        {/* Rating */}\r\n                        {(property.rating && property.totalRatings > 0) || user?.isAdmin ? (\r\n                            <div className=\"flex items-center gap-2 mb-4\">\r\n                                <div className=\"flex items-center gap-1\">\r\n                                    {renderStars(property.rating || 0)}\r\n                                </div>\r\n\r\n                                {user?.isAdmin && (\r\n                                    <span className=\"text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded-full\">\r\n                                        {t('adminClickToRate')}\r\n                                    </span>\r\n                                )}\r\n                                {ratingStatus === 'submitting' && (\r\n                                    <div className=\"flex items-center gap-1 text-xs text-gray-500\">\r\n                                        <Loader2 className=\"h-3 w-3 animate-spin\" />\r\n                                        {t('submittingRating')}\r\n                                    </div>\r\n                                )}\r\n                            </div>\r\n                        ) : null}\r\n                    </div>\r\n\r\n                    {/* Actions */}\r\n                    <div className=\"flex items-center gap-3\">\r\n                        <Button\r\n                            variant=\"outline\"\r\n                            size=\"sm\"\r\n                            onClick={handleFavoriteToggle}\r\n                            disabled={favoriteMutation.isPending || favoritesLoading}\r\n                            title={isFavorite ? t('removeFromFavorites') : t('addToFavorites')}\r\n                            className={cn(\r\n                                \"group relative overflow-hidden flex items-center justify-center w-10 h-10 p-0 rounded-full border-2 font-medium transition-all duration-300 ease-in-out\",\r\n                                isFavorite\r\n                                    ? \"bg-gradient-to-r from-red-500 to-pink-500 border-red-500 text-white shadow-lg shadow-red-500/25 hover:shadow-xl hover:shadow-red-500/40 hover:scale-105\"\r\n                                    : \"bg-white border-gray-200 text-gray-700 hover:border-red-300 hover:text-red-500 hover:shadow-md hover:scale-105 active:scale-95\"\r\n                            )}\r\n                        >\r\n                            {favoriteMutation.isPending ? (\r\n                                <Loader2 className=\"h-4 w-4 animate-spin\" />\r\n                            ) : (\r\n                                <Heart className={cn(\r\n                                    \"h-4 w-4 transition-all duration-300\",\r\n                                    isFavorite\r\n                                        ? \"fill-current animate-pulse\"\r\n                                        : \"group-hover:scale-110\"\r\n                                )} />\r\n                            )}\r\n                            {isFavorite && (\r\n                                <div className=\"absolute inset-0 bg-gradient-to-r from-red-400 to-pink-400 opacity-0 group-hover:opacity-20 transition-opacity duration-300 rounded-full\"></div>\r\n                            )}\r\n                        </Button>\r\n\r\n                        <Button\r\n                            variant=\"outline\"\r\n                            size=\"sm\"\r\n                            onClick={handleShare}\r\n                            disabled={shareStatus === 'sharing'}\r\n                            title={shareStatus === 'success' ? t('linkCopied') : t('shareProperty')}\r\n                            className={cn(\r\n                                \"group relative overflow-hidden flex items-center justify-center w-10 h-10 p-0 rounded-full border-2 font-medium transition-all duration-300 ease-in-out\",\r\n                                shareStatus === 'success'\r\n                                    ? \"bg-gradient-to-r from-green-500 to-emerald-500 border-green-500 text-white shadow-lg shadow-green-500/25\"\r\n                                    : \"bg-white border-gray-200 text-gray-700 hover:border-blue-300 hover:text-blue-500 hover:shadow-md hover:scale-105 active:scale-95\"\r\n                            )}\r\n                        >\r\n                            {shareStatus === 'sharing' ? (\r\n                                <Loader2 className=\"h-4 w-4 animate-spin\" />\r\n                            ) : shareStatus === 'success' ? (\r\n                                <Check className=\"h-4 w-4 animate-bounce\" />\r\n                            ) : (\r\n                                <Share2 className=\"h-4 w-4 group-hover:scale-110 transition-transform duration-300\" />\r\n                            )}\r\n                            {shareStatus === 'success' && (\r\n                                <div className=\"absolute inset-0 bg-gradient-to-r from-green-400 to-emerald-400 opacity-0 group-hover:opacity-20 transition-opacity duration-300 rounded-full\"></div>\r\n                            )}\r\n                        </Button>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Rating Status Messages */}\r\n                {ratingStatus === 'success' && (\r\n                    <div className=\"mb-6 p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl shadow-sm animate-in slide-in-from-top-2 duration-300\">\r\n                        <div className=\"flex items-center gap-3\">\r\n                            <div className=\"flex-shrink-0\">\r\n                                <Check className=\"h-5 w-5 text-green-600\" />\r\n                            </div>\r\n                            <p className=\"text-sm font-medium text-green-800\">\r\n                                {t('ratingSubmittedSuccessfully')}\r\n                            </p>\r\n                        </div>\r\n                    </div>\r\n                )}\r\n\r\n                {ratingStatus === 'error' && (\r\n                    <div className=\"mb-6 p-4 bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 rounded-xl shadow-sm animate-in slide-in-from-top-2 duration-300\">\r\n                        <div className=\"flex items-center gap-3\">\r\n                            <div className=\"flex-shrink-0\">\r\n                                <div className=\"h-5 w-5 rounded-full bg-red-100 flex items-center justify-center\">\r\n                                    <span className=\"text-red-600 text-xs font-bold\">!</span>\r\n                                </div>\r\n                            </div>\r\n                            <p className=\"text-sm font-medium text-red-800\">\r\n                                {t('failedToSubmitRating')}\r\n                            </p>\r\n                        </div>\r\n                    </div>\r\n                )}\r\n\r\n                {/* Success/Error Messages */}\r\n                {shareStatus === 'success' && (\r\n                    <div className=\"mb-6 p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl shadow-sm animate-in slide-in-from-top-2 duration-300\">\r\n                        <div className=\"flex items-center gap-3\">\r\n                            <div className=\"flex-shrink-0\">\r\n                                <Check className=\"h-5 w-5 text-green-600\" />\r\n                            </div>\r\n                            <p className=\"text-sm font-medium text-green-800\">\r\n                                {t('shareSuccessMessage')}\r\n                            </p>\r\n                        </div>\r\n                    </div>\r\n                )}\r\n\r\n                {shareStatus === 'error' && (\r\n                    <div className=\"mb-6 p-4 bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 rounded-xl shadow-sm animate-in slide-in-from-top-2 duration-300\">\r\n                        <div className=\"flex items-center gap-3\">\r\n                            <div className=\"flex-shrink-0\">\r\n                                <div className=\"h-5 w-5 rounded-full bg-red-100 flex items-center justify-center\">\r\n                                    <span className=\"text-red-600 text-xs font-bold\">!</span>\r\n                                </div>\r\n                            </div>\r\n                            <p className=\"text-sm font-medium text-red-800\">\r\n                                {t('shareErrorMessage')}\r\n                            </p>\r\n                        </div>\r\n                    </div>\r\n                )}\r\n\r\n                {/* Price Section */}\r\n                <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-100 rounded-lg p-4 mb-6\">\r\n                    <div className=\"flex items-center justify-between\">\r\n                        <div>\r\n                            <div className=\"text-3xl font-bold text-gray-900\">\r\n                                {isReady ? formatPropertyPrice(property.price, property.country || 'United States') : `$${property.price}`}\r\n                                <span className=\"text-lg font-normal text-gray-600\">{getPricePeriod()}</span>\r\n                            </div>\r\n                            {property.priceIncludeWaterAndElectricity && (\r\n                                <p className=\"text-sm text-green-600 mt-1\">{t('utilitiesIncluded')}</p>\r\n                            )}\r\n                        </div>\r\n                        {property.trialPeriod && (\r\n                            <Badge variant=\"outline\" className=\"bg-white border-blue-200 text-blue-700\">\r\n                                <Clock className=\"h-3 w-3 mr-1\" />\r\n                                {t('trialPeriodAvailable')}\r\n                            </Badge>\r\n                        )}\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Key Stats Grid */}\r\n                <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\">\r\n                    {/* Rooms */}\r\n                    <div className=\"text-center p-3 bg-gray-50 rounded-lg\">\r\n                        <Home className=\"h-6 w-6 mx-auto mb-2 text-gray-600\" />\r\n                        <div className=\"text-lg font-semibold text-gray-900\">{property.totalRooms || 0}</div>\r\n                        <div className=\"text-sm text-gray-600\">{t('totalRooms')}</div>\r\n                    </div>\r\n\r\n                    {/* Available Rooms */}\r\n                    <div className=\"text-center p-3 bg-gray-50 rounded-lg\">\r\n                        <Bed className=\"h-6 w-6 mx-auto mb-2 text-gray-600\" />\r\n                        <div className=\"text-lg font-semibold text-gray-900\">{property.availableRooms || 0}</div>\r\n                        <div className=\"text-sm text-gray-600\">{t('available')}</div>\r\n                    </div>\r\n\r\n                    {/* Bathrooms */}\r\n                    <div className=\"text-center p-3 bg-gray-50 rounded-lg\">\r\n                        <Bath className=\"h-6 w-6 mx-auto mb-2 text-gray-600\" />\r\n                        <div className=\"text-lg font-semibold text-gray-900\">{property.bathrooms || 0}</div>\r\n                        <div className=\"text-sm text-gray-600\">{t('bathrooms')}</div>\r\n                    </div>\r\n\r\n                    {/* Gender Requirement */}\r\n                    <div className=\"text-center p-3 bg-gray-50 rounded-lg\">\r\n                        <genderDisplay.icon className=\"h-6 w-6 mx-auto mb-2 text-gray-600\" />\r\n                        <div className=\"text-sm font-medium text-gray-900\">{genderDisplay.text}</div>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Additional Info */}\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\">\r\n                    {/* Size & Floor */}\r\n                    <div className=\"space-y-2\">\r\n                        {property.size && (\r\n                            <div className=\"flex items-center gap-2\">\r\n                                <Square className=\"h-4 w-4 text-gray-500\" />\r\n                                <span className=\"text-sm text-gray-600\">{t('size')}: {property.size || 'N/A'}</span>\r\n                            </div>\r\n                        )}\r\n                        {property.floor && (\r\n                            <div className=\"flex items-center gap-2\">\r\n                                <Home className=\"h-4 w-4 text-gray-500\" />\r\n                                <span className=\"text-sm text-gray-600\">{t('floor')}: {property.floor || 'N/A'}</span>\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n\r\n                    {/* Occupancy */}\r\n                    <div className=\"space-y-2\">\r\n                        <div className=\"flex items-center gap-2\">\r\n                            <Users className=\"h-4 w-4 text-gray-500\" />\r\n                            <span className=\"text-sm text-gray-600\">\r\n                                {t('currentResidents')}: {property.residentsCount || 0}\r\n                            </span>\r\n                        </div>\r\n                        <div className=\"flex items-center gap-2\">\r\n                            <Users className=\"h-4 w-4 text-gray-500\" />\r\n                            <span className=\"text-sm text-gray-600\">\r\n                                {t('availableSpots')}: {property.availablePersons || 0}\r\n                            </span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Quick Amenities */}\r\n                {quickAmenities.length > 0 && (\r\n                    <div className=\"border-t pt-4\">\r\n                        <h3 className=\"text-sm font-medium text-gray-900 mb-3\">{t('keyAmenities')}</h3>\r\n                        <div className=\"flex flex-wrap gap-2\">\r\n                            {quickAmenities.map((amenity) => (\r\n                                <div\r\n                                    key={amenity.key}\r\n                                    className=\"flex items-center gap-2 bg-green-50 text-green-700 px-3 py-1 rounded-full text-sm\"\r\n                                >\r\n                                    <amenity.icon className=\"h-4 w-4\" />\r\n                                    {amenity.label}\r\n                                </div>\r\n                            ))}\r\n                        </div>\r\n                    </div>\r\n                )}\r\n            </CardContent>\r\n        </Card>\r\n    )\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmBA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAhCA;;;;;;;;;;;;;;AAsCA,mDAAmD;AACnD,MAAM,WAAW,CAAC,EAAE,SAAS,EAA0B,iBACnD,6LAAC;QACG,WAAW;QACX,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;;0BAEf,6LAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;;;;;;0BAC1B,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;;;;;;;KAZV;AAgBN,MAAM,aAAa,CAAC,EAAE,SAAS,EAA0B,iBACrD,6LAAC;QACG,WAAW;QACX,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;;0BAEf,6LAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAI,GAAE;;;;;;0BACzB,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;;;;;;;MAZV;AAgBN,yBAAyB;AACzB,MAAM,QAAQ,CAAC,EAAE,QAAQ,EAAE,UAAU,SAAS,EAAE,YAAY,EAAE,EAI7D;IACG,MAAM,WAAW;QACb,SAAS;QACT,WAAW;QACX,SAAS;QACT,aAAa;IACjB;IAEA,qBACI,6LAAC;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACd,8EACA,QAAQ,CAAC,QAAQ,EACjB;kBAEC;;;;;;AAGb;MArBM;AAuBS,SAAS,iBAAiB,EAAE,QAAQ,EAAyB;;IACxE,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4C;IACzF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA+C;IAC9F,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,EAAE,mBAAmB,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD;IACxD,MAAM,EAAE,kBAAkB,EAAE,WAAW,gBAAgB,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,wBAAqB,AAAD;IAChF,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,WAAW,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,0DAA0D;IAC1D,MAAM,gBAAgB,SAAS,QAAQ,CAAC;IACxC,MAAM,mBAAmB,SAAS,QAAQ,CAAC;IAE3C,2CAA2C;IAC3C,MAAM,aAAa,mBAAmB,SAAS,EAAE;IAEjD,kBAAkB;IAClB,MAAM,iBAAiB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,UAAU;4DAAE,CAAC;gBACT,MAAM,aAAa;oBACf,OAAO;oBACP,GAAI,gBAAgB;wBAAE,UAAU,SAAS,EAAE;oBAAC,IAAI;wBAAE,YAAY,SAAS,EAAE;oBAAC,CAAC;gBAC/E;gBACA,OAAO,CAAA,GAAA,yJAAA,CAAA,eAAY,AAAD,EAAE;YACxB;;QACA,SAAS;4DAAE;gBACP,gBAAgB;gBAChB,gEAAgE;gBAChE,IAAI,kBAAkB;oBAClB,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;4BAAY,SAAS,IAAI;yBAAC;oBAAC;gBAC1E,OAAO,IAAI,eAAe;oBACtB,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;4BAAU,SAAS,IAAI;yBAAC;oBAAC;gBACxE;gBAEA,+BAA+B;gBAC/B;oEAAW,IAAM,gBAAgB;mEAAS;YAC9C;;QACA,OAAO;4DAAE,CAAC;gBACN,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,gBAAgB;gBAChB;oEAAW,IAAM,gBAAgB;mEAAS;YAC9C;;IACJ;IAEA,2BAA2B;IAC3B,MAAM,mBAAmB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjC,UAAU;8DAAE,IAAM,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,EAAE;;QAC5C,SAAS;8DAAE;gBACP,mCAAmC;gBACnC,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAa;qBAAa;gBAAC;YAC1E;;QACA,OAAO;8DAAE,CAAC;gBACN,QAAQ,KAAK,CAAC,4BAA4B;YAC9C;;IACJ;IAEA,wCAAwC;IACxC,MAAM,oBAAoB,CAAC;QACvB,IAAI,CAAC,MAAM,WAAW,eAAe,SAAS,EAAE;QAEhD,gBAAgB;QAChB,eAAe,MAAM,CAAC;IAC1B;IAEA,yBAAyB;IACzB,MAAM,uBAAuB;QACzB,IAAI,iBAAiB,SAAS,EAAE;QAEhC,IAAI;YACA,MAAM,iBAAiB,WAAW;QACtC,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,8BAA8B;QAChD;IACJ;IAEA,6BAA6B;IAC7B,MAAM,cAAc;QAChB,IAAI,gBAAgB,WAAW;QAE/B,eAAe;QAEf,MAAM,YAAY;YACd,OAAO,SAAS,KAAK;YACrB,MAAM,CAAC,yBAAyB,EAAE,SAAS,KAAK,EAAE;YAClD,KAAK,OAAO,QAAQ,CAAC,IAAI;QAC7B;QAEA,IAAI;YACA,4CAA4C;YAC5C,IAAI,WAAW,aAAa,2DAA2D,IAAI,CAAC,UAAU,SAAS,GAAG;gBAC9G,MAAM,UAAU,KAAK,CAAC;gBACtB,eAAe;YACnB,OAAO;gBACH,oCAAoC;gBACpC,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC,OAAO,QAAQ,CAAC,IAAI;gBACxD,eAAe;YACnB;YAEA,+BAA+B;YAC/B,WAAW,IAAM,eAAe,SAAS;QAC7C,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,kBAAkB;YAChC,eAAe;YACf,WAAW,IAAM,eAAe,SAAS;QAC7C;IACJ;IAEA,2BAA2B;IAC3B,MAAM,mBAAmB;QACrB,OAAQ,SAAS,cAAc;YAC3B,KAAK;gBACD,OAAO;oBAAE,MAAM;oBAAU,MAAM,EAAE;gBAAY;YACjD,KAAK;gBACD,OAAO;oBAAE,MAAM;oBAAY,MAAM,EAAE;gBAAc;YACrD;gBACI,OAAO;oBAAE,MAAM,uMAAA,CAAA,QAAK;oBAAE,MAAM,EAAE;gBAAe;QACrD;IACJ;IAEA,wBAAwB;IACxB,MAAM,iBAAiB;QACnB,MAAM,SAAS,SAAS,QAAQ,IAAI,SAAS,WAAW,IAAI;QAC5D,MAAM,YAAoC;YACtC,SAAS,EAAE;YACX,UAAU,EAAE;YACZ,WAAW,EAAE;YACb,aAAa,EAAE;YACf,cAAc,EAAE;YAChB,YAAY,EAAE;QAClB;QACA,OAAO,SAAS,CAAC,OAAO,IAAI,EAAE;IAClC;IAEA,mBAAmB;IACnB,MAAM,iBAAiB;QACnB,OAAQ,SAAS,OAAO,EAAE;YACtB,KAAK;YACL,KAAK;gBACD,OAAO;YACX,KAAK;YACL,KAAK;gBACD,OAAO;YACX,KAAK;YACL,KAAK;gBACD,OAAO;YACX,KAAK;YACL,KAAK;gBACD,OAAO;YACX;gBACI,OAAO;QACf;IACJ;IAEA,MAAM,gBAAgB;IACtB,MAAM,cAAc;IAEpB,gDAAgD;IAChD,MAAM,cAAc,CAAC;QACjB,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,CAAC,GAAG;YACjC,MAAM,YAAY,IAAI;YACtB,MAAM,aAAa,aAAa,CAAC,iBAAiB,MAAM;YACxD,MAAM,YAAY,kBAAkB,QAAQ,aAAa;YAEzD,qBACI,6LAAC,qMAAA,CAAA,OAAI;gBAED,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,uCACA,MAAM,UAAU,mCAAmC,kBACnD,aAAa,oCAAoC,iBACjD,aAAa,MAAM,UAAU,oCAAoC;gBAErE,SAAS,IAAM,MAAM,WAAW,kBAAkB;gBAClD,cAAc,IAAM,MAAM,WAAW,iBAAiB;gBACtD,cAAc,IAAM,MAAM,WAAW,iBAAiB;eATjD;;;;;QAYjB;IACJ;IAEA,kCAAkC;IAClC,MAAM,iBAAiB;QACnB;YAAE,KAAK;YAAY,MAAM,qMAAA,CAAA,OAAI;YAAE,OAAO;YAAQ,OAAO,SAAS,QAAQ;QAAC;QACvE;YAAE,KAAK;YAAW,MAAM,mMAAA,CAAA,MAAG;YAAE,OAAO;YAAW,OAAO,SAAS,OAAO;QAAC;QACvE;YAAE,KAAK;YAAmB,MAAM,qMAAA,CAAA,OAAI;YAAE,OAAO;YAAM,OAAO,SAAS,eAAe;QAAC;QACnF;YAAE,KAAK;YAAY,MAAM,6MAAA,CAAA,WAAQ;YAAE,OAAO;YAAY,OAAO,SAAS,QAAQ;QAAC;KAClF,CAAC,MAAM,CAAC,CAAA,UAAW,QAAQ,KAAK;IAEjC,qBACI,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;kBACZ,cAAA,6LAAC,mIAAA,CAAA,cAAW;YAAC,WAAU;;8BAEnB,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAI,WAAU;;8CAEX,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAM,SAAQ;4CAAY,WAAU;sDAChC,SAAS,IAAI;;;;;;wCAOjB,SAAS,UAAU,kBAChB,6LAAC;4CAAM,SAAQ;4CAAU,WAAU;;8DAC/B,6LAAC,uNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDACtB,EAAE;;;;;;;wCAGV,CAAC,SAAS,WAAW,kBAClB,6LAAC;4CAAM,SAAQ;sDACV,EAAE;;;;;;;;;;;;8CAMf,6LAAC;oCAAG,WAAU;8CACT,SAAS,KAAK;;;;;;8CAInB,6LAAC;oCAAI,WAAU;;sDACX,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;sDAAM,SAAS,OAAO;;;;;;wCACtB,6BACG,6LAAC;4CAAI,KAAK;4CAAa,KAAK,SAAS,OAAO;4CAAE,WAAU;;;;;;;;;;;;gCAK9D,SAAS,MAAM,IAAI,SAAS,YAAY,GAAG,KAAM,MAAM,wBACrD,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAI,WAAU;sDACV,YAAY,SAAS,MAAM,IAAI;;;;;;wCAGnC,MAAM,yBACH,6LAAC;4CAAK,WAAU;sDACX,EAAE;;;;;;wCAGV,iBAAiB,8BACd,6LAAC;4CAAI,WAAU;;8DACX,6LAAC,oNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAClB,EAAE;;;;;;;;;;;;2CAIf;;;;;;;sCAIR,6LAAC;4BAAI,WAAU;;8CACX,6LAAC,qIAAA,CAAA,SAAM;oCACH,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,UAAU,iBAAiB,SAAS,IAAI;oCACxC,OAAO,aAAa,EAAE,yBAAyB,EAAE;oCACjD,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,2JACA,aACM,4JACA;;wCAGT,iBAAiB,SAAS,iBACvB,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;iEAEnB,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,uCACA,aACM,+BACA;;;;;;wCAGb,4BACG,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAIvB,6LAAC,qIAAA,CAAA,SAAM;oCACH,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,UAAU,gBAAgB;oCAC1B,OAAO,gBAAgB,YAAY,EAAE,gBAAgB,EAAE;oCACvD,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,2JACA,gBAAgB,YACV,6GACA;;wCAGT,gBAAgB,0BACb,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;mDACnB,gBAAgB,0BAChB,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;iEAEjB,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAErB,gBAAgB,2BACb,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;gBAO9B,iBAAiB,2BACd,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAI,WAAU;0CACX,cAAA,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAErB,6LAAC;gCAAE,WAAU;0CACR,EAAE;;;;;;;;;;;;;;;;;gBAMlB,iBAAiB,yBACd,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAI,WAAU;0CACX,cAAA,6LAAC;oCAAI,WAAU;8CACX,cAAA,6LAAC;wCAAK,WAAU;kDAAiC;;;;;;;;;;;;;;;;0CAGzD,6LAAC;gCAAE,WAAU;0CACR,EAAE;;;;;;;;;;;;;;;;;gBAOlB,gBAAgB,2BACb,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAI,WAAU;0CACX,cAAA,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAErB,6LAAC;gCAAE,WAAU;0CACR,EAAE;;;;;;;;;;;;;;;;;gBAMlB,gBAAgB,yBACb,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAI,WAAU;0CACX,cAAA,6LAAC;oCAAI,WAAU;8CACX,cAAA,6LAAC;wCAAK,WAAU;kDAAiC;;;;;;;;;;;;;;;;0CAGzD,6LAAC;gCAAE,WAAU;0CACR,EAAE;;;;;;;;;;;;;;;;;8BAOnB,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;;kDACG,6LAAC;wCAAI,WAAU;;4CACV,UAAU,oBAAoB,SAAS,KAAK,EAAE,SAAS,OAAO,IAAI,mBAAmB,CAAC,CAAC,EAAE,SAAS,KAAK,EAAE;0DAC1G,6LAAC;gDAAK,WAAU;0DAAqC;;;;;;;;;;;;oCAExD,SAAS,+BAA+B,kBACrC,6LAAC;wCAAE,WAAU;kDAA+B,EAAE;;;;;;;;;;;;4BAGrD,SAAS,WAAW,kBACjB,6LAAC;gCAAM,SAAQ;gCAAU,WAAU;;kDAC/B,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAChB,EAAE;;;;;;;;;;;;;;;;;;8BAOnB,6LAAC;oBAAI,WAAU;;sCAEX,6LAAC;4BAAI,WAAU;;8CACX,6LAAC,sMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;oCAAI,WAAU;8CAAuC,SAAS,UAAU,IAAI;;;;;;8CAC7E,6LAAC;oCAAI,WAAU;8CAAyB,EAAE;;;;;;;;;;;;sCAI9C,6LAAC;4BAAI,WAAU;;8CACX,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;8CAAuC,SAAS,cAAc,IAAI;;;;;;8CACjF,6LAAC;oCAAI,WAAU;8CAAyB,EAAE;;;;;;;;;;;;sCAI9C,6LAAC;4BAAI,WAAU;;8CACX,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;oCAAI,WAAU;8CAAuC,SAAS,SAAS,IAAI;;;;;;8CAC5E,6LAAC;oCAAI,WAAU;8CAAyB,EAAE;;;;;;;;;;;;sCAI9C,6LAAC;4BAAI,WAAU;;8CACX,6LAAC,cAAc,IAAI;oCAAC,WAAU;;;;;;8CAC9B,6LAAC;oCAAI,WAAU;8CAAqC,cAAc,IAAI;;;;;;;;;;;;;;;;;;8BAK9E,6LAAC;oBAAI,WAAU;;sCAEX,6LAAC;4BAAI,WAAU;;gCACV,SAAS,IAAI,kBACV,6LAAC;oCAAI,WAAU;;sDACX,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAK,WAAU;;gDAAyB,EAAE;gDAAQ;gDAAG,SAAS,IAAI,IAAI;;;;;;;;;;;;;gCAG9E,SAAS,KAAK,kBACX,6LAAC;oCAAI,WAAU;;sDACX,6LAAC,sMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;4CAAK,WAAU;;gDAAyB,EAAE;gDAAS;gDAAG,SAAS,KAAK,IAAI;;;;;;;;;;;;;;;;;;;sCAMrF,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAI,WAAU;;sDACX,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;4CAAK,WAAU;;gDACX,EAAE;gDAAoB;gDAAG,SAAS,cAAc,IAAI;;;;;;;;;;;;;8CAG7D,6LAAC;oCAAI,WAAU;;sDACX,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;4CAAK,WAAU;;gDACX,EAAE;gDAAkB;gDAAG,SAAS,gBAAgB,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;gBAOpE,eAAe,MAAM,GAAG,mBACrB,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAG,WAAU;sCAA0C,EAAE;;;;;;sCAC1D,6LAAC;4BAAI,WAAU;sCACV,eAAe,GAAG,CAAC,CAAC,wBACjB,6LAAC;oCAEG,WAAU;;sDAEV,6LAAC,QAAQ,IAAI;4CAAC,WAAU;;;;;;wCACvB,QAAQ,KAAK;;mCAJT,QAAQ,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;AAapD;GA5dwB;;QACV,yMAAA,CAAA,kBAAe;QAIgB,mIAAA,CAAA,mBAAgB;QACG,wIAAA,CAAA,wBAAqB;QAChE,+HAAA,CAAA,eAAY;QACZ,4HAAA,CAAA,cAAW;QACR,yLAAA,CAAA,iBAAc;QAUX,iLAAA,CAAA,cAAW;QA4BT,iLAAA,CAAA,cAAW;;;MA/ChB", "debugId": null}}, {"offset": {"line": 2478, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/shared/rich-text-display.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport \"@/styles/rich-text.css\";\r\n\r\ninterface RichTextDisplayProps {\r\n    content: string;\r\n    className?: string;\r\n    locale?: 'ar' | 'en';\r\n    size?: 'sm' | 'md' | 'lg' | 'xl';\r\n    variant?: 'default' | 'card' | 'inline' | 'quote';\r\n    color?: string;\r\n    maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | 'full' | 'none';\r\n    animate?: boolean;\r\n}\r\n\r\nconst RichTextDisplay: React.FC<RichTextDisplayProps> = ({\r\n    content,\r\n    className,\r\n    locale = 'en',\r\n    size = 'md',\r\n    variant = 'default',\r\n    color,\r\n    maxWidth = 'none',\r\n    animate = false,\r\n}) => {\r\n    const [isClient, setIsClient] = useState(false);\r\n    const [domPurify, setDomPurify] = useState<any>(null);\r\n\r\n    // Ensure this only runs on client side\r\n    useEffect(() => {\r\n        setIsClient(true);\r\n\r\n        // Dynamic import of DOMPurify\r\n        import('dompurify').then((DOMPurify) => {\r\n            setDomPurify(DOMPurify.default);\r\n        }).catch((error) => {\r\n            console.warn('Failed to load DOMPurify:', error);\r\n        });\r\n    }, []);\r\n\r\n    // Sanitize HTML content to prevent XSS attacks\r\n    const sanitizedContent = React.useMemo(() => {\r\n        if (!content) return '';\r\n\r\n        // Return unsanitized content on server or if DOMPurify is not loaded\r\n        if (!isClient || !domPurify) {\r\n            return content;\r\n        }\r\n\r\n        try {\r\n            return domPurify.sanitize(content, {\r\n                ALLOWED_TAGS: [\r\n                    'p', 'br', 'strong', 'em', 'u', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',\r\n                    'ul', 'ol', 'li', 'a', 'img', 'blockquote', 'code', 'pre',\r\n                    'span', 'div', 'table', 'thead', 'tbody', 'tr', 'td', 'th'\r\n                ],\r\n                ALLOWED_ATTR: [\r\n                    'href', 'src', 'alt', 'title', 'target', 'rel', 'class', 'style',\r\n                    'data-*', 'width', 'height'\r\n                ],\r\n                ALLOW_DATA_ATTR: true,\r\n            });\r\n        } catch (error) {\r\n            console.warn('DOMPurify sanitization failed:', error);\r\n            return content;\r\n        }\r\n    }, [content, isClient, domPurify]);\r\n\r\n    // Size variants with explicit text sizing\r\n    const sizeVariants = {\r\n        sm: 'prose-sm text-sm',\r\n        md: 'prose text-base',\r\n        lg: 'prose-lg text-lg',\r\n        xl: 'prose-xl text-xl',\r\n    };\r\n\r\n    // Max width variants\r\n    const maxWidthVariants = {\r\n        sm: 'max-w-sm',\r\n        md: 'max-w-md',\r\n        lg: 'max-w-lg',\r\n        xl: 'max-w-xl',\r\n        full: 'max-w-full',\r\n        none: 'max-w-none',\r\n    };\r\n\r\n    // Variant styles\r\n    const variantStyles = {\r\n        default: 'prose-gray',\r\n        card: 'prose-gray bg-white p-6 rounded-lg shadow-sm border',\r\n        inline: 'prose-gray inline-block',\r\n        quote: 'prose-gray bg-gray-50 p-6 rounded-lg border-l-4 border-blue-500 italic',\r\n    };\r\n\r\n    // RTL support\r\n    const direction = locale === 'ar' ? 'rtl' : 'ltr';\r\n    const textAlign = locale === 'ar' ? 'text-right' : 'text-left';\r\n\r\n    // Base prose classes with customizations\r\n    const proseClasses = cn(\r\n        // Base prose styling\r\n        'rich-text-display',\r\n        sizeVariants[size],\r\n        maxWidthVariants[maxWidth],\r\n        variantStyles[variant],\r\n\r\n        // RTL and alignment\r\n        textAlign,\r\n\r\n        // Animation\r\n        animate && 'animate-fade-in',\r\n\r\n        // Variant-specific classes\r\n        variant === 'card' && 'rich-text-variant-card',\r\n        variant === 'quote' && 'rich-text-variant-quote',\r\n\r\n        // Custom prose modifications\r\n        'prose-headings:font-bold',\r\n        'prose-p:leading-relaxed',\r\n        'prose-a:text-blue-600 prose-a:no-underline hover:prose-a:underline',\r\n        'prose-strong:font-semibold',\r\n        'prose-em:italic',\r\n        'prose-blockquote:border-l-4 prose-blockquote:border-gray-300 prose-blockquote:pl-4',\r\n        'prose-code:bg-gray-100 prose-code:px-1 prose-code:py-0.5 prose-code:rounded',\r\n        'prose-pre:bg-gray-100 prose-pre:p-4 prose-pre:rounded-lg',\r\n        'prose-img:rounded-lg prose-img:shadow-sm',\r\n        'prose-table:border-collapse prose-table:border prose-table:border-gray-300',\r\n        'prose-th:border prose-th:border-gray-300 prose-th:bg-gray-50 prose-th:p-2',\r\n        'prose-td:border prose-td:border-gray-300 prose-td:p-2',\r\n\r\n        // Dark mode support\r\n        'dark:prose-invert',\r\n        'dark:prose-a:text-blue-400',\r\n        'dark:prose-code:bg-gray-800',\r\n        'dark:prose-pre:bg-gray-800',\r\n        'dark:prose-blockquote:border-gray-600',\r\n        'dark:prose-th:bg-gray-800',\r\n\r\n        className\r\n    );\r\n\r\n    // Font size mapping for more precise control\r\n    const fontSizeMap = {\r\n        sm: '0.875rem', // 14px\r\n        md: '1rem',     // 16px\r\n        lg: '1.125rem', // 18px\r\n        xl: '1.25rem',  // 20px\r\n    };\r\n\r\n    // Custom CSS variables for color theming and font sizing\r\n    const customStyles = {\r\n        fontSize: fontSizeMap[size],\r\n        lineHeight: size === 'sm' ? '1.5' : size === 'xl' ? '1.7' : '1.6',\r\n        '--prose-body': '#1f2937', // gray-800\r\n        '--prose-headings': color || '#1f2937', // gray-800\r\n        '--prose-bold': color || '#1f2937', // gray-800\r\n        '--prose-links': color || '#2563eb', // keep links blue\r\n        '--prose-code': '#1f2937', // gray-800\r\n        '--prose-pre': '#1f2937', // gray-800\r\n        '--prose-th': '#1f2937', // gray-800\r\n        '--prose-td': '#1f2937', // gray-800\r\n        '--prose-blockquote': '#1f2937', // gray-800\r\n        '--prose-li': '#1f2937', // gray-800\r\n        '--prose-p': '#1f2937', // gray-800\r\n        color: '#1f2937', // fallback gray-800\r\n    } as React.CSSProperties;\r\n\r\n    // Return early if no content\r\n    if (!content || !sanitizedContent) {\r\n        return null;\r\n    }\r\n\r\n    return (\r\n        <div\r\n            className={proseClasses}\r\n            dir={direction}\r\n            style={customStyles}\r\n            dangerouslySetInnerHTML={{ __html: sanitizedContent }}\r\n        />\r\n    );\r\n};\r\n\r\nexport default RichTextDisplay; "], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;;AAiBA,MAAM,kBAAkD,CAAC,EACrD,OAAO,EACP,SAAS,EACT,SAAS,IAAI,EACb,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,KAAK,EACL,WAAW,MAAM,EACjB,UAAU,KAAK,EAClB;;IACG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAEhD,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACN,YAAY;YAEZ,8BAA8B;YAC9B,gJAAoB,IAAI;6CAAC,CAAC;oBACtB,aAAa,UAAU,OAAO;gBAClC;4CAAG,KAAK;6CAAC,CAAC;oBACN,QAAQ,IAAI,CAAC,6BAA6B;gBAC9C;;QACJ;oCAAG,EAAE;IAEL,+CAA+C;IAC/C,MAAM,mBAAmB,6JAAA,CAAA,UAAK,CAAC,OAAO;qDAAC;YACnC,IAAI,CAAC,SAAS,OAAO;YAErB,qEAAqE;YACrE,IAAI,CAAC,YAAY,CAAC,WAAW;gBACzB,OAAO;YACX;YAEA,IAAI;gBACA,OAAO,UAAU,QAAQ,CAAC,SAAS;oBAC/B,cAAc;wBACV;wBAAK;wBAAM;wBAAU;wBAAM;wBAAK;wBAAM;wBAAM;wBAAM;wBAAM;wBAAM;wBAC9D;wBAAM;wBAAM;wBAAM;wBAAK;wBAAO;wBAAc;wBAAQ;wBACpD;wBAAQ;wBAAO;wBAAS;wBAAS;wBAAS;wBAAM;wBAAM;qBACzD;oBACD,cAAc;wBACV;wBAAQ;wBAAO;wBAAO;wBAAS;wBAAU;wBAAO;wBAAS;wBACzD;wBAAU;wBAAS;qBACtB;oBACD,iBAAiB;gBACrB;YACJ,EAAE,OAAO,OAAO;gBACZ,QAAQ,IAAI,CAAC,kCAAkC;gBAC/C,OAAO;YACX;QACJ;oDAAG;QAAC;QAAS;QAAU;KAAU;IAEjC,0CAA0C;IAC1C,MAAM,eAAe;QACjB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACR;IAEA,qBAAqB;IACrB,MAAM,mBAAmB;QACrB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM;QACN,MAAM;IACV;IAEA,iBAAiB;IACjB,MAAM,gBAAgB;QAClB,SAAS;QACT,MAAM;QACN,QAAQ;QACR,OAAO;IACX;IAEA,cAAc;IACd,MAAM,YAAY,WAAW,OAAO,QAAQ;IAC5C,MAAM,YAAY,WAAW,OAAO,eAAe;IAEnD,yCAAyC;IACzC,MAAM,eAAe,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAClB,qBAAqB;IACrB,qBACA,YAAY,CAAC,KAAK,EAClB,gBAAgB,CAAC,SAAS,EAC1B,aAAa,CAAC,QAAQ,EAEtB,oBAAoB;IACpB,WAEA,YAAY;IACZ,WAAW,mBAEX,2BAA2B;IAC3B,YAAY,UAAU,0BACtB,YAAY,WAAW,2BAEvB,6BAA6B;IAC7B,4BACA,2BACA,sEACA,8BACA,mBACA,sFACA,+EACA,4DACA,4CACA,8EACA,6EACA,yDAEA,oBAAoB;IACpB,qBACA,8BACA,+BACA,8BACA,yCACA,6BAEA;IAGJ,6CAA6C;IAC7C,MAAM,cAAc;QAChB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACR;IAEA,yDAAyD;IACzD,MAAM,eAAe;QACjB,UAAU,WAAW,CAAC,KAAK;QAC3B,YAAY,SAAS,OAAO,QAAQ,SAAS,OAAO,QAAQ;QAC5D,gBAAgB;QAChB,oBAAoB,SAAS;QAC7B,gBAAgB,SAAS;QACzB,iBAAiB,SAAS;QAC1B,gBAAgB;QAChB,eAAe;QACf,cAAc;QACd,cAAc;QACd,sBAAsB;QACtB,cAAc;QACd,aAAa;QACb,OAAO;IACX;IAEA,6BAA6B;IAC7B,IAAI,CAAC,WAAW,CAAC,kBAAkB;QAC/B,OAAO;IACX;IAEA,qBACI,6LAAC;QACG,WAAW;QACX,KAAK;QACL,OAAO;QACP,yBAAyB;YAAE,QAAQ;QAAiB;;;;;;AAGhE;GArKM;KAAA;uCAuKS", "debugId": null}}, {"offset": {"line": 2663, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/property-details/sections/property-description.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { Card, CardContent } from '@/components/ui/card'\r\nimport RichTextDisplay from '@/components/shared/rich-text-display'\r\nimport { Star } from 'lucide-react'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\n\r\ninterface PropertyDescriptionProps {\r\n    property: any\r\n}\r\n\r\nexport default function PropertyDescription({ property }: PropertyDescriptionProps) {\r\n    const locale = useLocale()\r\n    const t = useTranslations('propertyDetails.description')\r\n\r\n    const getGenderText = () => {\r\n        switch (property.genderRequired) {\r\n            case 'male':\r\n                return t('maleText')\r\n            case 'female':\r\n                return t('femaleText')\r\n            default:\r\n                return t('anyoneText')\r\n        }\r\n    }\r\n\r\n    return (\r\n        <div className=\"space-y-6\">\r\n            {/* Property Description */}\r\n            <Card className=\"border-0 shadow-sm\">\r\n                <CardContent className=\"p-6\">\r\n                    <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">{t('title')}</h2>\r\n\r\n                    {property.description ? (\r\n                        <div className=\"prose prose-gray-600 max-w-none\">\r\n                            <RichTextDisplay\r\n                                content={property.description}\r\n                                size=\"md\"\r\n                                variant=\"default\"\r\n                                maxWidth=\"none\"\r\n                                locale={locale as 'ar' | 'en'}\r\n                            />\r\n                        </div>\r\n                    ) : (\r\n                        <p className=\"text-gray-600\">\r\n                            {t('defaultDescription', {\r\n                                type: property.type,\r\n                                city: property.city,\r\n                                country: property.country,\r\n                                totalRooms: property.totalRooms,\r\n                                genderText: getGenderText()\r\n                            })}\r\n                        </p>\r\n                    )}\r\n                </CardContent>\r\n            </Card>\r\n\r\n            {/* Terms and Conditions */}\r\n            {property.termsAndConditions && (\r\n                <Card className=\"border-0 shadow-sm\">\r\n                    <CardContent className=\"p-6\">\r\n                        <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">{t('termsAndConditions')}</h2>\r\n                        <div className=\"prose prose-gray max-w-none\">\r\n                            <RichTextDisplay\r\n                                content={property.termsAndConditions}\r\n                                size=\"sm\"\r\n                                variant=\"default\"\r\n                                maxWidth=\"none\"\r\n                                locale={locale as 'ar' | 'en'}\r\n                            />\r\n                        </div>\r\n                    </CardContent>\r\n                </Card>\r\n            )}\r\n        </div>\r\n    )\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;;;AALA;;;;AAWe,SAAS,oBAAoB,EAAE,QAAQ,EAA4B;;IAC9E,MAAM,SAAS,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD;IACvB,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,MAAM,gBAAgB;QAClB,OAAQ,SAAS,cAAc;YAC3B,KAAK;gBACD,OAAO,EAAE;YACb,KAAK;gBACD,OAAO,EAAE;YACb;gBACI,OAAO,EAAE;QACjB;IACJ;IAEA,qBACI,6LAAC;QAAI,WAAU;;0BAEX,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACZ,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACnB,6LAAC;4BAAG,WAAU;sCAA4C,EAAE;;;;;;wBAE3D,SAAS,WAAW,iBACjB,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC,0JAAA,CAAA,UAAe;gCACZ,SAAS,SAAS,WAAW;gCAC7B,MAAK;gCACL,SAAQ;gCACR,UAAS;gCACT,QAAQ;;;;;;;;;;iDAIhB,6LAAC;4BAAE,WAAU;sCACR,EAAE,sBAAsB;gCACrB,MAAM,SAAS,IAAI;gCACnB,MAAM,SAAS,IAAI;gCACnB,SAAS,SAAS,OAAO;gCACzB,YAAY,SAAS,UAAU;gCAC/B,YAAY;4BAChB;;;;;;;;;;;;;;;;;YAOf,SAAS,kBAAkB,kBACxB,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACZ,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACnB,6LAAC;4BAAG,WAAU;sCAA4C,EAAE;;;;;;sCAC5D,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC,0JAAA,CAAA,UAAe;gCACZ,SAAS,SAAS,kBAAkB;gCACpC,MAAK;gCACL,SAAQ;gCACR,UAAS;gCACT,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxC;GAjEwB;;QACL,qKAAA,CAAA,YAAS;QACd,yMAAA,CAAA,kBAAe;;;KAFL", "debugId": null}}, {"offset": {"line": 2816, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/property-details/sections/property-features.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { Card, CardContent } from '@/components/ui/card'\r\nimport { useTranslations } from 'next-intl'\r\nimport {\r\n    Wifi,\r\n    Car,\r\n    Wind,\r\n    Building,\r\n    Users,\r\n    Droplets,\r\n    Flame,\r\n    CheckCircle,\r\n    XCircle,\r\n    Bed,\r\n    Bath,\r\n    Home,\r\n    Sofa,\r\n    Cigarette,\r\n    UserCheck,\r\n    Clock,\r\n    Globe,\r\n    Train,\r\n    ShoppingCart\r\n} from 'lucide-react'\r\nimport { cn } from '@/lib/utils'\r\nimport { useCurrencyState } from '@/hooks/useCurrencyState'\r\n\r\ninterface PropertyFeaturesProps {\r\n    property: any\r\n}\r\n\r\nexport default function PropertyFeatures({ property }: PropertyFeaturesProps) {\r\n    const t = useTranslations('propertyDetails.features')\r\n    const { formatPropertyPrice, isReady } = useCurrencyState()\r\n\r\n    // Helper function to format time periods\r\n    const formatTimePeriod = (period: string) => {\r\n        if (!period) return t('timePeriods.monthly')\r\n\r\n        const periodMap: { [key: string]: string } = {\r\n            'monthly': t('timePeriods.monthly'),\r\n            'quarterly': t('timePeriods.quarterly'),\r\n            'yearly': t('timePeriods.yearly'),\r\n            'weekly': t('timePeriods.weekly'),\r\n            'daily': t('timePeriods.daily')\r\n        }\r\n\r\n        return periodMap[period.toLowerCase()] || period\r\n    }\r\n\r\n    // Interior Features\r\n    const interiorFeatures = [\r\n        {\r\n            category: t('categories.roomAmenities'),\r\n            items: [\r\n                { key: 'includeFurniture', icon: Sofa, label: t('amenities.furnished'), value: property.includeFurniture },\r\n                { key: 'airConditioning', icon: Wind, label: t('amenities.airConditioning'), value: property.airConditioning },\r\n                { key: 'includeWaterHeater', icon: Flame, label: t('amenities.waterHeater'), value: property.includeWaterHeater },\r\n                { key: 'internet', icon: Wifi, label: t('amenities.wifiInternet'), value: property.internet },\r\n            ]\r\n        },\r\n        {\r\n            category: t('categories.bathroomWater'),\r\n            items: [\r\n                { key: 'separatedBathroom', icon: Bath, label: t('amenities.privateBathroom'), value: property.separatedBathroom },\r\n                { key: 'priceIncludeWaterAndElectricity', icon: Droplets, label: t('amenities.utilitiesIncluded'), value: property.priceIncludeWaterAndElectricity },\r\n            ]\r\n        },\r\n        {\r\n            category: t('categories.livingPolicies'),\r\n            items: [\r\n                { key: 'allowSmoking', icon: Cigarette, label: t('amenities.smokingAllowed'), value: property.allowSmoking },\r\n                { key: 'trialPeriod', icon: Clock, label: t('amenities.trialPeriod'), value: property.trialPeriod },\r\n                { key: 'goodForForeigners', icon: Globe, label: t('amenities.foreignerFriendly'), value: property.goodForForeigners },\r\n            ]\r\n        }\r\n    ]\r\n\r\n    // Building Features\r\n    const buildingFeatures = [\r\n        {\r\n            category: t('categories.buildingAmenities'),\r\n            items: [\r\n                { key: 'elevator', icon: Building, label: t('amenities.elevator'), value: property.elevator },\r\n                { key: 'parking', icon: Car, label: t('amenities.parking'), value: property.parking },\r\n            ]\r\n        },\r\n        {\r\n            category: t('categories.locationBenefits'),\r\n            items: [\r\n                { key: 'nearToMetro', icon: Train, label: t('amenities.nearMetro'), value: property.nearToMetro },\r\n                { key: 'nearToMarket', icon: ShoppingCart, label: t('amenities.nearMarket'), value: property.nearToMarket },\r\n            ]\r\n        }\r\n    ]\r\n\r\n    // Property Specifications\r\n    const specifications = [\r\n        { label: t('specifications.propertyType'), value: property.type, icon: Home },\r\n        { label: t('specifications.roomType'), value: property.roomType, icon: Bed },\r\n        { label: t('specifications.totalRooms'), value: property.totalRooms, icon: Home },\r\n        { label: t('specifications.availableRooms'), value: property.availableRooms, icon: Bed },\r\n        { label: t('specifications.bathrooms'), value: property.bathrooms, icon: Bath },\r\n        { label: t('specifications.floor'), value: property.floor, icon: Building },\r\n        { label: t('specifications.size'), value: property.size, icon: Home },\r\n        { label: t('specifications.currentResidents'), value: property.residentsCount, icon: Users },\r\n        { label: t('specifications.availableSpots'), value: property.availablePersons, icon: UserCheck },\r\n        { label: t('specifications.genderRequirement'), value: property.genderRequired === 'mixed' ? t('genderOptions.mixed') : property.genderRequired, icon: Users },\r\n    ].filter(spec => spec.value)\r\n\r\n    // Render feature item\r\n    const renderFeatureItem = (item: any) => (\r\n        <div\r\n            key={item.key}\r\n            className={cn(\r\n                \"flex items-center gap-3 p-3 rounded-lg border transition-colors\",\r\n                item.value\r\n                    ? \"bg-green-50 border-green-200 text-green-800\"\r\n                    : \"bg-gray-50 border-gray-200 text-gray-500\"\r\n            )}\r\n        >\r\n            <div className={cn(\r\n                \"flex-shrink-0\",\r\n                item.value ? \"text-green-600\" : \"text-gray-400\"\r\n            )}>\r\n                <item.icon className=\"h-5 w-5\" />\r\n            </div>\r\n\r\n            <div className=\"flex-1 min-w-0\">\r\n                <div className=\"flex items-center gap-2\">\r\n                    <span className=\"text-sm font-medium\">{item.label}</span>\r\n                    {item.value ? (\r\n                        <CheckCircle className=\"h-4 w-4 text-green-600\" />\r\n                    ) : (\r\n                        <XCircle className=\"h-4 w-4 text-gray-400\" />\r\n                    )}\r\n                </div>\r\n            </div>\r\n        </div>\r\n    )\r\n\r\n    return (\r\n        <div className=\"space-y-6\">\r\n            {/* Property Specifications */}\r\n            <Card className=\"border-0 shadow-sm\">\r\n                <CardContent className=\"p-6\">\r\n                    <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">{t('specificationsTitle')}</h2>\r\n\r\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n                        {specifications.map((spec, index) => (\r\n                            <div key={index} className=\"flex items-center gap-3 p-3 bg-gray-50 rounded-lg\">\r\n                                <div className=\"flex-shrink-0\">\r\n                                    <spec.icon className=\"h-5 w-5 text-gray-600\" />\r\n                                </div>\r\n                                <div className=\"flex-1 min-w-0\">\r\n                                    <div className=\"text-sm text-gray-600\">{spec.label}</div>\r\n                                    <div className=\"font-medium text-gray-900 capitalize\">{spec.value}</div>\r\n                                </div>\r\n                            </div>\r\n                        ))}\r\n                    </div>\r\n                </CardContent>\r\n            </Card>\r\n\r\n            {/* Interior Features */}\r\n            <Card className=\"border-0 shadow-sm\">\r\n                <CardContent className=\"p-6\">\r\n                    <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">{t('interiorFeaturesTitle')}</h2>\r\n\r\n                    <div className=\"space-y-6\">\r\n                        {interiorFeatures.map((category, categoryIndex) => (\r\n                            <div key={categoryIndex}>\r\n                                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">{category.category}</h3>\r\n                                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\r\n                                    {category.items.map(renderFeatureItem)}\r\n                                </div>\r\n                            </div>\r\n                        ))}\r\n                    </div>\r\n                </CardContent>\r\n            </Card>\r\n\r\n            {/* Building & Location Features */}\r\n            <Card className=\"border-0 shadow-sm\">\r\n                <CardContent className=\"p-6\">\r\n                    <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">{t('buildingFeaturesTitle')}</h2>\r\n\r\n                    <div className=\"space-y-6\">\r\n                        {buildingFeatures.map((category, categoryIndex) => (\r\n                            <div key={categoryIndex}>\r\n                                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">{category.category}</h3>\r\n                                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\r\n                                    {category.items.map(renderFeatureItem)}\r\n                                </div>\r\n                            </div>\r\n                        ))}\r\n                    </div>\r\n\r\n                </CardContent>\r\n            </Card>\r\n\r\n            {/* Rental Terms */}\r\n            <Card className=\"border-0 shadow-sm\">\r\n                <CardContent className=\"p-6\">\r\n                    <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">{t('rentalTermsTitle')}</h2>\r\n\r\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                        <div className=\"space-y-3\">\r\n                            <h3 className=\"text-lg font-medium text-gray-900\">{t('rentalTerms.paymentTerms')}</h3>\r\n                            <div className=\"space-y-2\">\r\n                                <div className=\"flex justify-between\">\r\n                                    <span className=\"text-sm text-gray-600\">{t('rentalTerms.paymentPeriod')}</span>\r\n                                    <span className=\"text-sm font-medium\">{formatTimePeriod(property.rentTime || property.paymentTime)}</span>\r\n                                </div>\r\n                                {property.price && (\r\n                                    <div className=\"flex justify-between\">\r\n                                        <span className=\"text-sm text-gray-600\">{t('rentalTerms.monthlyRent')}</span>\r\n                                        <span className=\"text-sm font-medium\">\r\n                                            {isReady ? formatPropertyPrice(property.price, property.country || property.city || 'Default') : `$${property.price}`}\r\n                                        </span>\r\n                                    </div>\r\n                                )}\r\n                                {property.deposit && (\r\n                                    <div className=\"flex justify-between\">\r\n                                        <span className=\"text-sm text-gray-600\">{t('rentalTerms.deposit')}</span>\r\n                                        <span className=\"text-sm font-medium\">\r\n                                            {isReady ? formatPropertyPrice(property.deposit, property.country || property.city || 'Default') : `$${property.deposit}`}\r\n                                        </span>\r\n                                    </div>\r\n                                )}\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div className=\"space-y-3\">\r\n                            <h3 className=\"text-lg font-medium text-gray-900\">{t('rentalTerms.occupancy')}</h3>\r\n                            <div className=\"space-y-2\">\r\n                                <div className=\"flex justify-between\">\r\n                                    <span className=\"text-sm text-gray-600\">{t('rentalTerms.currentResidents')}</span>\r\n                                    <span className=\"text-sm font-medium\">{property.residentsCount || 0}</span>\r\n                                </div>\r\n                                <div className=\"flex justify-between\">\r\n                                    <span className=\"text-sm text-gray-600\">{t('rentalTerms.availableSpots')}</span>\r\n                                    <span className=\"text-sm font-medium\">{property.availablePersons || 0}</span>\r\n                                </div>\r\n                                <div className=\"flex justify-between\">\r\n                                    <span className=\"text-sm text-gray-600\">{t('rentalTerms.genderRequirement')}</span>\r\n                                    <span className=\"text-sm font-medium capitalize\">{property.genderRequired || 'Mixed'}</span>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </CardContent>\r\n            </Card>\r\n        </div>\r\n    )\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqBA;AACA;;;AA1BA;;;;;;AAgCe,SAAS,iBAAiB,EAAE,QAAQ,EAAyB;;IACxE,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,EAAE,mBAAmB,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD;IAExD,yCAAyC;IACzC,MAAM,mBAAmB,CAAC;QACtB,IAAI,CAAC,QAAQ,OAAO,EAAE;QAEtB,MAAM,YAAuC;YACzC,WAAW,EAAE;YACb,aAAa,EAAE;YACf,UAAU,EAAE;YACZ,UAAU,EAAE;YACZ,SAAS,EAAE;QACf;QAEA,OAAO,SAAS,CAAC,OAAO,WAAW,GAAG,IAAI;IAC9C;IAEA,oBAAoB;IACpB,MAAM,mBAAmB;QACrB;YACI,UAAU,EAAE;YACZ,OAAO;gBACH;oBAAE,KAAK;oBAAoB,MAAM,qMAAA,CAAA,OAAI;oBAAE,OAAO,EAAE;oBAAwB,OAAO,SAAS,gBAAgB;gBAAC;gBACzG;oBAAE,KAAK;oBAAmB,MAAM,qMAAA,CAAA,OAAI;oBAAE,OAAO,EAAE;oBAA8B,OAAO,SAAS,eAAe;gBAAC;gBAC7G;oBAAE,KAAK;oBAAsB,MAAM,uMAAA,CAAA,QAAK;oBAAE,OAAO,EAAE;oBAA0B,OAAO,SAAS,kBAAkB;gBAAC;gBAChH;oBAAE,KAAK;oBAAY,MAAM,qMAAA,CAAA,OAAI;oBAAE,OAAO,EAAE;oBAA2B,OAAO,SAAS,QAAQ;gBAAC;aAC/F;QACL;QACA;YACI,UAAU,EAAE;YACZ,OAAO;gBACH;oBAAE,KAAK;oBAAqB,MAAM,qMAAA,CAAA,OAAI;oBAAE,OAAO,EAAE;oBAA8B,OAAO,SAAS,iBAAiB;gBAAC;gBACjH;oBAAE,KAAK;oBAAmC,MAAM,6MAAA,CAAA,WAAQ;oBAAE,OAAO,EAAE;oBAAgC,OAAO,SAAS,+BAA+B;gBAAC;aACtJ;QACL;QACA;YACI,UAAU,EAAE;YACZ,OAAO;gBACH;oBAAE,KAAK;oBAAgB,MAAM,+MAAA,CAAA,YAAS;oBAAE,OAAO,EAAE;oBAA6B,OAAO,SAAS,YAAY;gBAAC;gBAC3G;oBAAE,KAAK;oBAAe,MAAM,uMAAA,CAAA,QAAK;oBAAE,OAAO,EAAE;oBAA0B,OAAO,SAAS,WAAW;gBAAC;gBAClG;oBAAE,KAAK;oBAAqB,MAAM,uMAAA,CAAA,QAAK;oBAAE,OAAO,EAAE;oBAAgC,OAAO,SAAS,iBAAiB;gBAAC;aACvH;QACL;KACH;IAED,oBAAoB;IACpB,MAAM,mBAAmB;QACrB;YACI,UAAU,EAAE;YACZ,OAAO;gBACH;oBAAE,KAAK;oBAAY,MAAM,6MAAA,CAAA,WAAQ;oBAAE,OAAO,EAAE;oBAAuB,OAAO,SAAS,QAAQ;gBAAC;gBAC5F;oBAAE,KAAK;oBAAW,MAAM,mMAAA,CAAA,MAAG;oBAAE,OAAO,EAAE;oBAAsB,OAAO,SAAS,OAAO;gBAAC;aACvF;QACL;QACA;YACI,UAAU,EAAE;YACZ,OAAO;gBACH;oBAAE,KAAK;oBAAe,MAAM,+MAAA,CAAA,QAAK;oBAAE,OAAO,EAAE;oBAAwB,OAAO,SAAS,WAAW;gBAAC;gBAChG;oBAAE,KAAK;oBAAgB,MAAM,yNAAA,CAAA,eAAY;oBAAE,OAAO,EAAE;oBAAyB,OAAO,SAAS,YAAY;gBAAC;aAC7G;QACL;KACH;IAED,0BAA0B;IAC1B,MAAM,iBAAiB;QACnB;YAAE,OAAO,EAAE;YAAgC,OAAO,SAAS,IAAI;YAAE,MAAM,sMAAA,CAAA,OAAI;QAAC;QAC5E;YAAE,OAAO,EAAE;YAA4B,OAAO,SAAS,QAAQ;YAAE,MAAM,mMAAA,CAAA,MAAG;QAAC;QAC3E;YAAE,OAAO,EAAE;YAA8B,OAAO,SAAS,UAAU;YAAE,MAAM,sMAAA,CAAA,OAAI;QAAC;QAChF;YAAE,OAAO,EAAE;YAAkC,OAAO,SAAS,cAAc;YAAE,MAAM,mMAAA,CAAA,MAAG;QAAC;QACvF;YAAE,OAAO,EAAE;YAA6B,OAAO,SAAS,SAAS;YAAE,MAAM,qMAAA,CAAA,OAAI;QAAC;QAC9E;YAAE,OAAO,EAAE;YAAyB,OAAO,SAAS,KAAK;YAAE,MAAM,6MAAA,CAAA,WAAQ;QAAC;QAC1E;YAAE,OAAO,EAAE;YAAwB,OAAO,SAAS,IAAI;YAAE,MAAM,sMAAA,CAAA,OAAI;QAAC;QACpE;YAAE,OAAO,EAAE;YAAoC,OAAO,SAAS,cAAc;YAAE,MAAM,uMAAA,CAAA,QAAK;QAAC;QAC3F;YAAE,OAAO,EAAE;YAAkC,OAAO,SAAS,gBAAgB;YAAE,MAAM,mNAAA,CAAA,YAAS;QAAC;QAC/F;YAAE,OAAO,EAAE;YAAqC,OAAO,SAAS,cAAc,KAAK,UAAU,EAAE,yBAAyB,SAAS,cAAc;YAAE,MAAM,uMAAA,CAAA,QAAK;QAAC;KAChK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK;IAE3B,sBAAsB;IACtB,MAAM,oBAAoB,CAAC,qBACvB,6LAAC;YAEG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,mEACA,KAAK,KAAK,GACJ,gDACA;;8BAGV,6LAAC;oBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACb,iBACA,KAAK,KAAK,GAAG,mBAAmB;8BAEhC,cAAA,6LAAC,KAAK,IAAI;wBAAC,WAAU;;;;;;;;;;;8BAGzB,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAK,WAAU;0CAAuB,KAAK,KAAK;;;;;;4BAChD,KAAK,KAAK,iBACP,6LAAC,8NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAEvB,6LAAC,+MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;;;;;;;;WArB1B,KAAK,GAAG;;;;;IA4BrB,qBACI,6LAAC;QAAI,WAAU;;0BAEX,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACZ,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACnB,6LAAC;4BAAG,WAAU;sCAA4C,EAAE;;;;;;sCAE5D,6LAAC;4BAAI,WAAU;sCACV,eAAe,GAAG,CAAC,CAAC,MAAM,sBACvB,6LAAC;oCAAgB,WAAU;;sDACvB,6LAAC;4CAAI,WAAU;sDACX,cAAA,6LAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;;;;;;sDAEzB,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;oDAAI,WAAU;8DAAyB,KAAK,KAAK;;;;;;8DAClD,6LAAC;oDAAI,WAAU;8DAAwC,KAAK,KAAK;;;;;;;;;;;;;mCAN/D;;;;;;;;;;;;;;;;;;;;;0BAe1B,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACZ,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACnB,6LAAC;4BAAG,WAAU;sCAA4C,EAAE;;;;;;sCAE5D,6LAAC;4BAAI,WAAU;sCACV,iBAAiB,GAAG,CAAC,CAAC,UAAU,8BAC7B,6LAAC;;sDACG,6LAAC;4CAAG,WAAU;sDAA0C,SAAS,QAAQ;;;;;;sDACzE,6LAAC;4CAAI,WAAU;sDACV,SAAS,KAAK,CAAC,GAAG,CAAC;;;;;;;mCAHlB;;;;;;;;;;;;;;;;;;;;;0BAY1B,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACZ,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACnB,6LAAC;4BAAG,WAAU;sCAA4C,EAAE;;;;;;sCAE5D,6LAAC;4BAAI,WAAU;sCACV,iBAAiB,GAAG,CAAC,CAAC,UAAU,8BAC7B,6LAAC;;sDACG,6LAAC;4CAAG,WAAU;sDAA0C,SAAS,QAAQ;;;;;;sDACzE,6LAAC;4CAAI,WAAU;sDACV,SAAS,KAAK,CAAC,GAAG,CAAC;;;;;;;mCAHlB;;;;;;;;;;;;;;;;;;;;;0BAa1B,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACZ,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACnB,6LAAC;4BAAG,WAAU;sCAA4C,EAAE;;;;;;sCAE5D,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAG,WAAU;sDAAqC,EAAE;;;;;;sDACrD,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAK,WAAU;sEAAyB,EAAE;;;;;;sEAC3C,6LAAC;4DAAK,WAAU;sEAAuB,iBAAiB,SAAS,QAAQ,IAAI,SAAS,WAAW;;;;;;;;;;;;gDAEpG,SAAS,KAAK,kBACX,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAK,WAAU;sEAAyB,EAAE;;;;;;sEAC3C,6LAAC;4DAAK,WAAU;sEACX,UAAU,oBAAoB,SAAS,KAAK,EAAE,SAAS,OAAO,IAAI,SAAS,IAAI,IAAI,aAAa,CAAC,CAAC,EAAE,SAAS,KAAK,EAAE;;;;;;;;;;;;gDAIhI,SAAS,OAAO,kBACb,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAK,WAAU;sEAAyB,EAAE;;;;;;sEAC3C,6LAAC;4DAAK,WAAU;sEACX,UAAU,oBAAoB,SAAS,OAAO,EAAE,SAAS,OAAO,IAAI,SAAS,IAAI,IAAI,aAAa,CAAC,CAAC,EAAE,SAAS,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;8CAO7I,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAG,WAAU;sDAAqC,EAAE;;;;;;sDACrD,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAK,WAAU;sEAAyB,EAAE;;;;;;sEAC3C,6LAAC;4DAAK,WAAU;sEAAuB,SAAS,cAAc,IAAI;;;;;;;;;;;;8DAEtE,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAK,WAAU;sEAAyB,EAAE;;;;;;sEAC3C,6LAAC;4DAAK,WAAU;sEAAuB,SAAS,gBAAgB,IAAI;;;;;;;;;;;;8DAExE,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAK,WAAU;sEAAyB,EAAE;;;;;;sEAC3C,6LAAC;4DAAK,WAAU;sEAAkC,SAAS,cAAc,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjH;GAhOwB;;QACV,yMAAA,CAAA,kBAAe;QACgB,mIAAA,CAAA,mBAAgB;;;KAFrC", "debugId": null}}, {"offset": {"line": 3542, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/property-details/sections/property-map.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useState } from 'react'\r\nimport { Card, CardContent } from '@/components/ui/card'\r\nimport { Button } from '@/components/ui/button'\r\nimport { MapPin, Navigation } from 'lucide-react'\r\nimport dynamic from 'next/dynamic'\r\nimport { useTranslations } from 'next-intl'\r\n\r\n\r\ninterface PropertyMapProps {\r\n    property: any\r\n}\r\n\r\nexport default function PropertyMap({ property }: PropertyMapProps) {\r\n    const t = useTranslations('propertyDetails.map')\r\n\r\n    const lat = parseFloat(property.latitude) || 0\r\n    const lng = parseFloat(property.longitude) || 0\r\n\r\n    return (\r\n        <Card className=\"border-0 shadow-sm\">\r\n            <CardContent className=\"p-6\">\r\n                <div className=\"flex items-center gap-2 mb-6\">\r\n                    <MapPin className=\"h-5 w-5 text-gray-600\" />\r\n                    <h2 className=\"text-xl font-semibold text-gray-900\">{t('title')}</h2>\r\n                </div>\r\n\r\n                {/* Address Info */}\r\n                <div className=\"mb-6 p-4 bg-gray-50 rounded-lg\">\r\n                    <div className=\"flex items-start gap-3\">\r\n                        <MapPin className=\"h-5 w-5 text-gray-600 mt-0.5\" />\r\n                        <div>\r\n                            <h3 className=\"font-medium text-gray-900\">{property.title}</h3>\r\n                            <p className=\"text-gray-600 mt-1\">{property.address}</p>\r\n                            <p className=\"text-sm text-gray-500 mt-1\">\r\n                                {property.city}, {property.country}\r\n                            </p>\r\n                            <div className=\"flex items-center gap-4 mt-2\">\r\n                                <span className=\"text-sm text-gray-500\">\r\n                                    {t('coordinates')}: {lat.toFixed(6)}, {lng.toFixed(6)}\r\n                                </span>\r\n                                <Button\r\n                                    variant=\"outline\"\r\n                                    size=\"sm\"\r\n                                    onClick={() => window.open(`https://maps.google.com/?q=${lat},${lng}`, '_blank')}\r\n                                >\r\n                                    <Navigation className=\"h-4 w-4 mr-1\" />\r\n                                    {t('getDirections')}\r\n                                </Button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Interactive Map */}\r\n                {/* <div className=\"mb-6\">\r\n                    {!showMap ? (\r\n                        <div className=\"h-64 bg-gray-100 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300\">\r\n                            <div className=\"text-center\">\r\n                                <MapPin className=\"h-8 w-8 mx-auto text-gray-400 mb-2\" />\r\n                                <p className=\"text-gray-600 mb-3\">{t('interactiveMapView')}</p>\r\n                                <Button onClick={() => setShowMap(true)}>\r\n                                    {t('loadMap')}\r\n                                </Button>\r\n                            </div>\r\n                        </div>\r\n                    ) : (\r\n                        <div className=\"h-64 rounded-lg overflow-hidden border\">\r\n                            {typeof window !== 'undefined' && (\r\n                                <MapContainer\r\n                                    center={[lat, lng]}\r\n                                    zoom={15}\r\n                                    style={{ height: '100%', width: '100%' }}\r\n                                >\r\n                                    <TileLayer\r\n                                        url=\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\r\n                                        attribution='&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'\r\n                                    />\r\n                                    <Marker position={[lat, lng]}>\r\n                                        <Popup>\r\n                                            <div className=\"text-center\">\r\n                                                <h3 className=\"font-medium\">{property.title}</h3>\r\n                                                <p className=\"text-sm text-gray-600\">{property.address}</p>\r\n                                            </div>\r\n                                        </Popup>\r\n                                    </Marker>\r\n                                </MapContainer>\r\n                            )}\r\n                        </div>\r\n                    )}\r\n                </div> */}\r\n\r\n                {/* Location Info */}\r\n                <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border border-blue-100\">\r\n                    <h4 className=\"font-medium text-gray-900 mb-3\">{t('propertyLocationTitle')}</h4>\r\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\">\r\n                        <div className=\"space-y-2\">\r\n                            <div className=\"flex items-center gap-2\">\r\n                                <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\r\n                                <span className=\"text-gray-600\">{t('cityLabel')}: {property.city}</span>\r\n                            </div>\r\n                            <div className=\"flex items-center gap-2\">\r\n                                <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\r\n                                <span className=\"text-gray-600\">{t('countryLabel')}: {property.country}</span>\r\n                            </div>\r\n                        </div>\r\n                        <div className=\"space-y-2\">\r\n                            <div className=\"flex items-center gap-2\">\r\n                                <div className=\"w-2 h-2 bg-purple-500 rounded-full\"></div>\r\n                                <span className=\"text-gray-600\">{t('propertyTypeLabel')}: {property.type}</span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </CardContent>\r\n        </Card>\r\n    )\r\n} "], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAEA;;;AAPA;;;;;AAce,SAAS,YAAY,EAAE,QAAQ,EAAoB;;IAC9D,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,MAAM,MAAM,WAAW,SAAS,QAAQ,KAAK;IAC7C,MAAM,MAAM,WAAW,SAAS,SAAS,KAAK;IAE9C,qBACI,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;kBACZ,cAAA,6LAAC,mIAAA,CAAA,cAAW;YAAC,WAAU;;8BACnB,6LAAC;oBAAI,WAAU;;sCACX,6LAAC,6MAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,6LAAC;4BAAG,WAAU;sCAAuC,EAAE;;;;;;;;;;;;8BAI3D,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBAAI,WAAU;;0CACX,6LAAC,6MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;;kDACG,6LAAC;wCAAG,WAAU;kDAA6B,SAAS,KAAK;;;;;;kDACzD,6LAAC;wCAAE,WAAU;kDAAsB,SAAS,OAAO;;;;;;kDACnD,6LAAC;wCAAE,WAAU;;4CACR,SAAS,IAAI;4CAAC;4CAAG,SAAS,OAAO;;;;;;;kDAEtC,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAK,WAAU;;oDACX,EAAE;oDAAe;oDAAG,IAAI,OAAO,CAAC;oDAAG;oDAAG,IAAI,OAAO,CAAC;;;;;;;0DAEvD,6LAAC,qIAAA,CAAA,SAAM;gDACH,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,2BAA2B,EAAE,IAAI,CAAC,EAAE,KAAK,EAAE;;kEAEvE,6LAAC,iNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDACrB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BA8CvB,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAG,WAAU;sCAAkC,EAAE;;;;;;sCAClD,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAK,WAAU;;wDAAiB,EAAE;wDAAa;wDAAG,SAAS,IAAI;;;;;;;;;;;;;sDAEpE,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAK,WAAU;;wDAAiB,EAAE;wDAAgB;wDAAG,SAAS,OAAO;;;;;;;;;;;;;;;;;;;8CAG9E,6LAAC;oCAAI,WAAU;8CACX,cAAA,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAK,WAAU;;oDAAiB,EAAE;oDAAqB;oDAAG,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxG;GAxGwB;;QACV,yMAAA,CAAA,kBAAe;;;KADL", "debugId": null}}, {"offset": {"line": 3848, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/actions/offers.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { get, post } from \"@/services/api\";\r\n\r\ninterface OfferForm {\r\n  message: string;\r\n  price: string;\r\n  phone: string;\r\n  duration: string;\r\n  deposit: boolean;\r\n  propertyId?: string;\r\n  personId?: string;\r\n}\r\n\r\nexport const createOffer = async (offer: OfferForm) => {\r\n  const response = await post(`/offers`, offer);\r\n  return response.data;\r\n};\r\n\r\nexport const getOffers = async () => {\r\n  const response = await get(`/offers`);\r\n  return response.data;\r\n};\r\n"], "names": [], "mappings": ";;;;;;IAca,cAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 3864, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/property-details/sections/property-message.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { Card, CardContent } from '@/components/ui/card'\r\nimport { Button } from '@/components/ui/button'\r\nimport { Input } from '@/components/ui/input'\r\nimport { Label } from '@/components/ui/label'\r\nimport { Send, LogIn, AlertCircle } from 'lucide-react'\r\nimport { createOffer } from '@/actions/offers'\r\nimport { useUserStore } from '@/store/useUserStore'\r\nimport { useHeaderStore } from '@/store/useHeaderStore'\r\nimport { useTranslations } from 'next-intl'\r\n\r\ninterface PropertyMessageProps {\r\n    property: any\r\n}\r\n\r\nexport default function PropertyMessage({ property }: PropertyMessageProps) {\r\n    const { user, isAuthenticated } = useUserStore()\r\n    const { openLoginModal } = useHeaderStore()\r\n    const t = useTranslations('propertyDetails.offers')\r\n\r\n    const [formData, setFormData] = useState({\r\n        message: '',\r\n        price: '',\r\n        phone: '',\r\n        duration: '',\r\n        deposit: false\r\n    })\r\n\r\n    const [isSubmitting, setIsSubmitting] = useState(false)\r\n    const [error, setError] = useState('')\r\n    const [success, setSuccess] = useState('')\r\n\r\n    // Check if current user is the property owner\r\n    const isPropertyOwner = isAuthenticated && user?.id === property?.owner?.id\r\n\r\n    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\r\n        const { name, value, type } = e.target\r\n        setFormData(prev => ({\r\n            ...prev,\r\n            [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value\r\n        }))\r\n    }\r\n\r\n    const handleSubmit = async (e: React.FormEvent) => {\r\n        e.preventDefault()\r\n        setError('')\r\n        setSuccess('')\r\n\r\n        // Check if user is authenticated\r\n        if (!isAuthenticated) {\r\n            openLoginModal()\r\n            return\r\n        }\r\n\r\n        // Check if user is the property owner\r\n        if (isPropertyOwner) {\r\n            setError(t('validationErrors.cannotOfferOwnProperty'))\r\n            return\r\n        }\r\n\r\n        // Validate required fields\r\n        if (!formData.message || !formData.price || !formData.phone || !formData.duration) {\r\n            setError(t('validationErrors.allFieldsRequired'))\r\n            return\r\n        }\r\n\r\n        setIsSubmitting(true)\r\n\r\n        try {\r\n            const offerData = {\r\n                message: formData.message,\r\n                price: formData.price,\r\n                phone: formData.phone,\r\n                duration: formData.duration,\r\n                deposit: formData.deposit,\r\n                propertyId: property?.id\r\n            }\r\n\r\n            const response = await createOffer(offerData)\r\n\r\n            if (response) {\r\n                setSuccess(t('offerSubmitted'))\r\n                // Reset form\r\n                setFormData({\r\n                    message: '',\r\n                    price: '',\r\n                    phone: '',\r\n                    duration: '',\r\n                    deposit: false\r\n                })\r\n            }\r\n        } catch (error) {\r\n            console.error('Failed to create offer:', error)\r\n            setError(t('offerError'))\r\n        } finally {\r\n            setIsSubmitting(false)\r\n        }\r\n    }\r\n\r\n    // If user is not authenticated, show login prompt\r\n    if (!isAuthenticated) {\r\n        return (\r\n            <Card className=\"border-0 shadow-sm\">\r\n                <CardContent className=\"p-6\">\r\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">{t('makeOffer')}</h3>\r\n                    <div className=\"text-center py-6\">\r\n                        <LogIn className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\r\n                        <p className=\"text-gray-600 mb-4\">{t('signInPrompt')}</p>\r\n                        <Button onClick={openLoginModal} className=\"w-full\">\r\n                            <LogIn className=\"h-4 w-4 mr-2\" />\r\n                            {t('signInToMakeOffer')}\r\n                        </Button>\r\n                    </div>\r\n                </CardContent>\r\n            </Card>\r\n        )\r\n    }\r\n\r\n    // If user is the property owner, show message\r\n    if (isPropertyOwner) {\r\n        return (\r\n            <Card className=\"border-0 shadow-sm\">\r\n                <CardContent className=\"p-6\">\r\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">{t('makeOffer')}</h3>\r\n                    <div className=\"text-center py-6\">\r\n                        <AlertCircle className=\"h-12 w-12 text-orange-400 mx-auto mb-4\" />\r\n                        <p className=\"text-gray-600\">{t('ownPropertyMessage')}</p>\r\n                    </div>\r\n                </CardContent>\r\n            </Card>\r\n        )\r\n    }\r\n\r\n    return (\r\n        <Card className=\"border-0 shadow-sm\">\r\n            <CardContent className=\"p-6\">\r\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">{t('makeOffer')}</h3>\r\n\r\n                {error && (\r\n                    <div className=\"mb-4 p-4 bg-red-50 border border-red-200 rounded-md\">\r\n                        <div className=\"flex items-center gap-2\">\r\n                            <AlertCircle className=\"h-4 w-4 text-red-600\" />\r\n                            <span className=\"text-sm text-red-800\">{error}</span>\r\n                        </div>\r\n                    </div>\r\n                )}\r\n\r\n                {success && (\r\n                    <div className=\"mb-4 p-4 bg-green-50 border border-green-200 rounded-md\">\r\n                        <div className=\"flex items-center gap-2\">\r\n                            <Send className=\"h-4 w-4 text-green-600\" />\r\n                            <span className=\"text-sm text-green-800\">{success}</span>\r\n                        </div>\r\n                    </div>\r\n                )}\r\n\r\n                <form onSubmit={handleSubmit} className=\"space-y-4\">\r\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                        <div>\r\n                            <Label htmlFor=\"price\">{t('offerPrice')} {t('required')}</Label>\r\n                            <Input\r\n                                id=\"price\"\r\n                                name=\"price\"\r\n                                type=\"number\"\r\n                                required\r\n                                value={formData.price}\r\n                                onChange={handleInputChange}\r\n                                placeholder={t('offerPricePlaceholder')}\r\n                            />\r\n                        </div>\r\n                        <div>\r\n                            <Label htmlFor=\"phone\">{t('phoneNumber')} {t('required')}</Label>\r\n                            <Input\r\n                                id=\"phone\"\r\n                                name=\"phone\"\r\n                                type=\"tel\"\r\n                                required\r\n                                value={formData.phone}\r\n                                onChange={handleInputChange}\r\n                                placeholder={t('phoneNumberPlaceholder')}\r\n                            />\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div>\r\n                        <Label htmlFor=\"duration\">{t('duration')} {t('required')}</Label>\r\n                        <select\r\n                            id=\"duration\"\r\n                            name=\"duration\"\r\n                            required\r\n                            value={formData.duration}\r\n                            onChange={handleInputChange}\r\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                        >\r\n                            <option value=\"\">{t('durationPlaceholder')}</option>\r\n                            <option value=\"1-month\">{t('durationOptions.1-month')}</option>\r\n                            <option value=\"3-months\">{t('durationOptions.3-months')}</option>\r\n                            <option value=\"6-months\">{t('durationOptions.6-months')}</option>\r\n                            <option value=\"1-year\">{t('durationOptions.1-year')}</option>\r\n                            <option value=\"2-years\">{t('durationOptions.2-years')}</option>\r\n                            <option value=\"flexible\">{t('durationOptions.flexible')}</option>\r\n                        </select>\r\n                    </div>\r\n\r\n                    <div className=\"flex items-center gap-2\">\r\n                        <input\r\n                            type=\"checkbox\"\r\n                            id=\"deposit\"\r\n                            name=\"deposit\"\r\n                            checked={formData.deposit}\r\n                            onChange={handleInputChange}\r\n                            className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\r\n                        />\r\n                        <Label htmlFor=\"deposit\" className=\"text-sm\">\r\n                            {t('deposit')}\r\n                        </Label>\r\n                    </div>\r\n\r\n                    <div>\r\n                        <Label htmlFor=\"message\">{t('offerMessage')} {t('required')}</Label>\r\n                        <textarea\r\n                            id=\"message\"\r\n                            name=\"message\"\r\n                            required\r\n                            rows={4}\r\n                            value={formData.message}\r\n                            onChange={handleInputChange}\r\n                            placeholder={t('messagePlaceholder')}\r\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none\"\r\n                        />\r\n                    </div>\r\n\r\n                    <Button type=\"submit\" className=\"w-full\" disabled={isSubmitting}>\r\n                        {isSubmitting ? (\r\n                            <>\r\n                                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\r\n                                {t('sendingOffer')}\r\n                            </>\r\n                        ) : (\r\n                            <>\r\n                                <Send className=\"h-4 w-4 mr-2\" />\r\n                                {t('sendOffer')}\r\n                            </>\r\n                        )}\r\n                    </Button>\r\n                </form>\r\n            </CardContent>\r\n        </Card>\r\n    )\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;AAiBe,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;;IACtE,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAC7C,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IACxC,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,SAAS;QACT,OAAO;QACP,OAAO;QACP,UAAU;QACV,SAAS;IACb;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,8CAA8C;IAC9C,MAAM,kBAAkB,mBAAmB,MAAM,OAAO,UAAU,OAAO;IAEzE,MAAM,oBAAoB,CAAC;QACvB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM;QACtC,YAAY,CAAA,OAAQ,CAAC;gBACjB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,aAAa,AAAC,EAAE,MAAM,CAAsB,OAAO,GAAG;YAC3E,CAAC;IACL;IAEA,MAAM,eAAe,OAAO;QACxB,EAAE,cAAc;QAChB,SAAS;QACT,WAAW;QAEX,iCAAiC;QACjC,IAAI,CAAC,iBAAiB;YAClB;YACA;QACJ;QAEA,sCAAsC;QACtC,IAAI,iBAAiB;YACjB,SAAS,EAAE;YACX;QACJ;QAEA,2BAA2B;QAC3B,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,QAAQ,EAAE;YAC/E,SAAS,EAAE;YACX;QACJ;QAEA,gBAAgB;QAEhB,IAAI;YACA,MAAM,YAAY;gBACd,SAAS,SAAS,OAAO;gBACzB,OAAO,SAAS,KAAK;gBACrB,OAAO,SAAS,KAAK;gBACrB,UAAU,SAAS,QAAQ;gBAC3B,SAAS,SAAS,OAAO;gBACzB,YAAY,UAAU;YAC1B;YAEA,MAAM,WAAW,MAAM,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE;YAEnC,IAAI,UAAU;gBACV,WAAW,EAAE;gBACb,aAAa;gBACb,YAAY;oBACR,SAAS;oBACT,OAAO;oBACP,OAAO;oBACP,UAAU;oBACV,SAAS;gBACb;YACJ;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,2BAA2B;YACzC,SAAS,EAAE;QACf,SAAU;YACN,gBAAgB;QACpB;IACJ;IAEA,kDAAkD;IAClD,IAAI,CAAC,iBAAiB;QAClB,qBACI,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;sBACZ,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACnB,6LAAC;wBAAG,WAAU;kCAA4C,EAAE;;;;;;kCAC5D,6LAAC;wBAAI,WAAU;;0CACX,6LAAC,2MAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAE,WAAU;0CAAsB,EAAE;;;;;;0CACrC,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS;gCAAgB,WAAU;;kDACvC,6LAAC,2MAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAChB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;IAM3B;IAEA,8CAA8C;IAC9C,IAAI,iBAAiB;QACjB,qBACI,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;sBACZ,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACnB,6LAAC;wBAAG,WAAU;kCAA4C,EAAE;;;;;;kCAC5D,6LAAC;wBAAI,WAAU;;0CACX,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC;gCAAE,WAAU;0CAAiB,EAAE;;;;;;;;;;;;;;;;;;;;;;;IAKpD;IAEA,qBACI,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;kBACZ,cAAA,6LAAC,mIAAA,CAAA,cAAW;YAAC,WAAU;;8BACnB,6LAAC;oBAAG,WAAU;8BAA4C,EAAE;;;;;;gBAE3D,uBACG,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBAAI,WAAU;;0CACX,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;;;;;;;gBAKnD,yBACG,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBAAI,WAAU;;0CACX,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;gCAAK,WAAU;0CAA0B;;;;;;;;;;;;;;;;;8BAKtD,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACpC,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;;sDACG,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;;gDAAS,EAAE;gDAAc;gDAAE,EAAE;;;;;;;sDAC5C,6LAAC,oIAAA,CAAA,QAAK;4CACF,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,aAAa,EAAE;;;;;;;;;;;;8CAGvB,6LAAC;;sDACG,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;;gDAAS,EAAE;gDAAe;gDAAE,EAAE;;;;;;;sDAC7C,6LAAC,oIAAA,CAAA,QAAK;4CACF,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,aAAa,EAAE;;;;;;;;;;;;;;;;;;sCAK3B,6LAAC;;8CACG,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;;wCAAY,EAAE;wCAAY;wCAAE,EAAE;;;;;;;8CAC7C,6LAAC;oCACG,IAAG;oCACH,MAAK;oCACL,QAAQ;oCACR,OAAO,SAAS,QAAQ;oCACxB,UAAU;oCACV,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAI,EAAE;;;;;;sDACpB,6LAAC;4CAAO,OAAM;sDAAW,EAAE;;;;;;sDAC3B,6LAAC;4CAAO,OAAM;sDAAY,EAAE;;;;;;sDAC5B,6LAAC;4CAAO,OAAM;sDAAY,EAAE;;;;;;sDAC5B,6LAAC;4CAAO,OAAM;sDAAU,EAAE;;;;;;sDAC1B,6LAAC;4CAAO,OAAM;sDAAW,EAAE;;;;;;sDAC3B,6LAAC;4CAAO,OAAM;sDAAY,EAAE;;;;;;;;;;;;;;;;;;sCAIpC,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCACG,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,SAAS,SAAS,OAAO;oCACzB,UAAU;oCACV,WAAU;;;;;;8CAEd,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;8CAC9B,EAAE;;;;;;;;;;;;sCAIX,6LAAC;;8CACG,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;;wCAAW,EAAE;wCAAgB;wCAAE,EAAE;;;;;;;8CAChD,6LAAC;oCACG,IAAG;oCACH,MAAK;oCACL,QAAQ;oCACR,MAAM;oCACN,OAAO,SAAS,OAAO;oCACvB,UAAU;oCACV,aAAa,EAAE;oCACf,WAAU;;;;;;;;;;;;sCAIlB,6LAAC,qIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAS,WAAU;4BAAS,UAAU;sCAC9C,6BACG;;kDACI,6LAAC;wCAAI,WAAU;;;;;;oCACd,EAAE;;6DAGP;;kDACI,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCACf,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;AAQnC;GA1OwB;;QACc,+HAAA,CAAA,eAAY;QACnB,iIAAA,CAAA,iBAAc;QAC/B,yMAAA,CAAA,kBAAe;;;KAHL", "debugId": null}}, {"offset": {"line": 4462, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/actions/comments.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { post, patch, del } from \"@/services/api\";\r\n\r\ninterface CommentForm {\r\n  id?: string;\r\n  content: string;\r\n  propertyId?: string;\r\n  personId?: string;\r\n}\r\n\r\nexport const createComment = async (comment: CommentForm) => {\r\n  const response = await post(`/comments`, comment);\r\n  return response.data;\r\n};\r\n\r\nexport const updateComment = async (comment: CommentForm) => {\r\n  const response = await patch(`/comments/${comment.id}`, { comment });\r\n  return response.data;\r\n};\r\n\r\nexport const deleteComment = async (id: string) => {\r\n  const response = await del(`/comments/${id}`);\r\n  return response.data;\r\n};\r\n"], "names": [], "mappings": ";;;;;;IAWa,gBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 4478, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/property-details/sections/property-comments-offers.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useState } from 'react'\r\nimport { Card, CardContent } from \"@/components/ui/card\"\r\nimport { Button } from \"@/components/ui/button\"\r\nimport { Label } from \"@/components/ui/label\"\r\nimport { CheckCircle, Send, LogIn } from \"lucide-react\"\r\nimport { useTranslations } from 'next-intl'\r\nimport { useUserStore } from \"@/store/useUserStore\"\r\nimport { useHeaderStore } from \"@/store/useHeaderStore\"\r\nimport { useCurrencyState } from \"@/hooks/useCurrencyState\"\r\nimport { createComment } from \"@/actions/comments\"\r\n\r\ninterface PropertyCommentsOffersProps {\r\n    property: any\r\n}\r\n\r\nexport default function PropertyCommentsOffers({ property }: PropertyCommentsOffersProps) {\r\n    const { user, isAuthenticated } = useUserStore()\r\n    const { openLoginModal } = useHeaderStore()\r\n    const t = useTranslations('propertyDetails')\r\n    const { formatPrice, formatPropertyPrice } = useCurrencyState()\r\n\r\n    const [newComment, setNewComment] = useState('')\r\n    const [isSubmitting, setIsSubmitting] = useState(false)\r\n    const [comments, setComments] = useState(property.comments || [])\r\n\r\n    // Format date\r\n    const formatDate = (dateString: string) => {\r\n        return new Date(dateString).toLocaleDateString('en-US', {\r\n            year: 'numeric',\r\n            month: 'short',\r\n            day: 'numeric'\r\n        })\r\n    }\r\n\r\n    // Handle comment submission\r\n    const handleSubmitComment = async (e: React.FormEvent) => {\r\n        e.preventDefault()\r\n\r\n        if (!newComment.trim() || !isAuthenticated) return\r\n\r\n        setIsSubmitting(true)\r\n\r\n        try {\r\n            const response = await createComment({\r\n                content: newComment.trim(),\r\n                propertyId: property?.id,\r\n            })\r\n\r\n            if (response) {\r\n                // Add the new comment to the local state\r\n                const newCommentData = {\r\n                    id: response.id,\r\n                    content: newComment.trim(),\r\n                    createdAt: new Date().toISOString(),\r\n                    user: {\r\n                        id: user?.id,\r\n                        name: user?.name,\r\n                        isVerified: user?.isVerified\r\n                    }\r\n                }\r\n\r\n                setComments([newCommentData, ...comments])\r\n                setNewComment('')\r\n            }\r\n        } catch (error) {\r\n            console.error('Failed to submit comment:', error)\r\n            // You might want to show an error message to the user\r\n        } finally {\r\n            setIsSubmitting(false)\r\n        }\r\n    }\r\n\r\n    return (\r\n        <div className=\"space-y-8\">\r\n            {/* Comments & Reviews */}\r\n            <Card className=\"border-0 shadow-sm\">\r\n                <CardContent className=\"p-6\">\r\n                    <div className=\"flex items-center justify-between mb-6\">\r\n                        <h2 className=\"text-xl font-semibold text-gray-900\">{t('comments.title')}</h2>\r\n                        <span className=\"text-sm text-gray-500\">\r\n                            {comments.length === 1\r\n                                ? t('comments.commentCount', { count: comments.length })\r\n                                : t('comments.commentCountPlural', { count: comments.length })\r\n                            }\r\n                        </span>\r\n                    </div>\r\n\r\n                    {/* Comment Input Form */}\r\n                    <div className=\"mb-8\">\r\n                        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">{t('comments.leaveComment')}</h3>\r\n\r\n                        {isAuthenticated ? (\r\n                            <form onSubmit={handleSubmitComment} className=\"space-y-4\">\r\n                                <div>\r\n                                    <textarea\r\n                                        id=\"comment\"\r\n                                        rows={4}\r\n                                        value={newComment}\r\n                                        onChange={(e) => setNewComment(e.target.value)}\r\n                                        placeholder={t('comments.commentPlaceholder')}\r\n                                        className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none\"\r\n                                        required\r\n                                    />\r\n                                </div>\r\n\r\n                                <Button type=\"submit\" disabled={isSubmitting || !newComment.trim()}>\r\n                                    {isSubmitting ? (\r\n                                        <>\r\n                                            <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\r\n                                            {t('comments.posting')}\r\n                                        </>\r\n                                    ) : (\r\n                                        <>\r\n                                            <Send className=\"h-4 w-4 mr-2\" />\r\n                                            {t('comments.postComment')}\r\n                                        </>\r\n                                    )}\r\n                                </Button>\r\n                            </form>\r\n                        ) : (\r\n                            <div className=\"text-center py-8 border-2 border-dashed border-gray-300 rounded-lg\">\r\n                                <LogIn className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\r\n                                <h4 className=\"text-lg font-medium text-gray-900 mb-2\">{t('comments.loginRequired')}</h4>\r\n                                <p className=\"text-gray-600 mb-4\">\r\n                                    {t('comments.loginRequiredMessage')}\r\n                                </p>\r\n                                <Button onClick={openLoginModal} className=\"inline-flex items-center\">\r\n                                    <LogIn className=\"h-4 w-4 mr-2\" />\r\n                                    {t('comments.loginToComment')}\r\n                                </Button>\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n\r\n                    {/* Comments List */}\r\n                    {comments && comments.length > 0 && (\r\n                        <div className=\"space-y-6 border-t pt-6\">\r\n                            {comments.map((comment: any) => (\r\n                                <div key={comment.id} className=\"border-b last:border-b-0 pb-6 last:pb-0\">\r\n                                    <div className=\"flex items-start gap-4\">\r\n                                        <div className=\"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold\">\r\n                                            {comment.user?.name?.charAt(0) || 'U'}\r\n                                        </div>\r\n\r\n                                        <div className=\"flex-1\">\r\n                                            <div className=\"flex items-center gap-2 mb-2\">\r\n                                                <h4 className=\"font-medium text-gray-900\">\r\n                                                    {comment.user?.name || 'Anonymous User'}\r\n                                                </h4>\r\n                                                {comment.user?.isVerified && (\r\n                                                    <CheckCircle className=\"h-4 w-4 text-green-600\" />\r\n                                                )}\r\n                                                <span className=\"text-sm text-gray-500\">\r\n                                                    {formatDate(comment.createdAt)}\r\n                                                </span>\r\n                                            </div>\r\n\r\n                                            <p className=\"text-gray-700 leading-relaxed\">\r\n                                                {comment.content}\r\n                                            </p>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            ))}\r\n                        </div>\r\n                    )}\r\n                </CardContent>\r\n            </Card>\r\n\r\n            {/* Offers */}\r\n            {property.offers && property.offers.length > 0 && (\r\n                <Card className=\"border-0 shadow-sm\">\r\n                    <CardContent className=\"p-6\">\r\n                        <div className=\"flex items-center justify-between mb-6\">\r\n                            <h2 className=\"text-xl font-semibold text-gray-900\">{t('offers.recentOffers')}</h2>\r\n                            <span className=\"text-sm text-gray-500\">\r\n                                {property.offers.length === 1\r\n                                    ? t('offers.offerCount', { count: property.offers.length })\r\n                                    : t('offers.offerCountPlural', { count: property.offers.length })\r\n                                }\r\n                            </span>\r\n                        </div>\r\n\r\n                        <div className=\"space-y-4\">\r\n                            {property.offers.map((offer: any) => (\r\n                                <div key={offer.id} className=\"border border-gray-200 rounded-lg p-4\">\r\n                                    <div className=\"flex items-start gap-4\">\r\n                                        <div className=\"w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-full flex items-center justify-center text-white font-bold\">\r\n                                            {offer.user?.name?.charAt(0) || 'U'}\r\n                                        </div>\r\n\r\n                                        <div className=\"flex-1\">\r\n                                            <div className=\"flex items-center justify-between mb-3\">\r\n                                                <div className=\"flex items-center gap-2\">\r\n                                                    <h4 className=\"font-medium text-gray-900\">\r\n                                                        {offer.user?.name || 'Anonymous User'}\r\n                                                    </h4>\r\n                                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${offer.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :\r\n                                                        offer.status === 'accepted' ? 'bg-green-100 text-green-800' :\r\n                                                            'bg-red-100 text-red-800'\r\n                                                        }`}>\r\n                                                        {offer.status}\r\n                                                    </span>\r\n                                                </div>\r\n                                                <span className=\"text-sm text-gray-500\">\r\n                                                    {formatDate(offer.createdAt)}\r\n                                                </span>\r\n                                            </div>\r\n\r\n                                            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-3\">\r\n                                                <div>\r\n                                                    <span className=\"text-gray-600\">{t('offers.offerPrice')}:</span>\r\n                                                    <div className=\"font-medium\">\r\n                                                        {property.country\r\n                                                            ? formatPropertyPrice(offer.price || 0, property.country, { showSymbol: true })\r\n                                                            : formatPrice(offer.price || 0, { showSymbol: true })\r\n                                                        }\r\n                                                    </div>\r\n                                                </div>\r\n                                                <div>\r\n                                                    <span className=\"text-gray-600\">{t('offers.duration')}:</span>\r\n                                                    <div className=\"font-medium\">{offer.duration || t('offers.notSpecified')}</div>\r\n                                                </div>\r\n                                                <div>\r\n                                                    <span className=\"text-gray-600\">{t('offers.deposit')}:</span>\r\n                                                    <div className=\"font-medium\">\r\n                                                        {offer.deposit ? t('offers.depositRequired') : t('offers.depositNotRequired')}\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n\r\n                                            <div className=\"mb-3\">\r\n                                                <span className=\"text-gray-600\">{t('offers.contactLabel')}:</span>\r\n                                                <div className=\"font-medium\">{offer.phone || t('offers.notProvided')}</div>\r\n                                            </div>\r\n\r\n                                            <p className=\"text-gray-700 text-sm leading-relaxed\">\r\n                                                {offer.message}\r\n                                            </p>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            ))}\r\n                        </div>\r\n                    </CardContent>\r\n                </Card>\r\n            )}\r\n        </div>\r\n    )\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;AAiBe,SAAS,uBAAuB,EAAE,QAAQ,EAA+B;;IACpF,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAC7C,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IACxC,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,EAAE,WAAW,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD;IAE5D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,QAAQ,IAAI,EAAE;IAEhE,cAAc;IACd,MAAM,aAAa,CAAC;QAChB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACpD,MAAM;YACN,OAAO;YACP,KAAK;QACT;IACJ;IAEA,4BAA4B;IAC5B,MAAM,sBAAsB,OAAO;QAC/B,EAAE,cAAc;QAEhB,IAAI,CAAC,WAAW,IAAI,MAAM,CAAC,iBAAiB;QAE5C,gBAAgB;QAEhB,IAAI;YACA,MAAM,WAAW,MAAM,CAAA,GAAA,yJAAA,CAAA,gBAAa,AAAD,EAAE;gBACjC,SAAS,WAAW,IAAI;gBACxB,YAAY,UAAU;YAC1B;YAEA,IAAI,UAAU;gBACV,yCAAyC;gBACzC,MAAM,iBAAiB;oBACnB,IAAI,SAAS,EAAE;oBACf,SAAS,WAAW,IAAI;oBACxB,WAAW,IAAI,OAAO,WAAW;oBACjC,MAAM;wBACF,IAAI,MAAM;wBACV,MAAM,MAAM;wBACZ,YAAY,MAAM;oBACtB;gBACJ;gBAEA,YAAY;oBAAC;uBAAmB;iBAAS;gBACzC,cAAc;YAClB;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,sDAAsD;QAC1D,SAAU;YACN,gBAAgB;QACpB;IACJ;IAEA,qBACI,6LAAC;QAAI,WAAU;;0BAEX,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACZ,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACnB,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAG,WAAU;8CAAuC,EAAE;;;;;;8CACvD,6LAAC;oCAAK,WAAU;8CACX,SAAS,MAAM,KAAK,IACf,EAAE,yBAAyB;wCAAE,OAAO,SAAS,MAAM;oCAAC,KACpD,EAAE,+BAA+B;wCAAE,OAAO,SAAS,MAAM;oCAAC;;;;;;;;;;;;sCAMxE,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAG,WAAU;8CAA4C,EAAE;;;;;;gCAE3D,gCACG,6LAAC;oCAAK,UAAU;oCAAqB,WAAU;;sDAC3C,6LAAC;sDACG,cAAA,6LAAC;gDACG,IAAG;gDACH,MAAM;gDACN,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,aAAa,EAAE;gDACf,WAAU;gDACV,QAAQ;;;;;;;;;;;sDAIhB,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAS,UAAU,gBAAgB,CAAC,WAAW,IAAI;sDAC3D,6BACG;;kEACI,6LAAC;wDAAI,WAAU;;;;;;oDACd,EAAE;;6EAGP;;kEACI,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDACf,EAAE;;;;;;;;;;;;;yDAMnB,6LAAC;oCAAI,WAAU;;sDACX,6LAAC,2MAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;4CAAG,WAAU;sDAA0C,EAAE;;;;;;sDAC1D,6LAAC;4CAAE,WAAU;sDACR,EAAE;;;;;;sDAEP,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAS;4CAAgB,WAAU;;8DACvC,6LAAC,2MAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAChB,EAAE;;;;;;;;;;;;;;;;;;;wBAOlB,YAAY,SAAS,MAAM,GAAG,mBAC3B,6LAAC;4BAAI,WAAU;sCACV,SAAS,GAAG,CAAC,CAAC,wBACX,6LAAC;oCAAqB,WAAU;8CAC5B,cAAA,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAI,WAAU;0DACV,QAAQ,IAAI,EAAE,MAAM,OAAO,MAAM;;;;;;0DAGtC,6LAAC;gDAAI,WAAU;;kEACX,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;gEAAG,WAAU;0EACT,QAAQ,IAAI,EAAE,QAAQ;;;;;;4DAE1B,QAAQ,IAAI,EAAE,4BACX,6LAAC,8NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EAE3B,6LAAC;gEAAK,WAAU;0EACX,WAAW,QAAQ,SAAS;;;;;;;;;;;;kEAIrC,6LAAC;wDAAE,WAAU;kEACR,QAAQ,OAAO;;;;;;;;;;;;;;;;;;mCApBtB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;YAgCvC,SAAS,MAAM,IAAI,SAAS,MAAM,CAAC,MAAM,GAAG,mBACzC,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACZ,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACnB,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAG,WAAU;8CAAuC,EAAE;;;;;;8CACvD,6LAAC;oCAAK,WAAU;8CACX,SAAS,MAAM,CAAC,MAAM,KAAK,IACtB,EAAE,qBAAqB;wCAAE,OAAO,SAAS,MAAM,CAAC,MAAM;oCAAC,KACvD,EAAE,2BAA2B;wCAAE,OAAO,SAAS,MAAM,CAAC,MAAM;oCAAC;;;;;;;;;;;;sCAK3E,6LAAC;4BAAI,WAAU;sCACV,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,sBAClB,6LAAC;oCAAmB,WAAU;8CAC1B,cAAA,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAI,WAAU;0DACV,MAAM,IAAI,EAAE,MAAM,OAAO,MAAM;;;;;;0DAGpC,6LAAC;gDAAI,WAAU;;kEACX,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;gEAAI,WAAU;;kFACX,6LAAC;wEAAG,WAAU;kFACT,MAAM,IAAI,EAAE,QAAQ;;;;;;kFAEzB,6LAAC;wEAAK,WAAW,CAAC,2CAA2C,EAAE,MAAM,MAAM,KAAK,YAAY,kCACxF,MAAM,MAAM,KAAK,aAAa,gCAC1B,2BACF;kFACD,MAAM,MAAM;;;;;;;;;;;;0EAGrB,6LAAC;gEAAK,WAAU;0EACX,WAAW,MAAM,SAAS;;;;;;;;;;;;kEAInC,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;;kFACG,6LAAC;wEAAK,WAAU;;4EAAiB,EAAE;4EAAqB;;;;;;;kFACxD,6LAAC;wEAAI,WAAU;kFACV,SAAS,OAAO,GACX,oBAAoB,MAAM,KAAK,IAAI,GAAG,SAAS,OAAO,EAAE;4EAAE,YAAY;wEAAK,KAC3E,YAAY,MAAM,KAAK,IAAI,GAAG;4EAAE,YAAY;wEAAK;;;;;;;;;;;;0EAI/D,6LAAC;;kFACG,6LAAC;wEAAK,WAAU;;4EAAiB,EAAE;4EAAmB;;;;;;;kFACtD,6LAAC;wEAAI,WAAU;kFAAe,MAAM,QAAQ,IAAI,EAAE;;;;;;;;;;;;0EAEtD,6LAAC;;kFACG,6LAAC;wEAAK,WAAU;;4EAAiB,EAAE;4EAAkB;;;;;;;kFACrD,6LAAC;wEAAI,WAAU;kFACV,MAAM,OAAO,GAAG,EAAE,4BAA4B,EAAE;;;;;;;;;;;;;;;;;;kEAK7D,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;gEAAK,WAAU;;oEAAiB,EAAE;oEAAuB;;;;;;;0EAC1D,6LAAC;gEAAI,WAAU;0EAAe,MAAM,KAAK,IAAI,EAAE;;;;;;;;;;;;kEAGnD,6LAAC;wDAAE,WAAU;kEACR,MAAM,OAAO;;;;;;;;;;;;;;;;;;mCApDpB,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgElD;GA1OwB;;QACc,+HAAA,CAAA,eAAY;QACnB,iIAAA,CAAA,iBAAc;QAC/B,yMAAA,CAAA,kBAAe;QACoB,mIAAA,CAAA,mBAAgB;;;KAJzC", "debugId": null}}, {"offset": {"line": 5095, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/property-details/property-details-client.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { useQuery } from '@tanstack/react-query'\r\nimport { useTranslations } from 'next-intl'\r\nimport { getProperty } from '@/actions/properties'\r\nimport PropertyGallery from './sections/property-gallery'\r\nimport PropertyOverview from './sections/property-overview'\r\nimport PropertyDescription from './sections/property-description'\r\nimport PropertyFeatures from './sections/property-features'\r\nimport PropertyMap from './sections/property-map'\r\nimport PropertyMessage from './sections/property-message'\r\nimport PropertyCommentsOffers from './sections/property-comments-offers'\r\nimport { Loader2 } from 'lucide-react'\r\n\r\ninterface PropertyDetailsClientProps {\r\n    property: any // Initial SSR data\r\n}\r\n\r\nexport default function PropertyDetailsClient({ property: initialProperty }: PropertyDetailsClientProps) {\r\n    const t = useTranslations('propertyDetails')\r\n    const [isImageModalOpen, setIsImageModalOpen] = useState(false)\r\n    const [selectedImageIndex, setSelectedImageIndex] = useState(0)\r\n\r\n    // Use React Query for fresh data and real-time updates\r\n    const { data: propertyResponse, isLoading, error } = useQuery({\r\n        queryKey: ['property', initialProperty.slug],\r\n        queryFn: () => getProperty(initialProperty.slug),\r\n        initialData: { data: initialProperty, status: 200 },\r\n        staleTime: 5 * 60 * 1000, // 5 minutes\r\n        refetchOnWindowFocus: false,\r\n    })\r\n\r\n    const property = propertyResponse?.data?.data || initialProperty\r\n\r\n    if (isLoading && !property) {\r\n        return (\r\n            <div className=\"min-h-screen flex items-center justify-center\">\r\n                <div className=\"flex items-center gap-2\">\r\n                    <Loader2 className=\"h-6 w-6 animate-spin\" />\r\n                    <span>{t('loading.title')}</span>\r\n                </div>\r\n            </div>\r\n        )\r\n    }\r\n\r\n    if (error || !property) {\r\n        return (\r\n            <div className=\"min-h-screen flex items-center justify-center\">\r\n                <div className=\"text-center\">\r\n                    <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">{t('errors.propertyNotFound')}</h1>\r\n                    <p className=\"text-gray-600\">{t('errors.propertyNotFoundDesc')}</p>\r\n                </div>\r\n            </div>\r\n        )\r\n    }\r\n\r\n    const handleImageClick = (index: number) => {\r\n        setSelectedImageIndex(index)\r\n        setIsImageModalOpen(true)\r\n    }\r\n\r\n    return (\r\n        <div className=\"min-h-screen bg-gray-50\">\r\n            {/* Property Gallery - Full width hero section */}\r\n            <PropertyGallery\r\n                property={property}\r\n                onImageClick={handleImageClick}\r\n                isModalOpen={isImageModalOpen}\r\n                setIsModalOpen={setIsImageModalOpen}\r\n                selectedImageIndex={selectedImageIndex}\r\n                setSelectedImageIndex={setSelectedImageIndex}\r\n            />\r\n\r\n            {/* Main Content */}\r\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n                <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\r\n                    {/* Left Column - Main Content */}\r\n                    <div className=\"lg:col-span-2 space-y-8\">\r\n                        <PropertyOverview property={property} />\r\n                        <PropertyDescription property={property} />\r\n                        <PropertyFeatures property={property} />\r\n                        <PropertyMap property={property} />\r\n                    </div>\r\n\r\n                    {/* Right Column - Message Form */}\r\n                    <div className=\"lg:col-span-1\">\r\n                        <div className=\"sticky top-20 max-h-[calc(100vh-4rem)] overflow-y-auto\">\r\n                            <PropertyMessage property={property} />\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Comments and Offers - Full Width */}\r\n                <div className=\"mt-12\">\r\n                    <PropertyCommentsOffers property={property} />\r\n                </div>\r\n            </div>\r\n        </div>\r\n    )\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAbA;;;;;;;;;;;;;AAmBe,SAAS,sBAAsB,EAAE,UAAU,eAAe,EAA8B;;IACnG,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,uDAAuD;IACvD,MAAM,EAAE,MAAM,gBAAgB,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QAC1D,UAAU;YAAC;YAAY,gBAAgB,IAAI;SAAC;QAC5C,OAAO;8CAAE,IAAM,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,gBAAgB,IAAI;;QAC/C,aAAa;YAAE,MAAM;YAAiB,QAAQ;QAAI;QAClD,WAAW,IAAI,KAAK;QACpB,sBAAsB;IAC1B;IAEA,MAAM,WAAW,kBAAkB,MAAM,QAAQ;IAEjD,IAAI,aAAa,CAAC,UAAU;QACxB,qBACI,6LAAC;YAAI,WAAU;sBACX,cAAA,6LAAC;gBAAI,WAAU;;kCACX,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;kCAAM,EAAE;;;;;;;;;;;;;;;;;IAIzB;IAEA,IAAI,SAAS,CAAC,UAAU;QACpB,qBACI,6LAAC;YAAI,WAAU;sBACX,cAAA,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAG,WAAU;kCAAyC,EAAE;;;;;;kCACzD,6LAAC;wBAAE,WAAU;kCAAiB,EAAE;;;;;;;;;;;;;;;;;IAIhD;IAEA,MAAM,mBAAmB,CAAC;QACtB,sBAAsB;QACtB,oBAAoB;IACxB;IAEA,qBACI,6LAAC;QAAI,WAAU;;0BAEX,6LAAC,+KAAA,CAAA,UAAe;gBACZ,UAAU;gBACV,cAAc;gBACd,aAAa;gBACb,gBAAgB;gBAChB,oBAAoB;gBACpB,uBAAuB;;;;;;0BAI3B,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;;0CAEX,6LAAC;gCAAI,WAAU;;kDACX,6LAAC,gLAAA,CAAA,UAAgB;wCAAC,UAAU;;;;;;kDAC5B,6LAAC,mLAAA,CAAA,UAAmB;wCAAC,UAAU;;;;;;kDAC/B,6LAAC,gLAAA,CAAA,UAAgB;wCAAC,UAAU;;;;;;kDAC5B,6LAAC,2KAAA,CAAA,UAAW;wCAAC,UAAU;;;;;;;;;;;;0CAI3B,6LAAC;gCAAI,WAAU;0CACX,cAAA,6LAAC;oCAAI,WAAU;8CACX,cAAA,6LAAC,+KAAA,CAAA,UAAe;wCAAC,UAAU;;;;;;;;;;;;;;;;;;;;;;kCAMvC,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC,0LAAA,CAAA,UAAsB;4BAAC,UAAU;;;;;;;;;;;;;;;;;;;;;;;AAKtD;GAjFwB;;QACV,yMAAA,CAAA,kBAAe;QAK4B,8KAAA,CAAA,WAAQ;;;KANzC", "debugId": null}}]}
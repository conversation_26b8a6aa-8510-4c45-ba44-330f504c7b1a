{"version": 3, "file": "offers.examples.js", "sourceRoot": "", "sources": ["../../../src/offers/offers.examples.ts"], "names": [], "mappings": ";;;AAUa,QAAA,kBAAkB,GAAmB;IAChD,OAAO,EACL,yKAAyK;IAC3K,KAAK,EAAE,MAAM;IACb,KAAK,EAAE,eAAe;IACtB,QAAQ,EAAE,UAAU;IACpB,OAAO,EAAE,IAAI;IACb,UAAU,EAAE,mBAAmB;CAChC,CAAC;AAGW,QAAA,kBAAkB,GAAmB;IAChD,OAAO,EAAE,iDAAiD;IAC1D,KAAK,EAAE,MAAM;IACb,KAAK,EAAE,eAAe;IACtB,UAAU,EAAE,mBAAmB;CAChC,CAAC;AAGW,QAAA,oBAAoB,GAAmB;IAClD,OAAO,EACL,2OAA2O;IAC7O,KAAK,EAAE,MAAM;IACb,KAAK,EAAE,eAAe;IACtB,QAAQ,EAAE,WAAW;IACrB,OAAO,EAAE,IAAI;IACb,UAAU,EAAE,mBAAmB;CAChC,CAAC;AAGW,QAAA,qBAAqB,GAAmB;IACnD,OAAO,EACL,kGAAkG;IACpG,KAAK,EAAE,MAAM;IACb,KAAK,EAAE,eAAe;IACtB,QAAQ,EAAE,SAAS;IACnB,OAAO,EAAE,KAAK;IACd,UAAU,EAAE,mBAAmB;CAChC,CAAC;AAGW,QAAA,kBAAkB,GAAmB;IAChD,OAAO,EACL,6HAA6H;IAC/H,KAAK,EAAE,MAAM;IACb,QAAQ,EAAE,WAAW;IACrB,MAAM,EAAE,SAAS;CAClB,CAAC;AAGW,QAAA,aAAa,GAAG;IAE3B,YAAY,EAAE;QACZ,IAAI,EAAE,CAAC;QACP,KAAK,EAAE,EAAE;QACT,MAAM,EAAE,WAAoB;QAC5B,SAAS,EAAE,MAAe;KACT;IAGnB,cAAc,EAAE;QACd,MAAM,EAAE,SAAS;QACjB,IAAI,EAAE,CAAC;QACP,KAAK,EAAE,EAAE;KACQ;IAGnB,gBAAgB,EAAE;QAChB,UAAU,EAAE,mBAAmB;QAC/B,IAAI,EAAE,CAAC;QACP,KAAK,EAAE,EAAE;QACT,MAAM,EAAE,OAAgB;QACxB,SAAS,EAAE,MAAe;KACT;IAGnB,YAAY,EAAE;QACZ,MAAM,EAAE,eAAe;QACvB,IAAI,EAAE,CAAC;QACP,KAAK,EAAE,EAAE;QACT,MAAM,EAAE,WAAoB;QAC5B,SAAS,EAAE,MAAe;KACT;IAGnB,aAAa,EAAE;QACb,IAAI,EAAE,CAAC;QACP,KAAK,EAAE,EAAE;QACT,MAAM,EAAE,OAAgB;QACxB,SAAS,EAAE,KAAc;KACR;IAGnB,iBAAiB,EAAE;QACjB,OAAO,EAAE,IAAI;QACb,IAAI,EAAE,CAAC;QACP,KAAK,EAAE,EAAE;KACQ;IAGnB,YAAY,EAAE;QACZ,MAAM,EAAE,gCAAgC;QACxC,IAAI,EAAE,CAAC;QACP,KAAK,EAAE,CAAC;KACS;IAGnB,aAAa,EAAE;QACb,MAAM,EAAE,SAAS;QACjB,UAAU,EAAE,mBAAmB;QAC/B,OAAO,EAAE,IAAI;QACb,MAAM,EAAE,WAAW;QACnB,IAAI,EAAE,CAAC;QACP,KAAK,EAAE,CAAC;QACR,MAAM,EAAE,OAAgB;QACxB,SAAS,EAAE,MAAe;KACT;CACpB,CAAC;AAGW,QAAA,kBAAkB,GAAG;IAEhC,aAAa,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,CAAC;IAG/D,WAAW,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,CAAC;IAG/D,WAAW,EAAE;QACX,eAAe;QACf,kBAAkB;QAClB,aAAa;QACb,iBAAiB;QACjB,kBAAkB;KACnB;IAGD,cAAc,EAAE;QACd,QAAQ;QACR,SAAS;QACT,SAAS;QACT,UAAU;QACV,UAAU;QACV,WAAW;QACX,WAAW;QACX,YAAY;KACb;IAGD,aAAa,EAAE;QACb,yBAAyB;QACzB,oIAAoI;QACpI,sJAAsJ;KACvJ;IAGD,eAAe,EAAE;QAEf,EAAE,OAAO,EAAE,EAAE,EAAE;QACf,EAAE,OAAO,EAAE,IAAI,EAAE;QACjB,EAAE,OAAO,EAAE,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;QAG7B,EAAE,KAAK,EAAE,EAAE,EAAE;QACb,EAAE,KAAK,EAAE,MAAM,EAAE;QACjB,EAAE,KAAK,EAAE,OAAO,EAAE;QAClB,EAAE,KAAK,EAAE,KAAK,EAAE;QAGhB,EAAE,KAAK,EAAE,EAAE,EAAE;QACb,EAAE,KAAK,EAAE,KAAK,EAAE;QAChB,EAAE,KAAK,EAAE,aAAa,EAAE;QACxB,EAAE,KAAK,EAAE,GAAG,EAAE;QAGd,EAAE,UAAU,EAAE,EAAE,EAAE;QAClB,EAAE,UAAU,EAAE,cAAc,EAAE;QAC9B,EAAE,UAAU,EAAE,KAAK,EAAE;QAGrB,EAAE,QAAQ,EAAE,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;KAC9B;CACF,CAAC;AAGW,QAAA,mBAAmB,GAAG;IAEjC,MAAM,EAAE;QACN,MAAM,EAAE,MAAM;QACd,GAAG,EAAE,aAAa;QAClB,OAAO,EAAE,EAAE,aAAa,EAAE,uBAAuB,EAAE;QACnD,IAAI,EAAE,0BAAkB;KACzB;IAGD,MAAM,EAAE;QACN,MAAM,EAAE,KAAK;QACb,GAAG,EAAE,8DAA8D;QACnE,OAAO,EAAE,EAAE,aAAa,EAAE,wBAAwB,EAAE;KACrD;IAGD,aAAa,EAAE;QACb,MAAM,EAAE,KAAK;QACb,GAAG,EAAE,uBAAuB;QAC5B,OAAO,EAAE,EAAE,aAAa,EAAE,uBAAuB,EAAE;KACpD;IAGD,iBAAiB,EAAE;QACjB,MAAM,EAAE,KAAK;QACb,GAAG,EAAE,0CAA0C;QAC/C,OAAO,EAAE,EAAE,aAAa,EAAE,iCAAiC,EAAE;KAC9D;IAGD,cAAc,EAAE;QACd,MAAM,EAAE,KAAK;QACb,GAAG,EAAE,4CAA4C;QACjD,OAAO,EAAE,EAAE,aAAa,EAAE,uBAAuB,EAAE;KACpD;IAGD,MAAM,EAAE;QACN,MAAM,EAAE,KAAK;QACb,GAAG,EAAE,0DAA0D;QAC/D,OAAO,EAAE,EAAE,aAAa,EAAE,iCAAiC,EAAE;KAC9D;IAGD,QAAQ,EAAE;QACR,MAAM,EAAE,KAAK;QACb,GAAG,EAAE,mBAAmB;QACxB,OAAO,EAAE,EAAE,aAAa,EAAE,wBAAwB,EAAE;KACrD;IAGD,OAAO,EAAE;QACP,MAAM,EAAE,KAAK;QACb,GAAG,EAAE,4BAA4B;QACjC,OAAO,EAAE,EAAE,aAAa,EAAE,uBAAuB,EAAE;KACpD;IAGD,MAAM,EAAE;QACN,MAAM,EAAE,OAAO;QACf,GAAG,EAAE,4BAA4B;QACjC,OAAO,EAAE,EAAE,aAAa,EAAE,uBAAuB,EAAE;QACnD,IAAI,EAAE,0BAAkB;KACzB;IAGD,MAAM,EAAE;QACN,MAAM,EAAE,OAAO;QACf,GAAG,EAAE,mCAAmC;QACxC,OAAO,EAAE,EAAE,aAAa,EAAE,iCAAiC,EAAE;KAC9D;IAGD,MAAM,EAAE;QACN,MAAM,EAAE,OAAO;QACf,GAAG,EAAE,mCAAmC;QACxC,OAAO,EAAE,EAAE,aAAa,EAAE,iCAAiC,EAAE;QAC7D,IAAI,EAAE,EAAE,MAAM,EAAE,sCAAsC,EAAE;KACzD;IAGD,MAAM,EAAE;QACN,MAAM,EAAE,OAAO;QACf,GAAG,EAAE,mCAAmC;QACxC,OAAO,EAAE,EAAE,aAAa,EAAE,uBAAuB,EAAE;KACpD;IAGD,MAAM,EAAE;QACN,MAAM,EAAE,QAAQ;QAChB,GAAG,EAAE,4BAA4B;QACjC,OAAO,EAAE,EAAE,aAAa,EAAE,wBAAwB,EAAE;KACrD;CACF,CAAC;AAGW,QAAA,gBAAgB,GAAG;IAE9B,aAAa,EAAE;QACb,EAAE,EAAE,gBAAgB;QACpB,OAAO,EACL,qEAAqE;QACvE,KAAK,EAAE,MAAM;QACb,KAAK,EAAE,eAAe;QACtB,QAAQ,EAAE,UAAU;QACpB,OAAO,EAAE,IAAI;QACb,MAAM,EAAE,SAAS;QACjB,SAAS,EAAE,0BAA0B;QACrC,SAAS,EAAE,0BAA0B;QACrC,QAAQ,EAAE;YACR,EAAE,EAAE,mBAAmB;YACvB,KAAK,EAAE,2CAA2C;YAClD,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,OAAO;YAChB,IAAI,EAAE,WAAW;YACjB,KAAK,EAAE,MAAM;YACb,MAAM,EAAE,CAAC,0CAA0C,CAAC;YACpD,IAAI,EAAE;gBACJ,EAAE,EAAE,qBAAqB;gBACzB,IAAI,EAAE,cAAc;gBACpB,KAAK,EAAE,mBAAmB;gBAC1B,KAAK,EAAE,eAAe;aACvB;SACF;QACD,IAAI,EAAE;YACJ,EAAE,EAAE,oBAAoB;YACxB,IAAI,EAAE,cAAc;YACpB,KAAK,EAAE,kBAAkB;YACzB,KAAK,EAAE,eAAe;YACtB,GAAG,EAAE,IAAI;YACT,MAAM,EAAE,QAAQ;YAChB,UAAU,EAAE,mBAAmB;SAChC;QACD,QAAQ,EAAE;YACR;gBACE,EAAE,EAAE,kBAAkB;gBACtB,SAAS,EAAE,0BAA0B;gBACrC,OAAO,EAAE,0BAA0B;gBACnC,MAAM,EAAE,WAAW;aACpB;SACF;KACF;IAGD,iBAAiB,EAAE;QACjB,IAAI,EAAE,EAEL;QACD,KAAK,EAAE,EAAE;QACT,IAAI,EAAE,CAAC;QACP,KAAK,EAAE,EAAE;QACT,UAAU,EAAE,CAAC;QACb,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,KAAK;KACf;IAGD,aAAa,EAAE;QACb,WAAW,EAAE,GAAG;QAChB,aAAa,EAAE,EAAE;QACjB,cAAc,EAAE,EAAE;QAClB,cAAc,EAAE,EAAE;QAClB,eAAe,EAAE,EAAE;QACnB,aAAa,EAAE,IAAI;QACnB,eAAe,EAAE,GAAG;QACpB,cAAc,EAAE;YACd,OAAO,EAAE,EAAE;YACX,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE,EAAE;SACd;QACD,aAAa,EAAE;YACb,YAAY,EAAE,EAAE;YAChB,WAAW,EAAE,GAAG;YAChB,YAAY,EAAE,EAAE;YAChB,aAAa,EAAE,EAAE;SAClB;QACD,aAAa,EAAE;YACb;gBACE,EAAE,EAAE,iBAAiB;gBACrB,KAAK,EAAE,6BAA6B;gBACpC,UAAU,EAAE,EAAE;gBACd,aAAa,EAAE,IAAI;aACpB;YACD;gBACE,EAAE,EAAE,iBAAiB;gBACrB,KAAK,EAAE,wBAAwB;gBAC/B,UAAU,EAAE,EAAE;gBACd,aAAa,EAAE,IAAI;aACpB;SACF;QACD,kBAAkB,EAAE,EAAE;KACvB;IAGD,qBAAqB,EAAE;QACrB,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,4BAA4B;QACrC,IAAI,EAAE,EAEL;KACF;IAGD,oBAAoB,EAAE;QACpB,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,mCAAmC;QAC5C,IAAI,EAAE;YACJ,EAAE,EAAE,gBAAgB;YACpB,MAAM,EAAE,UAAU;YAClB,SAAS,EAAE,0BAA0B;SACtC;KACF;CACF,CAAC"}
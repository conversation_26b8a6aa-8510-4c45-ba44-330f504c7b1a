import { CreateNotificationDto, BroadcastNotificationDto, SendToUsersNotificationDto } from './dto/create-notification.dto';
import { UpdateNotificationDto } from './dto/update-notification.dto';
import { QueryNotificationsDto } from './dto/query-notifications.dto';
export declare const createNotificationExample: CreateNotificationDto;
export declare const adminNotificationExample: CreateNotificationDto;
export declare const broadcastNotificationExample: BroadcastNotificationDto;
export declare const sendToUsersNotificationExample: SendToUsersNotificationDto;
export declare const updateNotificationExample: UpdateNotificationDto;
export declare const queryExamples: {
    getUserNotifications: QueryNotificationsDto;
    getUnreadNotifications: QueryNotificationsDto;
    searchNotifications: QueryNotificationsDto;
};
export declare const apiEndpointExamples: {
    create: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
        body: CreateNotificationDto;
    };
    broadcast: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
        body: BroadcastNotificationDto;
    };
    sendToUsers: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
        body: SendToUsersNotificationDto;
    };
    getUserNotifications: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    markAsRead: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    markAllAsRead: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    getUnreadCount: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
};
export declare const responseExamples: {
    notificationResponse: {
        id: string;
        title: string;
        message: string;
        read: boolean;
        createdAt: string;
        user: {
            id: string;
            name: string;
            email: string;
        };
        admin: {
            id: string;
            name: string;
            email: string;
        };
    };
    broadcastResponse: {
        message: string;
        count: number;
    };
    sendToUsersResponse: {
        message: string;
        count: number;
        userIds: string[];
    };
    unreadCountResponse: {
        unreadCount: number;
    };
    markAllAsReadResponse: {
        message: string;
        count: number;
    };
    notificationsListResponse: {
        notifications: {
            id: string;
            title: string;
            message: string;
            read: boolean;
            createdAt: string;
            user: {
                id: string;
                name: string;
                email: string;
            };
        }[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    };
};
export declare const usageScenarios: {
    systemAnnouncement: {
        description: string;
        endpoint: string;
        example: {
            title: string;
            message: string;
            adminId: string;
        };
    };
    promotionalCampaign: {
        description: string;
        endpoint: string;
        example: {
            title: string;
            message: string;
            userIds: string[];
            adminId: string;
        };
    };
    personalNotification: {
        description: string;
        endpoint: string;
        example: {
            title: string;
            message: string;
            userId: string;
        };
    };
};

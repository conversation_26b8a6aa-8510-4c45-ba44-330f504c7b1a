export interface AuthResponse {
    access_token: string;
    user: UserResponse;
}
export interface UserResponse {
    id: string;
    name: string;
    email: string;
    phone: string | null;
    smoker: boolean;
    age: string | null;
    gender: string | null;
    nationality: string | null;
    occupation: string | null;
    isAdmin: boolean;
    isVerified: boolean;
    createdAt: Date;
}
export interface JwtPayload {
    sub: string;
    email: string;
    isAdmin: boolean;
    iat?: number;
    exp?: number;
}

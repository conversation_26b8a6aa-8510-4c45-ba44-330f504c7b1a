"use server";

import { get, post } from "@/services/api";
import { GetOffersParams } from "@/lib/types";

interface OfferForm {
  message: string;
  price: string;
  phone: string;
  duration: string;
  deposit: boolean;
  propertyId?: string;
  personId?: string;
}

export const createOffer = async (offer: OfferForm) => {
  const response = await post(`/offers`, offer);
  return response.data;
};

export const getOffers = async (params?: GetOffersParams) => {
  const searchParams = new URLSearchParams();

  if (params?.page) searchParams.append('page', params.page.toString());
  if (params?.limit) searchParams.append('limit', params.limit.toString());
  if (params?.categoryId) searchParams.append('categoryId', params.categoryId);
  if (params?.sortBy) searchParams.append('sortBy', params.sortBy);
  if (params?.sortOrder) searchParams.append('sortOrder', params.sortOrder);

  const queryString = searchParams.toString();
  const url = queryString ? `/offers?${queryString}` : '/offers';

  const response = await get(url);
  return response.data;
};

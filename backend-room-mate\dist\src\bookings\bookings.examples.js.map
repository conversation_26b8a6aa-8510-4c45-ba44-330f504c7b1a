{"version": 3, "file": "bookings.examples.js", "sourceRoot": "", "sources": ["../../../src/bookings/bookings.examples.ts"], "names": [], "mappings": ";;;AAUa,QAAA,oBAAoB,GAAqB;IACpD,OAAO,EAAE,gBAAgB;IACzB,SAAS,EAAE,0BAA0B;IACrC,OAAO,EAAE,0BAA0B;IACnC,WAAW,EAAE,MAAM;IACnB,WAAW,EAAE,IAAI;CAClB,CAAC;AAGW,QAAA,uBAAuB,GAAqB;IACvD,OAAO,EAAE,gBAAgB;IACzB,SAAS,EAAE,0BAA0B;IACrC,WAAW,EAAE,MAAM;IACnB,WAAW,EAAE,KAAK;CACnB,CAAC;AAGW,QAAA,uBAAuB,GAAqB;IACvD,OAAO,EAAE,gBAAgB;IACzB,SAAS,EAAE,0BAA0B;IACrC,OAAO,EAAE,0BAA0B;IACnC,WAAW,EAAE,MAAM;IACnB,WAAW,EAAE,IAAI;CAClB,CAAC;AAGW,QAAA,oBAAoB,GAAqB;IACpD,OAAO,EAAE,0BAA0B;IACnC,WAAW,EAAE,OAAO;IACpB,MAAM,EAAE,WAAW;IACnB,WAAW,EAAE,IAAI;CAClB,CAAC;AAGW,QAAA,aAAa,GAAG;IAE3B,cAAc,EAAE;QACd,IAAI,EAAE,CAAC;QACP,KAAK,EAAE,EAAE;QACT,MAAM,EAAE,WAAoB;QAC5B,SAAS,EAAE,MAAe;KACP;IAGrB,cAAc,EAAE;QACd,MAAM,EAAE,WAAW;QACnB,IAAI,EAAE,CAAC;QACP,KAAK,EAAE,EAAE;KACU;IAGrB,iBAAiB,EAAE;QACjB,aAAa,EAAE,0BAA0B;QACzC,WAAW,EAAE,0BAA0B;QACvC,IAAI,EAAE,CAAC;QACP,KAAK,EAAE,EAAE;QACT,MAAM,EAAE,WAAoB;QAC5B,SAAS,EAAE,KAAc;KACN;IAGrB,gBAAgB,EAAE;QAChB,UAAU,EAAE,mBAAmB;QAC/B,IAAI,EAAE,CAAC;QACP,KAAK,EAAE,EAAE;KACU;IAGrB,YAAY,EAAE;QACZ,MAAM,EAAE,eAAe;QACvB,IAAI,EAAE,CAAC;QACP,KAAK,EAAE,EAAE;QACT,MAAM,EAAE,WAAoB;QAC5B,SAAS,EAAE,MAAe;KACP;IAGrB,cAAc,EAAE;QACd,WAAW,EAAE,IAAI;QACjB,IAAI,EAAE,CAAC;QACP,KAAK,EAAE,EAAE;QACT,MAAM,EAAE,WAAoB;QAC5B,SAAS,EAAE,KAAc;KACN;IAGrB,aAAa,EAAE;QACb,WAAW,EAAE,KAAK;QAClB,IAAI,EAAE,CAAC;QACP,KAAK,EAAE,EAAE;KACU;IAGrB,aAAa,EAAE;QACb,MAAM,EAAE,WAAW;QACnB,UAAU,EAAE,mBAAmB;QAC/B,aAAa,EAAE,0BAA0B;QACzC,WAAW,EAAE,0BAA0B;QACvC,WAAW,EAAE,IAAI;QACjB,IAAI,EAAE,CAAC;QACP,KAAK,EAAE,CAAC;QACR,MAAM,EAAE,WAAoB;QAC5B,SAAS,EAAE,KAAc;KACN;CACtB,CAAC;AAGW,QAAA,kBAAkB,GAAG;IAEhC,aAAa,EAAE,CAAC,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,CAAC;IAGjE,UAAU,EAAE;QACV,0BAA0B;QAC1B,0BAA0B;QAC1B,0BAA0B;KAC3B;IAGD,YAAY,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,CAAC;IAGpD,UAAU,EAAE,CAAC,gBAAgB,EAAE,mBAAmB,EAAE,eAAe,CAAC;IAGpE,eAAe,EAAE;QAEf,EAAE,OAAO,EAAE,cAAc,EAAE;QAC3B,EAAE,OAAO,EAAE,EAAE,EAAE;QAGf,EAAE,SAAS,EAAE,cAAc,EAAE;QAC7B,EAAE,SAAS,EAAE,YAAY,EAAE;QAC3B,EAAE,OAAO,EAAE,0BAA0B,EAAE;QAGvC,EAAE,WAAW,EAAE,EAAE,EAAE;QACnB,EAAE,WAAW,EAAE,cAAc,EAAE;QAC/B,EAAE,WAAW,EAAE,OAAO,EAAE;QAGxB;YACE,SAAS,EAAE,0BAA0B;YACrC,OAAO,EAAE,0BAA0B;SACpC;KACF;CACF,CAAC;AAGW,QAAA,mBAAmB,GAAG;IAEjC,MAAM,EAAE;QACN,MAAM,EAAE,MAAM;QACd,GAAG,EAAE,eAAe;QACpB,OAAO,EAAE,EAAE,aAAa,EAAE,uBAAuB,EAAE;QACnD,IAAI,EAAE,4BAAoB;KAC3B;IAGD,MAAM,EAAE;QACN,MAAM,EAAE,KAAK;QACb,GAAG,EAAE,gEAAgE;QACrE,OAAO,EAAE,EAAE,aAAa,EAAE,wBAAwB,EAAE;KACrD;IAGD,eAAe,EAAE;QACf,MAAM,EAAE,KAAK;QACb,GAAG,EAAE,2BAA2B;QAChC,OAAO,EAAE,EAAE,aAAa,EAAE,uBAAuB,EAAE;KACpD;IAGD,mBAAmB,EAAE;QACnB,MAAM,EAAE,KAAK;QACb,GAAG,EAAE,4CAA4C;QACjD,OAAO,EAAE,EAAE,aAAa,EAAE,iCAAiC,EAAE;KAC9D;IAGD,cAAc,EAAE;QACd,MAAM,EAAE,KAAK;QACb,GAAG,EAAE,gDAAgD;QACrD,OAAO,EAAE,EAAE,aAAa,EAAE,uBAAuB,EAAE;KACpD;IAGD,WAAW,EAAE;QACX,MAAM,EAAE,KAAK;QACb,GAAG,EAAE,6DAA6D;QAClE,OAAO,EAAE,EAAE,aAAa,EAAE,uBAAuB,EAAE;KACpD;IAGD,QAAQ,EAAE;QACR,MAAM,EAAE,KAAK;QACb,GAAG,EAAE,qBAAqB;QAC1B,OAAO,EAAE,EAAE,aAAa,EAAE,wBAAwB,EAAE;KACrD;IAGD,OAAO,EAAE;QACP,MAAM,EAAE,KAAK;QACb,GAAG,EAAE,gCAAgC;QACrC,OAAO,EAAE,EAAE,aAAa,EAAE,uBAAuB,EAAE;KACpD;IAGD,MAAM,EAAE;QACN,MAAM,EAAE,OAAO;QACf,GAAG,EAAE,gCAAgC;QACrC,OAAO,EAAE,EAAE,aAAa,EAAE,uBAAuB,EAAE;QACnD,IAAI,EAAE,4BAAoB;KAC3B;IAGD,MAAM,EAAE;QACN,MAAM,EAAE,OAAO;QACf,GAAG,EAAE,uCAAuC;QAC5C,OAAO,EAAE,EAAE,aAAa,EAAE,uBAAuB,EAAE;KACpD;IAGD,OAAO,EAAE;QACP,MAAM,EAAE,OAAO;QACf,GAAG,EAAE,wCAAwC;QAC7C,OAAO,EAAE,EAAE,aAAa,EAAE,iCAAiC,EAAE;KAC9D;IAGD,QAAQ,EAAE;QACR,MAAM,EAAE,OAAO;QACf,GAAG,EAAE,yCAAyC;QAC9C,OAAO,EAAE,EAAE,aAAa,EAAE,iCAAiC,EAAE;KAC9D;IAGD,MAAM,EAAE;QACN,MAAM,EAAE,QAAQ;QAChB,GAAG,EAAE,gCAAgC;QACrC,OAAO,EAAE,EAAE,aAAa,EAAE,wBAAwB,EAAE;KACrD;CACF,CAAC;AAGW,QAAA,gBAAgB,GAAG;IAE9B,eAAe,EAAE;QACf,EAAE,EAAE,kBAAkB;QACtB,SAAS,EAAE,0BAA0B;QACrC,OAAO,EAAE,0BAA0B;QACnC,WAAW,EAAE,MAAM;QACnB,WAAW,EAAE,IAAI;QACjB,MAAM,EAAE,WAAW;QACnB,aAAa,EAAE,MAAM;QACrB,WAAW,EAAE,0BAA0B;QACvC,YAAY,EAAE,IAAI;QAClB,SAAS,EAAE,0BAA0B;QACrC,SAAS,EAAE,0BAA0B;QACrC,KAAK,EAAE;YACL,EAAE,EAAE,gBAAgB;YACpB,OAAO,EAAE,gCAAgC;YACzC,KAAK,EAAE,MAAM;YACb,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE;gBACR,EAAE,EAAE,mBAAmB;gBACvB,KAAK,EAAE,2CAA2C;gBAClD,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,OAAO;gBAChB,IAAI,EAAE,WAAW;gBACjB,MAAM,EAAE,CAAC,0CAA0C,CAAC;gBACpD,IAAI,EAAE;oBACJ,EAAE,EAAE,qBAAqB;oBACzB,IAAI,EAAE,cAAc;oBACpB,KAAK,EAAE,mBAAmB;oBAC1B,KAAK,EAAE,eAAe;iBACvB;aACF;YACD,IAAI,EAAE;gBACJ,EAAE,EAAE,aAAa;gBACjB,IAAI,EAAE,cAAc;gBACpB,KAAK,EAAE,kBAAkB;gBACzB,KAAK,EAAE,eAAe;aACvB;SACF;QACD,aAAa,EAAE;YACb;gBACE,EAAE,EAAE,qBAAqB;gBACzB,KAAK,EAAE,mBAAmB;gBAC1B,OAAO,EAAE,uDAAuD;gBAChE,SAAS,EAAE,0BAA0B;aACtC;SACF;KACF;IAGD,iBAAiB,EAAE;QACjB,IAAI,EAAE,EAEL;QACD,KAAK,EAAE,EAAE;QACT,IAAI,EAAE,CAAC;QACP,KAAK,EAAE,EAAE;QACT,UAAU,EAAE,CAAC;QACb,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,KAAK;KACf;IAGD,aAAa,EAAE;QACb,aAAa,EAAE,GAAG;QAClB,iBAAiB,EAAE,GAAG;QACtB,eAAe,EAAE,EAAE;QACnB,iBAAiB,EAAE,EAAE;QACrB,iBAAiB,EAAE,CAAC;QACpB,YAAY,EAAE,OAAO;QACrB,kBAAkB,EAAE,EAAE;QACtB,gBAAgB,EAAE,IAAI;QACtB,eAAe,EAAE;YACf,SAAS,EAAE,EAAE;YACb,SAAS,EAAE,EAAE;YACb,SAAS,EAAE,EAAE;SACd;QACD,gBAAgB,EAAE;YAChB,OAAO,EAAE,EAAE;YACX,SAAS,EAAE,GAAG;YACd,SAAS,EAAE,EAAE;YACb,SAAS,EAAE,CAAC;SACb;QACD,aAAa,EAAE;YACb;gBACE,EAAE,EAAE,iBAAiB;gBACrB,KAAK,EAAE,6BAA6B;gBACpC,YAAY,EAAE,EAAE;gBAChB,YAAY,EAAE,KAAK;aACpB;YACD;gBACE,EAAE,EAAE,iBAAiB;gBACrB,KAAK,EAAE,wBAAwB;gBAC/B,YAAY,EAAE,CAAC;gBACf,YAAY,EAAE,KAAK;aACpB;SACF;KACF;IAGD,qBAAqB,EAAE;QACrB,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,8BAA8B;QACvC,IAAI,EAAE,EAEL;KACF;IAGD,oBAAoB,EAAE;QACpB,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,qCAAqC;QAC9C,IAAI,EAAE;YACJ,EAAE,EAAE,kBAAkB;YACtB,MAAM,EAAE,WAAW;YACnB,SAAS,EAAE,0BAA0B;SACtC;KACF;CACF,CAAC"}
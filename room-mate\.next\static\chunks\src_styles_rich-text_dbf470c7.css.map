{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/styles/rich-text.css"], "sourcesContent": ["/* Rich Text Display Styles */\r\n\r\n.rich-text-display {\r\n  color: #374151;\r\n  line-height: 1.7;\r\n}\r\n\r\n.rich-text-display * {\r\n  max-width: 100%;\r\n}\r\n\r\n/* Headings */\r\n.rich-text-display h1,\r\n.rich-text-display h2,\r\n.rich-text-display h3,\r\n.rich-text-display h4,\r\n.rich-text-display h5,\r\n.rich-text-display h6 {\r\n  margin-top: 1.5em;\r\n  margin-bottom: 0.5em;\r\n  font-weight: 600;\r\n  line-height: 1.4;\r\n  color: inherit;\r\n}\r\n\r\n.rich-text-display h1 {\r\n  font-size: 2em;\r\n  margin-top: 0;\r\n}\r\n\r\n.rich-text-display h2 {\r\n  font-size: 1.5em;\r\n}\r\n\r\n.rich-text-display h3 {\r\n  font-size: 1.25em;\r\n}\r\n\r\n.rich-text-display h4 {\r\n  font-size: 1.125em;\r\n}\r\n\r\n.rich-text-display h5,\r\n.rich-text-display h6 {\r\n  font-size: 1em;\r\n}\r\n\r\n/* Paragraphs */\r\n.rich-text-display p {\r\n  margin-top: 0;\r\n  margin-bottom: 1em;\r\n}\r\n\r\n.rich-text-display p:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* Links */\r\n.rich-text-display a {\r\n  color: #2563eb;\r\n  text-decoration: none;\r\n  font-weight: 500;\r\n  transition: color 0.2s ease;\r\n}\r\n\r\n.rich-text-display a:hover {\r\n  color: #1d4ed8;\r\n  text-decoration: underline;\r\n}\r\n\r\n/* Lists */\r\n.rich-text-display ul,\r\n.rich-text-display ol {\r\n  margin-top: 0;\r\n  margin-bottom: 1em;\r\n  padding-left: 1.5em;\r\n}\r\n\r\n.rich-text-display li {\r\n  margin-bottom: 0.25em;\r\n}\r\n\r\n.rich-text-display li:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.rich-text-display ul li {\r\n  list-style-type: disc;\r\n}\r\n\r\n.rich-text-display ol li {\r\n  list-style-type: decimal;\r\n}\r\n\r\n.rich-text-display ul ul,\r\n.rich-text-display ol ol,\r\n.rich-text-display ul ol,\r\n.rich-text-display ol ul {\r\n  margin-top: 0.25em;\r\n  margin-bottom: 0.25em;\r\n}\r\n\r\n/* Emphasis */\r\n.rich-text-display strong,\r\n.rich-text-display b {\r\n  font-weight: 600;\r\n  color: inherit;\r\n}\r\n\r\n.rich-text-display em,\r\n.rich-text-display i {\r\n  font-style: italic;\r\n}\r\n\r\n.rich-text-display u {\r\n  text-decoration: underline;\r\n}\r\n\r\n/* Blockquotes */\r\n.rich-text-display blockquote {\r\n  margin: 1.5em 0;\r\n  padding-left: 1em;\r\n  border-left: 4px solid #d1d5db;\r\n  font-style: italic;\r\n  background-color: #f9fafb;\r\n  padding: 1em;\r\n  border-radius: 0.375rem;\r\n}\r\n\r\n.rich-text-display blockquote p {\r\n  margin: 0;\r\n}\r\n\r\n/* Code */\r\n.rich-text-display code {\r\n  background-color: #f3f4f6;\r\n  padding: 0.125rem 0.25rem;\r\n  border-radius: 0.25rem;\r\n  font-family: ui-monospace, SFMono-Regular, \"SF Mono\", Consolas,\r\n    \"Liberation Mono\", Menlo, monospace;\r\n  font-size: 0.875em;\r\n  font-weight: 400;\r\n}\r\n\r\n.rich-text-display pre {\r\n  background-color: #f3f4f6;\r\n  padding: 1rem;\r\n  border-radius: 0.5rem;\r\n  overflow-x: auto;\r\n  margin: 1em 0;\r\n}\r\n\r\n.rich-text-display pre code {\r\n  background-color: transparent;\r\n  padding: 0;\r\n  border-radius: 0;\r\n  font-size: inherit;\r\n}\r\n\r\n/* Images */\r\n.rich-text-display img {\r\n  max-width: 100%;\r\n  height: auto;\r\n  border-radius: 0.5rem;\r\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\r\n  margin: 1em 0;\r\n}\r\n\r\n/* Tables */\r\n.rich-text-display table {\r\n  width: 100%;\r\n  border-collapse: collapse;\r\n  margin: 1em 0;\r\n  border: 1px solid #d1d5db;\r\n  border-radius: 0.375rem;\r\n  overflow: hidden;\r\n}\r\n\r\n.rich-text-display th,\r\n.rich-text-display td {\r\n  padding: 0.75rem;\r\n  text-align: left;\r\n  border: 1px solid #d1d5db;\r\n}\r\n\r\n.rich-text-display th {\r\n  background-color: #f9fafb;\r\n  font-weight: 600;\r\n}\r\n\r\n.rich-text-display tbody tr:nth-child(even) {\r\n  background-color: #f9fafb;\r\n}\r\n\r\n/* Horizontal Rules */\r\n.rich-text-display hr {\r\n  margin: 2em 0;\r\n  border: 0;\r\n  border-top: 1px solid #d1d5db;\r\n}\r\n\r\n/* Variants */\r\n.rich-text-variant-card {\r\n  background-color: white;\r\n  padding: 1.5rem;\r\n  border-radius: 0.5rem;\r\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);\r\n  border: 1px solid #e5e7eb;\r\n}\r\n\r\n.rich-text-variant-quote {\r\n  background-color: #f9fafb;\r\n  padding: 1.5rem;\r\n  border-radius: 0.5rem;\r\n  border-left: 4px solid #3b82f6;\r\n  font-style: italic;\r\n  position: relative;\r\n}\r\n\r\n.rich-text-variant-quote::before {\r\n  content: '\"';\r\n  font-size: 4rem;\r\n  color: #3b82f6;\r\n  position: absolute;\r\n  top: -0.5rem;\r\n  left: 1rem;\r\n  font-family: serif;\r\n  opacity: 0.3;\r\n}\r\n\r\n/* Dark mode support */\r\n@media (prefers-color-scheme: dark) {\r\n  .rich-text-display {\r\n    color: #f3f4f6;\r\n  }\r\n\r\n  .rich-text-display h1,\r\n  .rich-text-display h2,\r\n  .rich-text-display h3,\r\n  .rich-text-display h4,\r\n  .rich-text-display h5,\r\n  .rich-text-display h6 {\r\n    color: #f9fafb;\r\n  }\r\n\r\n  .rich-text-display a {\r\n    color: #60a5fa;\r\n  }\r\n\r\n  .rich-text-display a:hover {\r\n    color: #93c5fd;\r\n  }\r\n\r\n  .rich-text-display blockquote {\r\n    background-color: #374151;\r\n    border-left-color: #6b7280;\r\n  }\r\n\r\n  .rich-text-display code {\r\n    background-color: #374151;\r\n    color: #f3f4f6;\r\n  }\r\n\r\n  .rich-text-display pre {\r\n    background-color: #374151;\r\n  }\r\n\r\n  .rich-text-display table {\r\n    border-color: #4b5563;\r\n  }\r\n\r\n  .rich-text-display th,\r\n  .rich-text-display td {\r\n    border-color: #4b5563;\r\n  }\r\n\r\n  .rich-text-display th {\r\n    background-color: #374151;\r\n  }\r\n\r\n  .rich-text-display tbody tr:nth-child(even) {\r\n    background-color: #374151;\r\n  }\r\n\r\n  .rich-text-display hr {\r\n    border-top-color: #4b5563;\r\n  }\r\n\r\n  .rich-text-variant-card {\r\n    background-color: #1f2937;\r\n    border-color: #374151;\r\n  }\r\n\r\n  .rich-text-variant-quote {\r\n    background-color: #374151;\r\n  }\r\n}\r\n\r\n/* Animation for fade-in */\r\n@keyframes fade-in {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(10px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.animate-fade-in {\r\n  animation: fade-in 0.5s ease-out;\r\n}\r\n\r\n/* Responsive text sizing */\r\n@media (max-width: 640px) {\r\n  .rich-text-display h1 {\r\n    font-size: 1.75em;\r\n  }\r\n\r\n  .rich-text-display h2 {\r\n    font-size: 1.375em;\r\n  }\r\n\r\n  .rich-text-display h3 {\r\n    font-size: 1.125em;\r\n  }\r\n\r\n  .rich-text-display blockquote {\r\n    margin-left: 0;\r\n    margin-right: 0;\r\n    padding-left: 0.75rem;\r\n    padding-right: 0.75rem;\r\n  }\r\n\r\n  .rich-text-display table {\r\n    font-size: 0.875rem;\r\n  }\r\n\r\n  .rich-text-display th,\r\n  .rich-text-display td {\r\n    padding: 0.5rem;\r\n  }\r\n}\r\n\r\n/* Print styles */\r\n@media print {\r\n  .rich-text-display {\r\n    color: black;\r\n  }\r\n\r\n  .rich-text-display a {\r\n    color: black;\r\n    text-decoration: underline;\r\n  }\r\n\r\n  .rich-text-display blockquote {\r\n    background-color: transparent;\r\n    border-left: 2px solid black;\r\n  }\r\n\r\n  .rich-text-display code,\r\n  .rich-text-display pre {\r\n    background-color: #f5f5f5;\r\n    border: 1px solid #ccc;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAEA;;;;;AAKA;;;;AAKA;;;;;;;;AAaA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAMA;;;;;AAKA;;;;AAKA;;;;;;;AAOA;;;;;AAMA;;;;;;AAOA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AASA;;;;;AAMA;;;;AAKA;;;;AAKA;;;;;;;;;AAUA;;;;AAKA;;;;;;;;;AAUA;;;;;;;;AAQA;;;;;;;AAQA;;;;;;;;AASA;;;;;;;;;AASA;;;;;;AAOA;;;;;AAKA;;;;AAKA;;;;;;AAOA;;;;;;;;AAQA;;;;;;;;;AASA;;;;;;;;;;;AAYA;EACE;;;;EAIA;;;;EASA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EASA;;;;EAQA;;;;EAIA;;;;;EAKA;;;;;AAMF;;;;;;;;;;;;AAWA;;;;AAKA;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;;EAOA;;;;EAIA;;;;;AAOF;EACE;;;;EAIA;;;;;EAKA;;;;;EAKA", "debugId": null}}]}
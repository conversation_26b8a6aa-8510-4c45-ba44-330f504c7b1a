generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                  String                @id @default(uuid())
  name                String
  email               String                @unique
  password            String
  phone               String?
  smoker              Boolean               @default(false)
  age                 String?
  gender              String?
  nationality         String?
  country             String?
  occupation          String?
  isAdmin             <PERSON>               @default(false)
  isVerified          Boolean               @default(false)
  isVIP               Boolean               @default(false)
  freeListingCount    Int                   @default(4)
  createdAt           DateTime              @default(now())
  updatedAt           DateTime              @updatedAt
  notifications       Notification[]        @relation("UserNotifications")
  sentNotifications   Notification[]        @relation("AdminNotifications")
  properties          Property[]            @relation("PropertyOwner")
  favorites           Property[]            @relation("UserFavorites")
  offers              Offer[]
  bookings            Booking[]
  ratings             Rating[]
  verifications       Verification[]
  personListings      Person[]              @relation("PersonOwner")
  personFavorites     Person[]              @relation("UserPersonFavorites")
  comments            Comment[]
  Property            Property[]
  VerificationRequest VerificationRequest[]
}

model Notification {
  id        String   @id @default(uuid())
  title     String
  message   String
  read      <PERSON><PERSON>an  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relation to the user receiving the notification
  userId String
  user   User   @relation("UserNotifications", fields: [userId], references: [id])

  // Relation to the admin sending the notification
  adminId String
  admin   User   @relation("AdminNotifications", fields: [adminId], references: [id])
}

model Category {
  id         String     @id @default(uuid())
  name       String     @unique
  icon       String?
  createdAt  DateTime   @default(now())
  updatedAt  DateTime   @updatedAt
  properties Property[]
  Person     Person[]
}

model Property {
  id                              String       @id @default(uuid())
  title                           String?
  slug                            String?      @unique
  images                          String[]     @default([])
  city                            String?
  country                         String?
  neighborhood                    String?
  address                         String?
  description                     String?
  latitude                        String?
  longitude                       String?
  type                            PropertyType @default(house)
  roomType                        RoomType     @default(single)
  genderRequired                  String?
  totalRooms                      String?
  availableRooms                  String?
  roomsToComplete                 String?
  price                           String?
  size                            String?
  floor                           String?
  bathrooms                       String?
  separatedBathroom               Boolean      @default(false)
  residentsCount                  String?
  availablePersons                String?
  rentTime                        RentTime     @default(monthly)
  paymentTime                     PaymentTime  @default(monthly)
  priceIncludeWaterAndElectricity Boolean      @default(false)
  allowSmoking                    Boolean      @default(false)
  includeFurniture                Boolean      @default(false)
  airConditioning                 Boolean      @default(false)
  includeWaterHeater              Boolean      @default(false)
  parking                         Boolean      @default(false)
  internet                        Boolean      @default(false)
  nearToMetro                     Boolean      @default(false)
  nearToMarket                    Boolean      @default(false)
  elevator                        Boolean      @default(false)
  trialPeriod                     Boolean      @default(false)
  goodForForeigners               Boolean      @default(false)
  rating                          Float        @default(0)
  totalRatings                    Int          @default(0)
  offers                          Offer[]
  bookings                        Booking[]
  ratings                         Rating[]
  termsAndConditions              String?
  isVerified                      Boolean      @default(false)
  isAvailable                     Boolean      @default(true)
  createdAt                       DateTime     @default(now())
  updatedAt                       DateTime     @updatedAt

  categoryId String
  category   Category @relation(fields: [categoryId], references: [id])

  ownerId String
  owner   User   @relation("PropertyOwner", fields: [ownerId], references: [id])

  favorites           User[]                @relation("UserFavorites")
  verifications       Verification[]
  comments            Comment[]
  User                User?                 @relation(fields: [userId], references: [id])
  userId              String?
  VerificationRequest VerificationRequest[]
}

model Person {
  id                              String       @id @default(uuid())
  title                           String?
  slug                            String?      @unique
  city                            String?
  images                          String[]     @default([])
  country                         String?
  neighborhood                    String?
  address                         String?
  description                     String?
  latitude                        String?
  longitude                       String?
  type                            PropertyType @default(house)
  roomType                        RoomType     @default(single)
  genderRequired                  String?
  totalRooms                      String?
  availableRooms                  String?
  price                           String?
  size                            String?
  floor                           String?
  bathrooms                       String?
  separatedBathroom               Boolean      @default(false)
  residentsCount                  String?
  availablePersons                String?
  rentTime                        RentTime     @default(monthly)
  paymentTime                     PaymentTime  @default(monthly)
  priceIncludeWaterAndElectricity Boolean      @default(false)
  allowSmoking                    Boolean      @default(false)
  includeFurniture                Boolean      @default(false)
  airConditioning                 Boolean      @default(false)
  includeWaterHeater              Boolean      @default(false)
  parking                         Boolean      @default(false)
  internet                        Boolean      @default(false)
  nearToMetro                     Boolean      @default(false)
  nearToMarket                    Boolean      @default(false)
  elevator                        Boolean      @default(false)
  trialPeriod                     Boolean      @default(false)
  goodForForeigners               Boolean      @default(false)
  rating                          Float        @default(0)
  totalRatings                    Int          @default(0)
  termsAndConditions              String?
  isVerified                      Boolean      @default(false)
  isAvailable                     Boolean      @default(true)
  createdAt                       DateTime     @default(now())
  updatedAt                       DateTime     @updatedAt

  categoryId String
  category   Category @relation(fields: [categoryId], references: [id])

  ownerId String
  owner   User   @relation("PersonOwner", fields: [ownerId], references: [id])

  offers Offer[]

  favorites     User[]         @relation("UserPersonFavorites")
  verifications Verification[]
  comments      Comment[]
  Rating        Rating[]
}

model Comment {
  id        String   @id @default(uuid())
  content   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  userId String
  user   User   @relation(fields: [userId], references: [id])

  propertyId String?
  property   Property? @relation(fields: [propertyId], references: [id])

  personId String?
  person   Person? @relation(fields: [personId], references: [id])
}

model Offer {
  id        String      @id @default(uuid())
  message   String
  price     String
  phone     String
  duration  String?
  deposit   Boolean     @default(false)
  status    OfferStatus @default(pending)
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt

  propertyId String?
  property   Property? @relation(fields: [propertyId], references: [id])

  personId String?
  person   Person? @relation(fields: [personId], references: [id])

  userId String
  user   User   @relation(fields: [userId], references: [id])

  booking Booking?
}

// New Booking model to track confirmed bookings
model Booking {
  id          String        @id @default(uuid())
  startDate   DateTime
  endDate     DateTime?
  totalAmount String
  status      BookingStatus @default(confirmed)
  depositPaid Boolean       @default(false)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  userId String
  user   User   @relation(fields: [userId], references: [id])

  propertyId String
  property   Property @relation(fields: [propertyId], references: [id])

  offerId String @unique
  offer   Offer  @relation(fields: [offerId], references: [id])
}

// Rating model to track property and person ratings
model Rating {
  id        String   @id @default(uuid())
  score     Float
  comment   String?
  createdAt DateTime @default(now())

  userId String
  user   User   @relation(fields: [userId], references: [id])

  propertyId String?
  property   Property? @relation(fields: [propertyId], references: [id])

  personId String?
  person   Person? @relation(fields: [personId], references: [id])

  @@unique([userId, propertyId])
  @@unique([userId, personId])
}

model Verification {
  id        String             @id @default(uuid())
  status    VerificationStatus @default(pending)
  createdAt DateTime           @default(now())
  updatedAt DateTime           @updatedAt

  userId String?
  user   User?   @relation(fields: [userId], references: [id])

  propertyId String?
  property   Property? @relation(fields: [propertyId], references: [id])
  Person     Person?   @relation(fields: [personId], references: [id])
  personId   String?
}

model VerificationRequest {
  id     String   @id @default(uuid())
  images String[]

  userId String
  user   User   @relation(fields: [userId], references: [id])

  propertyId String?
  property   Property? @relation(fields: [propertyId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

enum VerificationStatus {
  pending
  approved
  rejected
}

// Enums for Property model
enum PropertyType {
  house
  room
}

enum RoomType {
  mixed
  single
}

enum RentTime {
  daily
  weekly
  monthly
  quarterly
  semiannual
  annually
}

enum PaymentTime {
  daily
  weekly
  monthly
  quarterly
  semiannual
  annually
}

// Enums for Offer and Booking models
enum OfferStatus {
  pending
  accepted
  rejected
  cancelled
}

enum BookingStatus {
  confirmed
  cancelled
  completed
}

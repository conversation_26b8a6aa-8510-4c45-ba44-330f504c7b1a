"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VerificationService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
const client_1 = require("@prisma/client");
let VerificationService = class VerificationService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    verificationRequestSelectFields = {
        id: true,
        images: true,
        userId: true,
        propertyId: true,
        user: {
            select: {
                id: true,
                name: true,
                email: true,
            },
        },
        property: {
            select: {
                id: true,
                title: true,
                city: true,
                country: true,
            },
        },
        createdAt: true,
        updatedAt: true,
    };
    verificationSelectFields = {
        id: true,
        status: true,
        userId: true,
        propertyId: true,
        personId: true,
        user: {
            select: {
                id: true,
                name: true,
                email: true,
            },
        },
        property: {
            select: {
                id: true,
                title: true,
                city: true,
                country: true,
            },
        },
        person: {
            select: {
                id: true,
                title: true,
                city: true,
                country: true,
            },
        },
        createdAt: true,
        updatedAt: true,
    };
    async createVerificationRequest(createVerificationRequestDto, currentUser) {
        const { images, propertyId } = createVerificationRequestDto;
        if (propertyId) {
            const property = await this.prisma.property.findUnique({
                where: { id: propertyId },
                select: { id: true, ownerId: true, title: true },
            });
            if (!property) {
                throw new common_1.NotFoundException('Property not found');
            }
            if (property.ownerId !== currentUser.sub && !currentUser.isAdmin) {
                throw new common_1.ForbiddenException('You can only create verification requests for your own properties');
            }
            const existingRequest = await this.prisma.verificationRequest.findFirst({
                where: {
                    userId: currentUser.sub,
                    propertyId,
                },
            });
            if (existingRequest) {
                throw new common_1.ConflictException('A verification request already exists for this property');
            }
        }
        if (!propertyId) {
            const existingUserRequest = await this.prisma.verificationRequest.findFirst({
                where: {
                    userId: currentUser.sub,
                    propertyId: null,
                },
            });
            if (existingUserRequest) {
                throw new common_1.ConflictException('A user verification request already exists');
            }
        }
        try {
            const verificationRequest = await this.prisma.verificationRequest.create({
                data: {
                    images,
                    userId: currentUser.sub,
                    propertyId: propertyId || null,
                },
                select: this.verificationRequestSelectFields,
            });
            return verificationRequest;
        }
        catch {
            throw new common_1.BadRequestException('Failed to create verification request');
        }
    }
    async findAllVerificationRequests(query, currentUser) {
        if (!currentUser.isAdmin) {
            throw new common_1.ForbiddenException('Only admins can view all verification requests');
        }
        const { page = 1, limit = 10, userId, propertyId, sortBy = 'createdAt', sortOrder = 'desc', } = query;
        const skip = (page - 1) * limit;
        const where = {};
        if (userId)
            where.userId = userId;
        if (propertyId)
            where.propertyId = propertyId;
        const total = await this.prisma.verificationRequest.count({ where });
        const verificationRequests = await this.prisma.verificationRequest.findMany({
            where,
            select: this.verificationRequestSelectFields,
            skip,
            take: limit,
            orderBy: { [sortBy]: sortOrder },
        });
        const totalPages = Math.ceil(total / limit);
        return {
            verificationRequests,
            pagination: {
                page,
                limit,
                total,
                totalPages,
                hasNext: page < totalPages,
                hasPrev: page > 1,
            },
        };
    }
    async findUserVerificationRequests(currentUser) {
        const verificationRequests = await this.prisma.verificationRequest.findMany({
            where: { userId: currentUser.sub },
            select: this.verificationRequestSelectFields,
            orderBy: { createdAt: 'desc' },
        });
        return verificationRequests;
    }
    async findOneVerificationRequest(id, currentUser) {
        const verificationRequest = await this.prisma.verificationRequest.findUnique({
            where: { id },
            select: this.verificationRequestSelectFields,
        });
        if (!verificationRequest) {
            throw new common_1.NotFoundException('Verification request not found');
        }
        if (verificationRequest.userId !== currentUser.sub &&
            !currentUser.isAdmin) {
            throw new common_1.ForbiddenException('You can only view your own verification requests');
        }
        return verificationRequest;
    }
    async deleteVerificationRequest(id, currentUser) {
        const verificationRequest = await this.prisma.verificationRequest.findUnique({
            where: { id },
            select: { id: true, userId: true },
        });
        if (!verificationRequest) {
            throw new common_1.NotFoundException('Verification request not found');
        }
        if (verificationRequest.userId !== currentUser.sub &&
            !currentUser.isAdmin) {
            throw new common_1.ForbiddenException('You can only delete your own verification requests');
        }
        await this.prisma.verificationRequest.delete({ where: { id } });
        return { message: 'Verification request deleted successfully' };
    }
    async processVerificationRequest(requestId, updateVerificationDto, currentUser) {
        if (!currentUser.isAdmin) {
            throw new common_1.ForbiddenException('Only admins can process verification requests');
        }
        const verificationRequest = await this.prisma.verificationRequest.findUnique({
            where: { id: requestId },
            include: {
                user: {
                    select: { id: true, name: true, email: true },
                },
                property: {
                    select: { id: true, title: true, city: true, country: true },
                },
            },
        });
        if (!verificationRequest) {
            throw new common_1.NotFoundException('Verification request not found');
        }
        const { status } = updateVerificationDto;
        try {
            const result = await this.prisma.$transaction(async (prisma) => {
                const verification = await prisma.verification.create({
                    data: {
                        status,
                        userId: verificationRequest.userId,
                        propertyId: verificationRequest.propertyId,
                    },
                    select: this.verificationSelectFields,
                });
                if (status === client_1.VerificationStatus.approved) {
                    if (verificationRequest.propertyId) {
                        await prisma.property.update({
                            where: { id: verificationRequest.propertyId },
                            data: { isVerified: true },
                        });
                    }
                    else {
                        await prisma.user.update({
                            where: { id: verificationRequest.userId },
                            data: { isVerified: true },
                        });
                    }
                }
                await prisma.verificationRequest.delete({
                    where: { id: requestId },
                });
                return verification;
            });
            return result;
        }
        catch (error) {
            console.error(error);
            throw new common_1.BadRequestException('Failed to process verification request');
        }
    }
    async findAllVerifications(query, currentUser) {
        if (!currentUser.isAdmin) {
            throw new common_1.ForbiddenException('Only admins can view all verifications');
        }
        const { page = 1, limit = 10, status, userId, propertyId, personId, sortBy = 'createdAt', sortOrder = 'desc', } = query;
        const skip = (page - 1) * limit;
        const where = {};
        if (status)
            where.status = status;
        if (userId)
            where.userId = userId;
        if (propertyId)
            where.propertyId = propertyId;
        if (personId)
            where.personId = personId;
        const total = await this.prisma.verification.count({ where });
        const verifications = await this.prisma.verification.findMany({
            where,
            select: this.verificationSelectFields,
            skip,
            take: limit,
            orderBy: { [sortBy]: sortOrder },
        });
        const totalPages = Math.ceil(total / limit);
        return {
            verifications,
            pagination: {
                page,
                limit,
                total,
                totalPages,
                hasNext: page < totalPages,
                hasPrev: page > 1,
            },
        };
    }
    async findUserVerifications(currentUser) {
        const verifications = await this.prisma.verification.findMany({
            where: { userId: currentUser.sub },
            select: this.verificationSelectFields,
            orderBy: { createdAt: 'desc' },
        });
        return verifications;
    }
    async findOneVerification(id, currentUser) {
        const verification = await this.prisma.verification.findUnique({
            where: { id },
            select: this.verificationSelectFields,
        });
        if (!verification) {
            throw new common_1.NotFoundException('Verification not found');
        }
        if (verification.userId !== currentUser.sub && !currentUser.isAdmin) {
            throw new common_1.ForbiddenException('You can only view your own verifications');
        }
        return verification;
    }
    async getVerificationStats() {
        const [totalRequests, pendingRequests, approvedVerifications, rejectedVerifications,] = await Promise.all([
            this.prisma.verificationRequest.count(),
            this.prisma.verificationRequest.count(),
            this.prisma.verification.count({
                where: { status: client_1.VerificationStatus.approved },
            }),
            this.prisma.verification.count({
                where: { status: client_1.VerificationStatus.rejected },
            }),
        ]);
        return {
            totalRequests,
            pendingRequests,
            approvedVerifications,
            rejectedVerifications,
        };
    }
    async revokeVerification(id, currentUser) {
        if (!currentUser.isAdmin) {
            throw new common_1.ForbiddenException('Only admins can revoke verifications');
        }
        const verification = await this.prisma.verification.findUnique({
            where: { id },
            include: {
                user: { select: { id: true } },
                property: { select: { id: true } },
            },
        });
        if (!verification) {
            throw new common_1.NotFoundException('Verification not found');
        }
        if (verification.status !== client_1.VerificationStatus.approved) {
            throw new common_1.ConflictException('Can only revoke approved verifications');
        }
        try {
            const result = await this.prisma.$transaction(async (prisma) => {
                const updatedVerification = await prisma.verification.update({
                    where: { id },
                    data: { status: client_1.VerificationStatus.rejected },
                    select: this.verificationSelectFields,
                });
                if (verification.propertyId) {
                    await prisma.property.update({
                        where: { id: verification.propertyId },
                        data: { isVerified: false },
                    });
                }
                else if (verification.userId) {
                    await prisma.user.update({
                        where: { id: verification.userId },
                        data: { isVerified: false },
                    });
                }
                return updatedVerification;
            });
            return result;
        }
        catch (error) {
            console.error(error);
            throw new common_1.BadRequestException('Failed to revoke verification');
        }
    }
};
exports.VerificationService = VerificationService;
exports.VerificationService = VerificationService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], VerificationService);
//# sourceMappingURL=verification.service.js.map
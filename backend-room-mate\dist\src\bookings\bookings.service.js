"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BookingsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
let BookingsService = class BookingsService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createFromOffer(createBookingDto, user) {
        const { offerId, startDate, endDate, totalAmount, depositPaid = false, } = createBookingDto;
        const offer = await this.prisma.offer.findUnique({
            where: { id: offerId },
            include: {
                property: {
                    select: {
                        id: true,
                        title: true,
                        isAvailable: true,
                        ownerId: true,
                    },
                },
                booking: true,
            },
        });
        if (!offer) {
            throw new common_1.NotFoundException('Offer not found');
        }
        if (!offer.property) {
            throw new common_1.NotFoundException('Property not found for this offer');
        }
        const property = offer.property;
        if (property.ownerId !== user.sub && !user.isAdmin) {
            throw new common_1.ForbiddenException('Only property owner can create bookings from offers');
        }
        if (offer.status !== 'accepted') {
            throw new common_1.ConflictException('Only accepted offers can be converted to bookings');
        }
        if (offer.booking) {
            throw new common_1.ConflictException('Booking already exists for this offer');
        }
        const startDateTime = new Date(startDate);
        const endDateTime = endDate ? new Date(endDate) : null;
        if (endDateTime && startDateTime >= endDateTime) {
            throw new common_1.BadRequestException('End date must be after start date');
        }
        const overlappingBookings = await this.prisma.booking.findMany({
            where: {
                propertyId: offer.property.id,
                status: { in: ['confirmed', 'completed'] },
                OR: [
                    {
                        AND: [
                            { startDate: { lte: startDateTime } },
                            {
                                OR: [{ endDate: { gte: startDateTime } }, { endDate: null }],
                            },
                        ],
                    },
                    ...(endDateTime
                        ? [
                            {
                                AND: [
                                    { startDate: { lte: endDateTime } },
                                    {
                                        OR: [
                                            { endDate: { gte: endDateTime } },
                                            { endDate: null },
                                        ],
                                    },
                                ],
                            },
                        ]
                        : []),
                ],
            },
        });
        if (overlappingBookings.length > 0) {
            throw new common_1.ConflictException('Property is already booked for the selected dates');
        }
        const booking = await this.prisma.booking.create({
            data: {
                offerId,
                userId: offer.userId,
                propertyId: offer.property.id,
                startDate: startDateTime,
                endDate: endDateTime,
                totalAmount,
                depositPaid,
                status: 'confirmed',
            },
            include: {
                offer: {
                    select: {
                        id: true,
                        message: true,
                        price: true,
                        phone: true,
                    },
                },
                property: {
                    select: {
                        id: true,
                        title: true,
                        city: true,
                        country: true,
                        address: true,
                        images: true,
                    },
                },
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        phone: true,
                    },
                },
            },
        });
        return {
            success: true,
            data: booking,
            message: 'Booking created successfully',
        };
    }
    async findAll(query, user) {
        const { page = 1, limit = 10, search, status, propertyId, userId, depositPaid, startDateFrom, startDateTo, sortBy = 'createdAt', sortOrder = 'desc', } = query;
        const offset = (page - 1) * limit;
        const where = {};
        if (user && !user.isAdmin) {
            where.OR = [
                { userId: user.sub },
                {
                    property: {
                        ownerId: user.sub,
                    },
                },
            ];
        }
        if (search) {
            const searchConditions = [
                {
                    property: {
                        title: { contains: search, mode: 'insensitive' },
                    },
                },
                {
                    user: {
                        name: { contains: search, mode: 'insensitive' },
                    },
                },
            ];
            if (where.OR) {
                where.AND = [{ OR: where.OR }, { OR: searchConditions }];
                delete where.OR;
            }
            else {
                where.OR = searchConditions;
            }
        }
        if (status)
            where.status = status;
        if (propertyId)
            where.propertyId = propertyId;
        if (userId)
            where.userId = userId;
        if (depositPaid !== undefined)
            where.depositPaid = depositPaid;
        if (startDateFrom || startDateTo) {
            where.startDate = {};
            if (startDateFrom)
                where.startDate.gte = new Date(startDateFrom);
            if (startDateTo)
                where.startDate.lte = new Date(startDateTo);
        }
        const [bookings, total] = await Promise.all([
            this.prisma.booking.findMany({
                where,
                skip: offset,
                take: limit,
                orderBy: { [sortBy]: sortOrder },
                include: {
                    property: {
                        select: {
                            id: true,
                            title: true,
                            city: true,
                            country: true,
                            address: true,
                            images: true,
                            price: true,
                        },
                    },
                    user: {
                        select: {
                            id: true,
                            name: true,
                            email: true,
                            phone: true,
                        },
                    },
                    offer: {
                        select: {
                            id: true,
                            message: true,
                            price: true,
                        },
                    },
                },
            }),
            this.prisma.booking.count({ where }),
        ]);
        const totalPages = Math.ceil(total / limit);
        return {
            success: true,
            data: bookings,
            pagination: {
                page,
                limit,
                total,
                totalPages,
                hasNext: page < totalPages,
                hasPrev: page > 1,
            },
        };
    }
    async findOne(id) {
        const booking = await this.prisma.booking.findUnique({
            where: { id },
            include: {
                offer: {
                    select: {
                        id: true,
                        message: true,
                        price: true,
                        phone: true,
                        duration: true,
                        deposit: true,
                    },
                },
                property: {
                    select: {
                        id: true,
                        title: true,
                        city: true,
                        country: true,
                        address: true,
                        description: true,
                        images: true,
                        price: true,
                        owner: {
                            select: {
                                id: true,
                                name: true,
                                email: true,
                                phone: true,
                            },
                        },
                    },
                },
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        phone: true,
                    },
                },
            },
        });
        if (!booking) {
            throw new common_1.NotFoundException('Booking not found');
        }
        return {
            success: true,
            data: booking,
        };
    }
    async update(id, updateBookingDto, user) {
        const booking = await this.prisma.booking.findUnique({
            where: { id },
            include: {
                property: {
                    select: { ownerId: true },
                },
            },
        });
        if (!booking) {
            throw new common_1.NotFoundException('Booking not found');
        }
        const isGuest = booking.userId === user.sub;
        const isPropertyOwner = booking.property.ownerId === user.sub;
        const isAdmin = user.isAdmin;
        if (!isGuest && !isPropertyOwner && !isAdmin) {
            throw new common_1.ForbiddenException('Access denied');
        }
        const allowedUpdates = {};
        if (updateBookingDto.depositPaid !== undefined &&
            (isPropertyOwner || isAdmin)) {
            allowedUpdates.depositPaid = updateBookingDto.depositPaid;
        }
        if (updateBookingDto.status && (isPropertyOwner || isAdmin)) {
            if (booking.status === 'completed' &&
                updateBookingDto.status !== 'completed') {
                throw new common_1.ConflictException('Cannot change status of completed booking');
            }
            allowedUpdates.status = updateBookingDto.status;
        }
        if (updateBookingDto.endDate && (isPropertyOwner || isAdmin)) {
            const endDateTime = new Date(updateBookingDto.endDate);
            const startDateTime = new Date(booking.startDate);
            if (endDateTime <= startDateTime) {
                throw new common_1.BadRequestException('End date must be after start date');
            }
            allowedUpdates.endDate = endDateTime;
        }
        if (Object.keys(allowedUpdates).length === 0) {
            throw new common_1.ForbiddenException('No valid updates provided for your role');
        }
        const updatedBooking = await this.prisma.booking.update({
            where: { id },
            data: allowedUpdates,
            include: {
                property: {
                    select: {
                        id: true,
                        title: true,
                        city: true,
                        country: true,
                        price: true,
                    },
                },
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                    },
                },
                offer: {
                    select: {
                        id: true,
                        price: true,
                    },
                },
            },
        });
        return {
            success: true,
            data: updatedBooking,
            message: 'Booking updated successfully',
        };
    }
    async cancelBooking(id, user) {
        return this.updateBookingStatus(id, 'cancelled', user);
    }
    async completeBooking(id, user) {
        return this.updateBookingStatus(id, 'completed', user);
    }
    async updateBookingStatus(id, status, user) {
        const booking = await this.prisma.booking.findUnique({
            where: { id },
            include: {
                property: {
                    select: { ownerId: true },
                },
            },
        });
        if (!booking) {
            throw new common_1.NotFoundException('Booking not found');
        }
        const isGuest = booking.userId === user.sub;
        const isPropertyOwner = booking.property.ownerId === user.sub;
        const isAdmin = user.isAdmin;
        if (status === 'cancelled') {
            if (!isGuest && !isPropertyOwner && !isAdmin) {
                throw new common_1.ForbiddenException('Only guest or property owner can cancel bookings');
            }
            if (booking.status === 'completed') {
                throw new common_1.ConflictException('Cannot cancel completed booking');
            }
        }
        if (status === 'completed') {
            if (!isPropertyOwner && !isAdmin) {
                throw new common_1.ForbiddenException('Only property owner can mark booking as completed');
            }
            if (booking.status !== 'confirmed') {
                throw new common_1.ConflictException('Can only complete confirmed bookings');
            }
        }
        const updatedBooking = await this.prisma.booking.update({
            where: { id },
            data: { status },
            include: {
                property: {
                    select: {
                        id: true,
                        title: true,
                        city: true,
                        country: true,
                        price: true,
                    },
                },
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                    },
                },
            },
        });
        return {
            success: true,
            data: updatedBooking,
            message: `Booking ${status} successfully`,
        };
    }
    async getMyBookings(userId, query) {
        const { page = 1, limit = 10, status, sortBy = 'createdAt', sortOrder = 'desc', } = query;
        const offset = (page - 1) * limit;
        const where = { userId };
        if (status)
            where.status = status;
        const [bookings, total] = await Promise.all([
            this.prisma.booking.findMany({
                where,
                skip: offset,
                take: limit,
                orderBy: { [sortBy]: sortOrder },
                include: {
                    property: {
                        select: {
                            id: true,
                            title: true,
                            city: true,
                            country: true,
                            address: true,
                            images: true,
                            price: true,
                        },
                    },
                    offer: {
                        select: {
                            id: true,
                            price: true,
                        },
                    },
                },
            }),
            this.prisma.booking.count({ where }),
        ]);
        const totalPages = Math.ceil(total / limit);
        return {
            success: true,
            data: bookings,
            pagination: {
                page,
                limit,
                total,
                totalPages,
                hasNext: page < totalPages,
                hasPrev: page > 1,
            },
        };
    }
    async getPropertyBookings(propertyId, query) {
        const { page = 1, limit = 10, status, sortBy = 'createdAt', sortOrder = 'desc', } = query;
        const offset = (page - 1) * limit;
        const where = { propertyId };
        if (status)
            where.status = status;
        const [bookings, total] = await Promise.all([
            this.prisma.booking.findMany({
                where,
                skip: offset,
                take: limit,
                orderBy: { [sortBy]: sortOrder },
                include: {
                    user: {
                        select: {
                            id: true,
                            name: true,
                            email: true,
                            phone: true,
                        },
                    },
                    offer: {
                        select: {
                            id: true,
                            message: true,
                            price: true,
                        },
                    },
                },
            }),
            this.prisma.booking.count({ where }),
        ]);
        const totalPages = Math.ceil(total / limit);
        return {
            success: true,
            data: bookings,
            pagination: {
                page,
                limit,
                total,
                totalPages,
                hasNext: page < totalPages,
                hasPrev: page > 1,
            },
        };
    }
    async getBookingStats(user) {
        const where = {};
        if (!user.isAdmin) {
            where.OR = [
                { userId: user.sub },
                {
                    property: {
                        ownerId: user.sub,
                    },
                },
            ];
        }
        const stats = await this.prisma.booking.groupBy({
            by: ['status'],
            where,
            _count: {
                status: true,
            },
        });
        const completedBookings = await this.prisma.booking.findMany({
            where: {
                ...where,
                status: 'completed',
            },
            select: {
                totalAmount: true,
            },
        });
        const totalRevenue = completedBookings.reduce((sum, booking) => {
            const amount = parseFloat(booking.totalAmount) || 0;
            return sum + amount;
        }, 0);
        return {
            success: true,
            data: {
                statusBreakdown: stats.reduce((acc, stat) => {
                    acc[stat.status] = stat._count.status;
                    return acc;
                }, {}),
                totalRevenue: totalRevenue.toString(),
            },
        };
    }
};
exports.BookingsService = BookingsService;
exports.BookingsService = BookingsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], BookingsService);
//# sourceMappingURL=bookings.service.js.map
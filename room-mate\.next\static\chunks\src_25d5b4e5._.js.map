{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/providers/query-provider.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { QueryClient, QueryClientProvider } from \"@tanstack/react-query\";\r\nimport { useState } from \"react\";\r\n\r\n\r\nexport default function QueryProvider({\r\n  children,\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  const [queryClient] = useState(\r\n    () =>\r\n      new QueryClient({\r\n        defaultOptions: {\r\n          queries: {\r\n            staleTime: 60 * 1000,\r\n            refetchOnWindowFocus: false,\r\n          },\r\n        },\r\n      })\r\n  );\r\n\r\n  return (\r\n    <QueryClientProvider client={queryClient}>\r\n      {children}\r\n    </QueryClientProvider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;;;AAHA;;;AAMe,SAAS,cAAc,EACpC,QAAQ,EAGT;;IACC,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;kCAC3B,IACE,IAAI,gLAAA,CAAA,cAAW,CAAC;gBACd,gBAAgB;oBACd,SAAS;wBACP,WAAW,KAAK;wBAChB,sBAAsB;oBACxB;gBACF;YACF;;IAGJ,qBACE,6LAAC,yLAAA,CAAA,sBAAmB;QAAC,QAAQ;kBAC1B;;;;;;AAGP;GAtBwB;KAAA", "debugId": null}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/services/storage-security.ts"], "sourcesContent": ["// Security utilities for localStorage\r\nclass StorageSecurity {\r\n  private secretKey =\r\n    process.env.NEXT_PUBLIC_STORAGE_SECRET || \"default-secret-key\";\r\n\r\n  // Simple encryption using base64 and XOR (for demo - use crypto library in production)\r\n  private encrypt(text: string): string {\r\n    const key = this.secretKey;\r\n    let result = \"\";\r\n\r\n    for (let i = 0; i < text.length; i++) {\r\n      const charCode = text.charCodeAt(i) ^ key.charCodeAt(i % key.length);\r\n      result += String.fromCharCode(charCode);\r\n    }\r\n\r\n    return btoa(result);\r\n  }\r\n\r\n  private decrypt(encryptedText: string): string {\r\n    try {\r\n      const text = atob(encryptedText);\r\n      const key = this.secretKey;\r\n      let result = \"\";\r\n\r\n      for (let i = 0; i < text.length; i++) {\r\n        const charCode = text.charCodeAt(i) ^ key.charCodeAt(i % key.length);\r\n        result += String.fromCharCode(charCode);\r\n      }\r\n\r\n      return result;\r\n    } catch {\r\n      return \"\";\r\n    }\r\n  }\r\n\r\n  // Create a hash for integrity check\r\n  private createHash(data: string): string {\r\n    let hash = 0;\r\n    if (data.length === 0) return hash.toString();\r\n\r\n    for (let i = 0; i < data.length; i++) {\r\n      const char = data.charCodeAt(i);\r\n      hash = (hash << 5) - hash + char;\r\n      hash = hash & hash; // Convert to 32bit integer\r\n    }\r\n\r\n    return hash.toString();\r\n  }\r\n\r\n  // Encrypt and store data with integrity check\r\n  encryptAndStore(key: string, data: any): void {\r\n    try {\r\n      const jsonString = JSON.stringify(data);\r\n      const hash = this.createHash(jsonString);\r\n      const encryptedData = this.encrypt(jsonString);\r\n\r\n      const secureData = {\r\n        data: encryptedData,\r\n        hash: hash,\r\n        timestamp: Date.now(),\r\n      };\r\n\r\n      localStorage.setItem(key, JSON.stringify(secureData));\r\n    } catch (error) {\r\n      console.error(\"Failed to encrypt and store data:\", error);\r\n    }\r\n  }\r\n\r\n  // Decrypt and retrieve data with integrity check\r\n  decryptAndRetrieve(key: string): any {\r\n    try {\r\n      const storedData = localStorage.getItem(key);\r\n      if (!storedData) return null;\r\n\r\n      const secureData = JSON.parse(storedData);\r\n\r\n      // Check if data structure is valid\r\n      if (!secureData.data || !secureData.hash) {\r\n        console.warn(\"Invalid data structure detected\");\r\n        localStorage.removeItem(key);\r\n        return null;\r\n      }\r\n\r\n      const decryptedString = this.decrypt(secureData.data);\r\n      if (!decryptedString) {\r\n        console.warn(\"Failed to decrypt data\");\r\n        localStorage.removeItem(key);\r\n        return null;\r\n      }\r\n\r\n      // Verify integrity\r\n      const currentHash = this.createHash(decryptedString);\r\n      if (currentHash !== secureData.hash) {\r\n        console.warn(\r\n          \"Data integrity check failed - data may have been tampered with\"\r\n        );\r\n        localStorage.removeItem(key);\r\n        return null;\r\n      }\r\n\r\n      return JSON.parse(decryptedString);\r\n    } catch (error) {\r\n      console.error(\"Failed to decrypt and retrieve data:\", error);\r\n      localStorage.removeItem(key);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  // Clear stored data\r\n  clearStorage(key: string): void {\r\n    localStorage.removeItem(key);\r\n  }\r\n}\r\n\r\nexport const storageSecurity = new StorageSecurity();\r\n"], "names": [], "mappings": "AAAA,sCAAsC;;;;AAGlC;AAFJ,MAAM;IACI,YACN,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,0BAA0B,IAAI,qBAAqB;IAEjE,uFAAuF;IAC/E,QAAQ,IAAY,EAAU;QACpC,MAAM,MAAM,IAAI,CAAC,SAAS;QAC1B,IAAI,SAAS;QAEb,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YACpC,MAAM,WAAW,KAAK,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,IAAI,IAAI,MAAM;YACnE,UAAU,OAAO,YAAY,CAAC;QAChC;QAEA,OAAO,KAAK;IACd;IAEQ,QAAQ,aAAqB,EAAU;QAC7C,IAAI;YACF,MAAM,OAAO,KAAK;YAClB,MAAM,MAAM,IAAI,CAAC,SAAS;YAC1B,IAAI,SAAS;YAEb,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;gBACpC,MAAM,WAAW,KAAK,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,IAAI,IAAI,MAAM;gBACnE,UAAU,OAAO,YAAY,CAAC;YAChC;YAEA,OAAO;QACT,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,oCAAoC;IAC5B,WAAW,IAAY,EAAU;QACvC,IAAI,OAAO;QACX,IAAI,KAAK,MAAM,KAAK,GAAG,OAAO,KAAK,QAAQ;QAE3C,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YACpC,MAAM,OAAO,KAAK,UAAU,CAAC;YAC7B,OAAO,CAAC,QAAQ,CAAC,IAAI,OAAO;YAC5B,OAAO,OAAO,MAAM,2BAA2B;QACjD;QAEA,OAAO,KAAK,QAAQ;IACtB;IAEA,8CAA8C;IAC9C,gBAAgB,GAAW,EAAE,IAAS,EAAQ;QAC5C,IAAI;YACF,MAAM,aAAa,KAAK,SAAS,CAAC;YAClC,MAAM,OAAO,IAAI,CAAC,UAAU,CAAC;YAC7B,MAAM,gBAAgB,IAAI,CAAC,OAAO,CAAC;YAEnC,MAAM,aAAa;gBACjB,MAAM;gBACN,MAAM;gBACN,WAAW,KAAK,GAAG;YACrB;YAEA,aAAa,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD;IACF;IAEA,iDAAiD;IACjD,mBAAmB,GAAW,EAAO;QACnC,IAAI;YACF,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,CAAC,YAAY,OAAO;YAExB,MAAM,aAAa,KAAK,KAAK,CAAC;YAE9B,mCAAmC;YACnC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,IAAI,EAAE;gBACxC,QAAQ,IAAI,CAAC;gBACb,aAAa,UAAU,CAAC;gBACxB,OAAO;YACT;YAEA,MAAM,kBAAkB,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI;YACpD,IAAI,CAAC,iBAAiB;gBACpB,QAAQ,IAAI,CAAC;gBACb,aAAa,UAAU,CAAC;gBACxB,OAAO;YACT;YAEA,mBAAmB;YACnB,MAAM,cAAc,IAAI,CAAC,UAAU,CAAC;YACpC,IAAI,gBAAgB,WAAW,IAAI,EAAE;gBACnC,QAAQ,IAAI,CACV;gBAEF,aAAa,UAAU,CAAC;gBACxB,OAAO;YACT;YAEA,OAAO,KAAK,KAAK,CAAC;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,aAAa,UAAU,CAAC;YACxB,OAAO;QACT;IACF;IAEA,oBAAoB;IACpB,aAAa,GAAW,EAAQ;QAC9B,aAAa,UAAU,CAAC;IAC1B;AACF;AAEO,MAAM,kBAAkB,IAAI", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/store/useUserStore.ts"], "sourcesContent": ["import { create } from \"zustand\";\r\nimport { storageSecurity } from \"@/services/storage-security\";\r\n\r\nexport interface User {\r\n  id: string;\r\n  name: string;\r\n  email: string;\r\n  phone: string;\r\n  smoker: boolean;\r\n  age: string;\r\n  gender: string;\r\n  nationality: string;\r\n  occupation: string;\r\n  country: string;\r\n  isAdmin: boolean;\r\n  isVerified: boolean;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\ninterface UserState {\r\n  user: User | null;\r\n  isAuthenticated: boolean;\r\n  setUser: (user: User | null) => void;\r\n  clearUser: () => void;\r\n  _hasHydrated: boolean;\r\n  _setHasHydrated: (hasHydrated: boolean) => void;\r\n  shouldFetchUser: () => boolean;\r\n  refreshUser: () => Promise<void>;\r\n}\r\n\r\n// Custom storage implementation with encryption\r\nconst secureStorage = {\r\n  getItem: (name: string): string | null => {\r\n    const data = storageSecurity.decryptAndRetrieve(name);\r\n    return data ? JSON.stringify(data) : null;\r\n  },\r\n  setItem: (name: string, value: string): void => {\r\n    try {\r\n      const data = JSON.parse(value);\r\n      storageSecurity.encryptAndStore(name, data);\r\n    } catch (error) {\r\n      console.error(\"Failed to store encrypted data:\", error);\r\n    }\r\n  },\r\n  removeItem: (name: string): void => {\r\n    storageSecurity.clearStorage(name);\r\n  },\r\n};\r\n\r\nexport const useUserStore = create<UserState>()((set, get) => ({\r\n  user: null,\r\n  isAuthenticated: false,\r\n  _hasHydrated: false,\r\n\r\n  setUser: (user) => {\r\n    set({ user, isAuthenticated: !!user });\r\n    // Store encrypted data\r\n    if (user) {\r\n      storageSecurity.encryptAndStore(\"user-storage\", {\r\n        state: { user, isAuthenticated: true },\r\n      });\r\n    }\r\n  },\r\n\r\n  clearUser: () => {\r\n    set({ user: null, isAuthenticated: false });\r\n    storageSecurity.clearStorage(\"user-storage\");\r\n  },\r\n\r\n  _setHasHydrated: (hasHydrated) => {\r\n    set({ _hasHydrated: hasHydrated });\r\n  },\r\n\r\n  shouldFetchUser: () => {\r\n    const state = get();\r\n    // If we don't have a user but we've hydrated, we might need to fetch from server\r\n    // This handles the case where user is in server cookie but not in local storage\r\n    return state._hasHydrated && !state.user;\r\n  },\r\n\r\n  refreshUser: async () => {\r\n    try {\r\n      const { getUser, hasValidToken } = await import(\"@/actions/auth\");\r\n\r\n      const hasToken = await hasValidToken();\r\n      if (!hasToken) {\r\n        get().clearUser();\r\n        return;\r\n      }\r\n\r\n      const response = await getUser();\r\n      if (response.data && response.status === 200) {\r\n        get().setUser(response.data);\r\n      } else {\r\n        get().clearUser();\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Failed to refresh user:\", error);\r\n      get().clearUser();\r\n    }\r\n  },\r\n}));\r\n\r\n// Hydrate from encrypted storage on initialization\r\nif (typeof window !== \"undefined\") {\r\n  const storedData = storageSecurity.decryptAndRetrieve(\"user-storage\");\r\n  if (storedData?.state) {\r\n    useUserStore.setState({\r\n      ...storedData.state,\r\n      _hasHydrated: true,\r\n    });\r\n  } else {\r\n    useUserStore.setState({ _hasHydrated: true });\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AA8BA,gDAAgD;AAChD,MAAM,gBAAgB;IACpB,SAAS,CAAC;QACR,MAAM,OAAO,yIAAA,CAAA,kBAAe,CAAC,kBAAkB,CAAC;QAChD,OAAO,OAAO,KAAK,SAAS,CAAC,QAAQ;IACvC;IACA,SAAS,CAAC,MAAc;QACtB,IAAI;YACF,MAAM,OAAO,KAAK,KAAK,CAAC;YACxB,yIAAA,CAAA,kBAAe,CAAC,eAAe,CAAC,MAAM;QACxC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IACA,YAAY,CAAC;QACX,yIAAA,CAAA,kBAAe,CAAC,YAAY,CAAC;IAC/B;AACF;AAEO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAAe,CAAC,KAAK,MAAQ,CAAC;QAC7D,MAAM;QACN,iBAAiB;QACjB,cAAc;QAEd,SAAS,CAAC;YACR,IAAI;gBAAE;gBAAM,iBAAiB,CAAC,CAAC;YAAK;YACpC,uBAAuB;YACvB,IAAI,MAAM;gBACR,yIAAA,CAAA,kBAAe,CAAC,eAAe,CAAC,gBAAgB;oBAC9C,OAAO;wBAAE;wBAAM,iBAAiB;oBAAK;gBACvC;YACF;QACF;QAEA,WAAW;YACT,IAAI;gBAAE,MAAM;gBAAM,iBAAiB;YAAM;YACzC,yIAAA,CAAA,kBAAe,CAAC,YAAY,CAAC;QAC/B;QAEA,iBAAiB,CAAC;YAChB,IAAI;gBAAE,cAAc;YAAY;QAClC;QAEA,iBAAiB;YACf,MAAM,QAAQ;YACd,iFAAiF;YACjF,gFAAgF;YAChF,OAAO,MAAM,YAAY,IAAI,CAAC,MAAM,IAAI;QAC1C;QAEA,aAAa;YACX,IAAI;gBACF,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,GAAG;gBAEnC,MAAM,WAAW,MAAM;gBACvB,IAAI,CAAC,UAAU;oBACb,MAAM,SAAS;oBACf;gBACF;gBAEA,MAAM,WAAW,MAAM;gBACvB,IAAI,SAAS,IAAI,IAAI,SAAS,MAAM,KAAK,KAAK;oBAC5C,MAAM,OAAO,CAAC,SAAS,IAAI;gBAC7B,OAAO;oBACL,MAAM,SAAS;gBACjB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,MAAM,SAAS;YACjB;QACF;IACF,CAAC;AAED,mDAAmD;AACnD,wCAAmC;IACjC,MAAM,aAAa,yIAAA,CAAA,kBAAe,CAAC,kBAAkB,CAAC;IACtD,IAAI,YAAY,OAAO;QACrB,aAAa,QAAQ,CAAC;YACpB,GAAG,WAAW,KAAK;YACnB,cAAc;QAChB;IACF,OAAO;QACL,aAAa,QAAQ,CAAC;YAAE,cAAc;QAAK;IAC7C;AACF", "debugId": null}}, {"offset": {"line": 263, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/store/useHeaderStore.ts"], "sourcesContent": ["import { create } from \"zustand\";\r\n\r\ninterface User {\r\n  id: string;\r\n  name: string;\r\n  isAdmin: boolean;\r\n}\r\n\r\ninterface HeaderState {\r\n  // Mock user data\r\n  isUser: boolean;\r\n  isAdmin: boolean;\r\n  user: User | null;\r\n\r\n  // Modal states\r\n  isLoginModalOpen: boolean;\r\n  isRegisterModalOpen: boolean;\r\n\r\n  // Actions\r\n  setIsUser: (isUser: boolean) => void;\r\n  setIsAdmin: (isAdmin: boolean) => void;\r\n  setUser: (user: User | null) => void;\r\n  openLoginModal: () => void;\r\n  closeLoginModal: () => void;\r\n  openRegisterModal: () => void;\r\n  closeRegisterModal: () => void;\r\n  toggleModal: (modalType: \"login\" | \"register\") => void;\r\n}\r\n\r\nexport const useHeaderStore = create<HeaderState>((set, get) => ({\r\n  // Initial mock state - you can change these for testing\r\n  isUser: false, // Set to true to test logged-in state\r\n  isAdmin: false, // Set to true to test admin state\r\n  user: null,\r\n\r\n  isLoginModalOpen: false,\r\n  isRegisterModalOpen: false,\r\n\r\n  setIsUser: (isUser) => set({ isUser }),\r\n  setIsAdmin: (isAdmin) => set({ isAdmin }),\r\n  setUser: (user) => set({ user }),\r\n\r\n  openLoginModal: () =>\r\n    set({\r\n      isLoginModalOpen: true,\r\n      isRegisterModalOpen: false,\r\n    }),\r\n\r\n  closeLoginModal: () => set({ isLoginModalOpen: false }),\r\n\r\n  openRegisterModal: () =>\r\n    set({\r\n      isRegisterModalOpen: true,\r\n      isLoginModalOpen: false,\r\n    }),\r\n\r\n  closeRegisterModal: () => set({ isRegisterModalOpen: false }),\r\n\r\n  toggleModal: (modalType) => {\r\n    const state = get();\r\n    if (modalType === \"login\") {\r\n      set({\r\n        isLoginModalOpen: !state.isLoginModalOpen,\r\n        isRegisterModalOpen: false,\r\n      });\r\n    } else {\r\n      set({\r\n        isRegisterModalOpen: !state.isRegisterModalOpen,\r\n        isLoginModalOpen: false,\r\n      });\r\n    }\r\n  },\r\n}));\r\n"], "names": [], "mappings": ";;;AAAA;;AA6BO,MAAM,iBAAiB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAe,CAAC,KAAK,MAAQ,CAAC;QAC/D,wDAAwD;QACxD,QAAQ;QACR,SAAS;QACT,MAAM;QAEN,kBAAkB;QAClB,qBAAqB;QAErB,WAAW,CAAC,SAAW,IAAI;gBAAE;YAAO;QACpC,YAAY,CAAC,UAAY,IAAI;gBAAE;YAAQ;QACvC,SAAS,CAAC,OAAS,IAAI;gBAAE;YAAK;QAE9B,gBAAgB,IACd,IAAI;gBACF,kBAAkB;gBAClB,qBAAqB;YACvB;QAEF,iBAAiB,IAAM,IAAI;gBAAE,kBAAkB;YAAM;QAErD,mBAAmB,IACjB,IAAI;gBACF,qBAAqB;gBACrB,kBAAkB;YACpB;QAEF,oBAAoB,IAAM,IAAI;gBAAE,qBAAqB;YAAM;QAE3D,aAAa,CAAC;YACZ,MAAM,QAAQ;YACd,IAAI,cAAc,SAAS;gBACzB,IAAI;oBACF,kBAAkB,CAAC,MAAM,gBAAgB;oBACzC,qBAAqB;gBACvB;YACF,OAAO;gBACL,IAAI;oBACF,qBAAqB,CAAC,MAAM,mBAAmB;oBAC/C,kBAAkB;gBACpB;YACF;QACF;IACF,CAAC", "debugId": null}}, {"offset": {"line": 322, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/actions/auth.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { get, post } from \"@/services/api\";\r\nimport {\r\n  setSecure<PERSON>ookie,\r\n  clearSecure<PERSON><PERSON>ie,\r\n  getSecure<PERSON>ookie,\r\n} from \"@/services/secure-cookies\";\r\nimport { isCountryAllowed } from \"@/constants/auth\";\r\n\r\ninterface RegisterUser {\r\n  name: string;\r\n  email: string;\r\n  password: string;\r\n  phone: string;\r\n  smoker: boolean;\r\n  age: string;\r\n  gender: string;\r\n  nationality: string;\r\n  country: string;\r\n  occupation: string;\r\n}\r\n\r\ninterface LoginUser {\r\n  email: string;\r\n  password: string;\r\n}\r\n\r\nexport const registerUser = async (user: RegisterUser) => {\r\n  console.log(user);\r\n  // Validate country restriction before sending to API\r\n  if (!isCountryAllowed(user.country)) {\r\n    return {\r\n      error:\r\n        \"Registration is only available for Egypt, UAE, Saudi Arabia, and Jordannnn\",\r\n      status: 400,\r\n    };\r\n  }\r\n\r\n  const response = await post(\"/auth/register\", user);\r\n\r\n  console.log(\"response\", response);\r\n\r\n  if (response.data && response.data.access_token) {\r\n    // Store token in secure httpOnly cookie\r\n    await setSecureCookie(\"roommate-finder-token\", response.data.access_token);\r\n  }\r\n\r\n  return response;\r\n};\r\n\r\nexport const loginUser = async (user: LoginUser) => {\r\n  const response = await post(\"/auth/login\", user);\r\n\r\n  if (response.data && response.data.access_token) {\r\n    // Store token in secure httpOnly cookie\r\n    await setSecureCookie(\"roommate-finder-token\", response.data.access_token);\r\n  }\r\n\r\n  return response;\r\n};\r\n\r\nexport const logoutUser = async () => {\r\n  await clearSecureCookie(\"roommate-finder-token\");\r\n  return { success: true };\r\n};\r\n\r\nexport const getUser = async () => {\r\n  const response = await get(\"/users/me\");\r\n  return response;\r\n};\r\n\r\nexport const hasValidToken = async (): Promise<boolean> => {\r\n  const token = await getSecureCookie(\"roommate-finder-token\");\r\n  return !!token;\r\n};\r\n"], "names": [], "mappings": ";;;;;;IAmEa,UAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 338, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/actions/auth.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { get, post } from \"@/services/api\";\r\nimport {\r\n  setSecure<PERSON>ookie,\r\n  clearSecure<PERSON><PERSON>ie,\r\n  getSecure<PERSON>ookie,\r\n} from \"@/services/secure-cookies\";\r\nimport { isCountryAllowed } from \"@/constants/auth\";\r\n\r\ninterface RegisterUser {\r\n  name: string;\r\n  email: string;\r\n  password: string;\r\n  phone: string;\r\n  smoker: boolean;\r\n  age: string;\r\n  gender: string;\r\n  nationality: string;\r\n  country: string;\r\n  occupation: string;\r\n}\r\n\r\ninterface LoginUser {\r\n  email: string;\r\n  password: string;\r\n}\r\n\r\nexport const registerUser = async (user: RegisterUser) => {\r\n  console.log(user);\r\n  // Validate country restriction before sending to API\r\n  if (!isCountryAllowed(user.country)) {\r\n    return {\r\n      error:\r\n        \"Registration is only available for Egypt, UAE, Saudi Arabia, and Jordannnn\",\r\n      status: 400,\r\n    };\r\n  }\r\n\r\n  const response = await post(\"/auth/register\", user);\r\n\r\n  console.log(\"response\", response);\r\n\r\n  if (response.data && response.data.access_token) {\r\n    // Store token in secure httpOnly cookie\r\n    await setSecureCookie(\"roommate-finder-token\", response.data.access_token);\r\n  }\r\n\r\n  return response;\r\n};\r\n\r\nexport const loginUser = async (user: LoginUser) => {\r\n  const response = await post(\"/auth/login\", user);\r\n\r\n  if (response.data && response.data.access_token) {\r\n    // Store token in secure httpOnly cookie\r\n    await setSecureCookie(\"roommate-finder-token\", response.data.access_token);\r\n  }\r\n\r\n  return response;\r\n};\r\n\r\nexport const logoutUser = async () => {\r\n  await clearSecureCookie(\"roommate-finder-token\");\r\n  return { success: true };\r\n};\r\n\r\nexport const getUser = async () => {\r\n  const response = await get(\"/users/me\");\r\n  return response;\r\n};\r\n\r\nexport const hasValidToken = async (): Promise<boolean> => {\r\n  const token = await getSecureCookie(\"roommate-finder-token\");\r\n  return !!token;\r\n};\r\n"], "names": [], "mappings": ";;;;;;IAwEa,gBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 354, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/providers/auth-provider.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect } from \"react\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport { useUserStore } from \"@/store/useUserStore\";\r\nimport { useHeaderStore } from \"@/store/useHeaderStore\";\r\nimport { getUser, hasValidToken } from \"@/actions/auth\";\r\n\r\ninterface AuthProviderProps {\r\n    children: React.ReactNode;\r\n}\r\n\r\nexport function AuthProvider({ children }: AuthProviderProps) {\r\n    const { user, setUser, clearUser, _hasHydrated, shouldFetchUser } = useUserStore();\r\n    const { setUser: setHeaderUser, setIsUser, setIsAdmin } = useHeaderStore();\r\n    const pathname = usePathname();\r\n\r\n    // Function to check and update user authentication\r\n    const checkAuthStatus = async () => {\r\n        try {\r\n            // First check if we have a valid token\r\n            const hasToken = await hasValidToken();\r\n\r\n            if (!hasToken) {\r\n                // No token, clear user data from both stores\r\n                clearUser();\r\n                setHeaderUser(null);\r\n                setIsUser(false);\r\n                setIsAdmin(false);\r\n                return;\r\n            }\r\n\r\n            // If we have a token, try to get user data\r\n            const response = await getUser();\r\n\r\n            if (response.data && response.status === 200) {\r\n                // User is authenticated, save user data to both stores\r\n                setUser(response.data);\r\n\r\n                // Sync to header store as well\r\n                setHeaderUser({\r\n                    id: response.data.id,\r\n                    name: response.data.name,\r\n                    isAdmin: response.data.isAdmin || false\r\n                });\r\n                setIsUser(true);\r\n                setIsAdmin(response.data.isAdmin || false);\r\n            } else {\r\n                // Token is invalid or expired, clear user data from both stores\r\n                clearUser();\r\n                setHeaderUser(null);\r\n                setIsUser(false);\r\n                setIsAdmin(false);\r\n            }\r\n        } catch (error) {\r\n            console.error(\"Auth check failed:\", error);\r\n            // On error, clear user data from both stores\r\n            clearUser();\r\n            setHeaderUser(null);\r\n            setIsUser(false);\r\n            setIsAdmin(false);\r\n        }\r\n    };\r\n\r\n    // Force auth check on mount (always runs once)\r\n    useEffect(() => {\r\n        checkAuthStatus();\r\n    }, []);\r\n\r\n    // Check auth on initial load\r\n    useEffect(() => {\r\n        if (_hasHydrated) {\r\n            // If we don't have user data but store is hydrated, check with server\r\n            if (shouldFetchUser()) {\r\n                checkAuthStatus();\r\n            } else {\r\n            }\r\n        }\r\n    }, [_hasHydrated]);\r\n\r\n    // Check auth on route changes (navigation)\r\n    useEffect(() => {\r\n        if (_hasHydrated) {\r\n            // Get fresh state to avoid stale closure\r\n            const currentState = useUserStore.getState();\r\n            // Only check if we don't already have user data or if we should fetch\r\n            if (!currentState.user || shouldFetchUser()) {\r\n                checkAuthStatus();\r\n            }\r\n        }\r\n    }, [pathname, _hasHydrated]);\r\n\r\n    return <>{children}</>;\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;;;AANA;;;;;;AAYO,SAAS,aAAa,EAAE,QAAQ,EAAqB;;IACxD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,YAAY,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAC/E,MAAM,EAAE,SAAS,aAAa,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IACvE,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,mDAAmD;IACnD,MAAM,kBAAkB;QACpB,IAAI;YACA,uCAAuC;YACvC,MAAM,WAAW,MAAM,CAAA,GAAA,yJAAA,CAAA,gBAAa,AAAD;YAEnC,IAAI,CAAC,UAAU;gBACX,6CAA6C;gBAC7C;gBACA,cAAc;gBACd,UAAU;gBACV,WAAW;gBACX;YACJ;YAEA,2CAA2C;YAC3C,MAAM,WAAW,MAAM,CAAA,GAAA,yJAAA,CAAA,UAAO,AAAD;YAE7B,IAAI,SAAS,IAAI,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC1C,uDAAuD;gBACvD,QAAQ,SAAS,IAAI;gBAErB,+BAA+B;gBAC/B,cAAc;oBACV,IAAI,SAAS,IAAI,CAAC,EAAE;oBACpB,MAAM,SAAS,IAAI,CAAC,IAAI;oBACxB,SAAS,SAAS,IAAI,CAAC,OAAO,IAAI;gBACtC;gBACA,UAAU;gBACV,WAAW,SAAS,IAAI,CAAC,OAAO,IAAI;YACxC,OAAO;gBACH,gEAAgE;gBAChE;gBACA,cAAc;gBACd,UAAU;gBACV,WAAW;YACf;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,sBAAsB;YACpC,6CAA6C;YAC7C;YACA,cAAc;YACd,UAAU;YACV,WAAW;QACf;IACJ;IAEA,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACN;QACJ;iCAAG,EAAE;IAEL,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACN,IAAI,cAAc;gBACd,sEAAsE;gBACtE,IAAI,mBAAmB;oBACnB;gBACJ,OAAO,CACP;YACJ;QACJ;iCAAG;QAAC;KAAa;IAEjB,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACN,IAAI,cAAc;gBACd,yCAAyC;gBACzC,MAAM,eAAe,+HAAA,CAAA,eAAY,CAAC,QAAQ;gBAC1C,sEAAsE;gBACtE,IAAI,CAAC,aAAa,IAAI,IAAI,mBAAmB;oBACzC;gBACJ;YACJ;QACJ;iCAAG;QAAC;QAAU;KAAa;IAE3B,qBAAO;kBAAG;;AACd;GAjFgB;;QACwD,+HAAA,CAAA,eAAY;QACtB,iIAAA,CAAA,iBAAc;QACvD,qIAAA,CAAA,cAAW;;;KAHhB", "debugId": null}}, {"offset": {"line": 477, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/actions/auth.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { get, post } from \"@/services/api\";\r\nimport {\r\n  setSecure<PERSON>ookie,\r\n  clearSecure<PERSON><PERSON>ie,\r\n  getSecure<PERSON>ookie,\r\n} from \"@/services/secure-cookies\";\r\nimport { isCountryAllowed } from \"@/constants/auth\";\r\n\r\ninterface RegisterUser {\r\n  name: string;\r\n  email: string;\r\n  password: string;\r\n  phone: string;\r\n  smoker: boolean;\r\n  age: string;\r\n  gender: string;\r\n  nationality: string;\r\n  country: string;\r\n  occupation: string;\r\n}\r\n\r\ninterface LoginUser {\r\n  email: string;\r\n  password: string;\r\n}\r\n\r\nexport const registerUser = async (user: RegisterUser) => {\r\n  console.log(user);\r\n  // Validate country restriction before sending to API\r\n  if (!isCountryAllowed(user.country)) {\r\n    return {\r\n      error:\r\n        \"Registration is only available for Egypt, UAE, Saudi Arabia, and Jordannnn\",\r\n      status: 400,\r\n    };\r\n  }\r\n\r\n  const response = await post(\"/auth/register\", user);\r\n\r\n  console.log(\"response\", response);\r\n\r\n  if (response.data && response.data.access_token) {\r\n    // Store token in secure httpOnly cookie\r\n    await setSecureCookie(\"roommate-finder-token\", response.data.access_token);\r\n  }\r\n\r\n  return response;\r\n};\r\n\r\nexport const loginUser = async (user: LoginUser) => {\r\n  const response = await post(\"/auth/login\", user);\r\n\r\n  if (response.data && response.data.access_token) {\r\n    // Store token in secure httpOnly cookie\r\n    await setSecureCookie(\"roommate-finder-token\", response.data.access_token);\r\n  }\r\n\r\n  return response;\r\n};\r\n\r\nexport const logoutUser = async () => {\r\n  await clearSecureCookie(\"roommate-finder-token\");\r\n  return { success: true };\r\n};\r\n\r\nexport const getUser = async () => {\r\n  const response = await get(\"/users/me\");\r\n  return response;\r\n};\r\n\r\nexport const hasValidToken = async (): Promise<boolean> => {\r\n  const token = await getSecureCookie(\"roommate-finder-token\");\r\n  return !!token;\r\n};\r\n"], "names": [], "mappings": ";;;;;;IA8Da,aAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 493, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\nimport { useUserStore } from \"@/store/useUserStore\";\r\nimport { useHeaderStore } from \"@/store/useHeaderStore\";\r\nimport { logoutUser } from \"@/actions/auth\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport const handleLogout = async () => {\r\n  try {\r\n    // Call server action to clear cookie\r\n    await logoutUser();\r\n\r\n    // Clear user store\r\n    const { clearUser } = useUserStore.getState();\r\n    clearUser();\r\n\r\n    // Clear header store\r\n    const { setIsUser, setIsAdmin, setUser } = useHeaderStore.getState();\r\n    setIsUser(false);\r\n    setIsAdmin(false);\r\n    setUser(null);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Logout error:\", error);\r\n    return { success: false, error };\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,eAAe;IAC1B,IAAI;QACF,qCAAqC;QACrC,MAAM,CAAA,GAAA,yJAAA,CAAA,aAAU,AAAD;QAEf,mBAAmB;QACnB,MAAM,EAAE,SAAS,EAAE,GAAG,+HAAA,CAAA,eAAY,CAAC,QAAQ;QAC3C;QAEA,qBAAqB;QACrB,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,iIAAA,CAAA,iBAAc,CAAC,QAAQ;QAClE,UAAU;QACV,WAAW;QACX,QAAQ;QAER,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF", "debugId": null}}, {"offset": {"line": 542, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/header/logo.tsx"], "sourcesContent": ["import Image from 'next/image'\r\nimport { cn } from '@/lib/utils'\r\n\r\ninterface LogoProps {\r\n    className?: string\r\n    onClick?: () => void\r\n}\r\n\r\nexport function Logo({ className, onClick }: LogoProps) {\r\n    return (\r\n        <div\r\n            className={cn(\r\n                \"flex items-center cursor-pointer transition-opacity hover:opacity-80\",\r\n                className\r\n            )}\r\n            onClick={onClick}\r\n        >\r\n            <Image\r\n                src=\"/logo.png\"\r\n                alt=\"Logo\"\r\n                width={100}\r\n                height={100}\r\n                className=\"h-16 w-auto\"\r\n                priority\r\n            />\r\n        </div>\r\n    )\r\n} "], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOO,SAAS,KAAK,EAAE,SAAS,EAAE,OAAO,EAAa;IAClD,qBACI,6LAAC;QACG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,wEACA;QAEJ,SAAS;kBAET,cAAA,6LAAC,gIAAA,CAAA,UAAK;YACF,KAAI;YACJ,KAAI;YACJ,OAAO;YACP,QAAQ;YACR,WAAU;YACV,QAAQ;;;;;;;;;;;AAIxB;KAnBgB", "debugId": null}}, {"offset": {"line": 585, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 617, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/header/search-bar.tsx"], "sourcesContent": ["import { Search } from 'lucide-react'\r\nimport { Input } from '@/components/ui/input'\r\nimport { cn } from '@/lib/utils'\r\nimport { useTranslations } from 'next-intl'\r\n\r\ninterface SearchBarProps {\r\n    className?: string\r\n    isMobile?: boolean\r\n}\r\n\r\nexport function SearchBar({ className, isMobile = false }: SearchBarProps) {\r\n    const t = useTranslations('header.search')\r\n\r\n    const placeholder = isMobile ? t('mobile_placeholder') : t('placeholder')\r\n\r\n    return (\r\n        <div className={cn(\"relative flex-1 max-w-md\", className)}>\r\n            <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\" />\r\n            <Input\r\n                type=\"search\"\r\n                placeholder={placeholder}\r\n                className=\"pl-10 pr-4\"\r\n            />\r\n        </div>\r\n    )\r\n} "], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;;AAOO,SAAS,UAAU,EAAE,SAAS,EAAE,WAAW,KAAK,EAAkB;;IACrE,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,MAAM,cAAc,WAAW,EAAE,wBAAwB,EAAE;IAE3D,qBACI,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;;0BAC3C,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;0BAClB,6LAAC,oIAAA,CAAA,QAAK;gBACF,MAAK;gBACL,aAAa;gBACb,WAAU;;;;;;;;;;;;AAI1B;GAfgB;;QACF,yMAAA,CAAA,kBAAe;;;KADb", "debugId": null}}, {"offset": {"line": 678, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex cursor-pointer items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-white dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,8cACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 741, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { useLocale } from \"next-intl\"\r\n\r\nfunction Dialog({\r\n    ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n    return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\r\n}\r\n\r\nfunction DialogTrigger({\r\n    ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n    return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\r\n}\r\n\r\nfunction DialogPortal({\r\n    ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n    return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\r\n}\r\n\r\nfunction DialogClose({\r\n    ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n    return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\r\n}\r\n\r\nfunction DialogOverlay({\r\n    className,\r\n    ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n    return (\r\n        <DialogPrimitive.Overlay\r\n            data-slot=\"dialog-overlay\"\r\n            className={cn(\r\n                \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n                className\r\n            )}\r\n            {...props}\r\n        />\r\n    )\r\n}\r\n\r\nfunction DialogContent({\r\n    className,\r\n    children,\r\n    showCloseButton = true,\r\n    ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\r\n    showCloseButton?: boolean\r\n}) {\r\n    const locale = useLocale()\r\n    return (\r\n        <DialogPortal data-slot=\"dialog-portal\">\r\n            <DialogOverlay />\r\n            <DialogPrimitive.Content\r\n                data-slot=\"dialog-content\"\r\n                className={cn(\r\n                    \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n                    className\r\n                )}\r\n                {...props}\r\n            >\r\n                {children}\r\n                {showCloseButton && (\r\n                    <DialogPrimitive.Close\r\n                        data-slot=\"dialog-close\"\r\n                        className={`ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 ${locale === 'ar' ? 'left-4' : 'right-4'} rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4`}\r\n                    >\r\n                        <XIcon />\r\n                        <span className=\"sr-only\">Close</span>\r\n                    </DialogPrimitive.Close>\r\n                )}\r\n            </DialogPrimitive.Content>\r\n        </DialogPortal>\r\n    )\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n    return (\r\n        <div\r\n            data-slot=\"dialog-header\"\r\n            className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n            {...props}\r\n        />\r\n    )\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n    return (\r\n        <div\r\n            data-slot=\"dialog-footer\"\r\n            className={cn(\r\n                \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n                className\r\n            )}\r\n            {...props}\r\n        />\r\n    )\r\n}\r\n\r\nfunction DialogTitle({\r\n    className,\r\n    ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n    return (\r\n        <DialogPrimitive.Title\r\n            data-slot=\"dialog-title\"\r\n            className={cn(\"text-lg leading-none font-semibold\", className)}\r\n            {...props}\r\n        />\r\n    )\r\n}\r\n\r\nfunction DialogDescription({\r\n    className,\r\n    ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n    return (\r\n        <DialogPrimitive.Description\r\n            data-slot=\"dialog-description\"\r\n            className={cn(\"text-muted-foreground text-sm\", className)}\r\n            {...props}\r\n        />\r\n    )\r\n}\r\n\r\nexport {\r\n    Dialog,\r\n    DialogClose,\r\n    DialogContent,\r\n    DialogDescription,\r\n    DialogFooter,\r\n    DialogHeader,\r\n    DialogOverlay,\r\n    DialogPortal,\r\n    DialogTitle,\r\n    DialogTrigger,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AACA;;;AAPA;;;;;AASA,SAAS,OAAO,EACZ,GAAG,OAC6C;IAChD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC7D;KAJS;AAMT,SAAS,cAAc,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,aAAa,EAClB,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,YAAY,EACjB,GAAG,OAC8C;IACjD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,cAAc,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACI,6LAAC,qKAAA,CAAA,UAAuB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,0JACA;QAEH,GAAG,KAAK;;;;;;AAGrB;MAdS;AAgBT,SAAS,cAAc,EACnB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGN;;IACG,MAAM,SAAS,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD;IACvB,qBACI,6LAAC;QAAa,aAAU;;0BACpB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACpB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,+WACA;gBAEH,GAAG,KAAK;;oBAER;oBACA,iCACG,6LAAC,qKAAA,CAAA,QAAqB;wBAClB,aAAU;wBACV,WAAW,CAAC,0HAA0H,EAAE,WAAW,OAAO,WAAW,UAAU,gOAAgO,CAAC;;0CAEhZ,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMlD;GAjCS;;QAQU,qKAAA,CAAA,YAAS;;;MARnB;AAmCT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACI,6LAAC;QACG,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGrB;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACI,6LAAC;QACG,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,0DACA;QAEH,GAAG,KAAK;;;;;;AAGrB;MAXS;AAaT,SAAS,YAAY,EACjB,SAAS,EACT,GAAG,OAC8C;IACjD,qBACI,6LAAC,qKAAA,CAAA,QAAqB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGrB;MAXS;AAaT,SAAS,kBAAkB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACI,6LAAC,qKAAA,CAAA,cAA2B;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGrB;MAXS", "debugId": null}}, {"offset": {"line": 949, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/actions/auth.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { get, post } from \"@/services/api\";\r\nimport {\r\n  setSecure<PERSON>ookie,\r\n  clearSecure<PERSON><PERSON>ie,\r\n  getSecure<PERSON>ookie,\r\n} from \"@/services/secure-cookies\";\r\nimport { isCountryAllowed } from \"@/constants/auth\";\r\n\r\ninterface RegisterUser {\r\n  name: string;\r\n  email: string;\r\n  password: string;\r\n  phone: string;\r\n  smoker: boolean;\r\n  age: string;\r\n  gender: string;\r\n  nationality: string;\r\n  country: string;\r\n  occupation: string;\r\n}\r\n\r\ninterface LoginUser {\r\n  email: string;\r\n  password: string;\r\n}\r\n\r\nexport const registerUser = async (user: RegisterUser) => {\r\n  console.log(user);\r\n  // Validate country restriction before sending to API\r\n  if (!isCountryAllowed(user.country)) {\r\n    return {\r\n      error:\r\n        \"Registration is only available for Egypt, UAE, Saudi Arabia, and Jordannnn\",\r\n      status: 400,\r\n    };\r\n  }\r\n\r\n  const response = await post(\"/auth/register\", user);\r\n\r\n  console.log(\"response\", response);\r\n\r\n  if (response.data && response.data.access_token) {\r\n    // Store token in secure httpOnly cookie\r\n    await setSecureCookie(\"roommate-finder-token\", response.data.access_token);\r\n  }\r\n\r\n  return response;\r\n};\r\n\r\nexport const loginUser = async (user: LoginUser) => {\r\n  const response = await post(\"/auth/login\", user);\r\n\r\n  if (response.data && response.data.access_token) {\r\n    // Store token in secure httpOnly cookie\r\n    await setSecureCookie(\"roommate-finder-token\", response.data.access_token);\r\n  }\r\n\r\n  return response;\r\n};\r\n\r\nexport const logoutUser = async () => {\r\n  await clearSecureCookie(\"roommate-finder-token\");\r\n  return { success: true };\r\n};\r\n\r\nexport const getUser = async () => {\r\n  const response = await get(\"/users/me\");\r\n  return response;\r\n};\r\n\r\nexport const hasValidToken = async (): Promise<boolean> => {\r\n  const token = await getSecureCookie(\"roommate-finder-token\");\r\n  return !!token;\r\n};\r\n"], "names": [], "mappings": ";;;;;;IAmDa,YAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 965, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/constants/auth.ts"], "sourcesContent": ["// Allowed countries for registration\r\nexport const ALLOWED_COUNTRIES = [\r\n  {\r\n    name_en: \"Egypt\",\r\n    name_ar: \"مصر\",\r\n    code: \"EG\",\r\n  },\r\n  {\r\n    name_en: \"UAE\",\r\n    name_ar: \"الإمارة العربية المتحدة\",\r\n    code: \"AE\",\r\n  },\r\n  {\r\n    name_en: \"Saudi Arabia\",\r\n    name_ar: \"المملكة العربية السعودية\",\r\n    code: \"SA\",\r\n  },\r\n  {\r\n    name_en: \"Jordan\",\r\n    name_ar: \"الأردن\",\r\n    code: \"JO\",\r\n  },\r\n] as const;\r\n\r\nexport type AllowedCountry = (typeof ALLOWED_COUNTRIES)[number];\r\n\r\n// Country validation function\r\nexport const isCountryAllowed = (\r\n  country: string\r\n): country is AllowedCountry[\"code\"] => {\r\n  return ALLOWED_COUNTRIES.some((c) => c.name_en === country) || false;\r\n};\r\n\r\n// Registration step validation\r\nexport const REGISTRATION_STEPS = {\r\n  PERSONAL_INFO: 1,\r\n  CONTACT_INFO: 2,\r\n  ADDITIONAL_INFO: 3,\r\n  VERIFICATION: 4,\r\n} as const;\r\n\r\n// Form validation patterns\r\nexport const VALIDATION_PATTERNS = {\r\n  email: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\r\n  password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$/,\r\n} as const;\r\n\r\n// Gender options\r\nexport const GENDER_OPTIONS = [\r\n  { gender: \"Male\", gender_ar: \"ذكر\" },\r\n  { gender: \"Female\", gender_ar: \"أنثى\" },\r\n] as const;\r\n"], "names": [], "mappings": "AAAA,qCAAqC;;;;;;;;AAC9B,MAAM,oBAAoB;IAC/B;QACE,SAAS;QACT,SAAS;QACT,MAAM;IACR;IACA;QACE,SAAS;QACT,SAAS;QACT,MAAM;IACR;IACA;QACE,SAAS;QACT,SAAS;QACT,MAAM;IACR;IACA;QACE,SAAS;QACT,SAAS;QACT,MAAM;IACR;CACD;AAKM,MAAM,mBAAmB,CAC9B;IAEA,OAAO,kBAAkB,IAAI,CAAC,CAAC,IAAM,EAAE,OAAO,KAAK,YAAY;AACjE;AAGO,MAAM,qBAAqB;IAChC,eAAe;IACf,cAAc;IACd,iBAAiB;IACjB,cAAc;AAChB;AAGO,MAAM,sBAAsB;IACjC,OAAO;IACP,UAAU;AACZ;AAGO,MAAM,iBAAiB;IAC5B;QAAE,QAAQ;QAAQ,WAAW;IAAM;IACnC;QAAE,QAAQ;QAAU,WAAW;IAAO;CACvC", "debugId": null}}, {"offset": {"line": 1027, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Label({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 1061, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/auth/login-form.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { useTranslations } from 'next-intl'\r\nimport { LogIn, Eye, EyeOff } from 'lucide-react'\r\nimport { Button } from '@/components/ui/button'\r\nimport { Input } from '@/components/ui/input'\r\nimport { loginUser } from '@/actions/auth'\r\nimport { useUserStore } from '@/store/useUserStore'\r\nimport { useHeaderStore } from '@/store/useHeaderStore'\r\nimport { VALIDATION_PATTERNS } from '@/constants/auth'\r\nimport { Label } from '../ui/label'\r\n\r\ninterface LoginFormProps {\r\n    onSuccess?: () => void\r\n    onSwitchToRegister?: () => void\r\n}\r\n\r\ninterface FormData {\r\n    email: string\r\n    password: string\r\n}\r\n\r\ninterface FormErrors {\r\n    email?: string\r\n    password?: string\r\n    general?: string\r\n}\r\n\r\nexport function LoginForm({ onSuccess, onSwitchToRegister }: LoginFormProps) {\r\n    const [formData, setFormData] = useState<FormData>({\r\n        email: '',\r\n        password: ''\r\n    })\r\n    const [errors, setErrors] = useState<FormErrors>({})\r\n    const [isLoading, setIsLoading] = useState(false)\r\n    const [showPassword, setShowPassword] = useState(false)\r\n\r\n    const { setUser } = useUserStore()\r\n    const { setIsUser, setIsAdmin, setUser: setHeaderUser } = useHeaderStore()\r\n    const t = useTranslations('header.modals.login')\r\n\r\n    const validateForm = (): boolean => {\r\n        const newErrors: FormErrors = {}\r\n\r\n        // Email validation\r\n        if (!formData.email) {\r\n            newErrors.email = t('errors.email_required')\r\n        } else if (!VALIDATION_PATTERNS.email.test(formData.email)) {\r\n            newErrors.email = t('errors.email_invalid')\r\n        }\r\n\r\n        // Password validation\r\n        if (!formData.password) {\r\n            newErrors.password = t('errors.password_required')\r\n        }\r\n\r\n        setErrors(newErrors)\r\n        return Object.keys(newErrors).length === 0\r\n    }\r\n\r\n    const handleSubmit = async (e: React.FormEvent) => {\r\n        e.preventDefault()\r\n\r\n        if (!validateForm()) return\r\n\r\n        setIsLoading(true)\r\n        setErrors({})\r\n\r\n        try {\r\n            const response = await loginUser({\r\n                email: formData.email,\r\n                password: formData.password\r\n            })\r\n\r\n            if (response.data && !response.error) {\r\n                // Set user in store\r\n                setUser(response.data.user)\r\n\r\n                // Sync header store\r\n                setIsUser(true)\r\n                setIsAdmin(response.data.user.isAdmin)\r\n                setHeaderUser({\r\n                    id: response.data.user.id,\r\n                    name: response.data.user.name,\r\n                    isAdmin: response.data.user.isAdmin\r\n                })\r\n\r\n                onSuccess?.()\r\n            } else {\r\n                setErrors({ general: response.error || t('errors.invalid_credentials') })\r\n            }\r\n        } catch (error) {\r\n            console.error('Login error:', error)\r\n            setErrors({ general: t('errors.invalid_credentials') })\r\n        } finally {\r\n            setIsLoading(false)\r\n        }\r\n    }\r\n\r\n    const handleInputChange = (field: keyof FormData) => (e: React.ChangeEvent<HTMLInputElement>) => {\r\n        setFormData(prev => ({ ...prev, [field]: e.target.value }))\r\n        // Clear field error when user starts typing\r\n        if (errors[field]) {\r\n            setErrors(prev => ({ ...prev, [field]: undefined }))\r\n        }\r\n    }\r\n\r\n    return (\r\n        <form onSubmit={handleSubmit} className=\"space-y-3\">\r\n            {errors.general && (\r\n                <div className=\"p-3 text-sm text-red-600 bg-red-50 rounded-md border border-red-200\">\r\n                    {errors.general}\r\n                </div>\r\n            )}\r\n\r\n            <div className=\"space-y-3\">\r\n                <Label htmlFor=\"email\">\r\n                    {t('email')}\r\n                </Label>\r\n                <Input\r\n                    id=\"email\"\r\n                    type=\"email\"\r\n                    value={formData.email}\r\n                    onChange={handleInputChange('email')}\r\n                    placeholder={t('email')}\r\n                    className={errors.email ? 'border-red-500' : ''}\r\n                    disabled={isLoading}\r\n                />\r\n                {errors.email && (\r\n                    <p className=\"text-sm text-red-600\">{errors.email}</p>\r\n                )}\r\n            </div>\r\n\r\n            <div className=\"space-y-3\">\r\n                <Label htmlFor=\"password\">\r\n                    {t('password')}\r\n                </Label>\r\n                <div className=\"relative\">\r\n                    <Input\r\n                        id=\"password\"\r\n                        type={showPassword ? 'text' : 'password'}\r\n                        value={formData.password}\r\n                        onChange={handleInputChange('password')}\r\n                        placeholder={t('password')}\r\n                        className={errors.password ? 'border-red-500 pr-10' : 'pr-10'}\r\n                        disabled={isLoading}\r\n                    />\r\n                    <button\r\n                        type=\"button\"\r\n                        onClick={() => setShowPassword(!showPassword)}\r\n                        className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none\"\r\n                        disabled={isLoading}\r\n                    >\r\n                        {showPassword ? (\r\n                            <EyeOff className=\"h-4 w-4\" />\r\n                        ) : (\r\n                            <Eye className=\"h-4 w-4\" />\r\n                        )}\r\n                    </button>\r\n                </div>\r\n                {errors.password && (\r\n                    <p className=\"text-sm text-red-600\">{errors.password}</p>\r\n                )}\r\n            </div>\r\n\r\n            <Button\r\n                type=\"submit\"\r\n                className=\"w-full mt-2\"\r\n                disabled={isLoading}\r\n            >\r\n                {isLoading ? (\r\n                    <>\r\n                        <LogIn className=\"mr-2 h-4 w-4 animate-spin\" />\r\n                        {t('loading')}\r\n                    </>\r\n                ) : (\r\n                    <>\r\n                        <LogIn className=\"mr-2 h-4 w-4\" />\r\n                        {t('login_button')}\r\n                    </>\r\n                )}\r\n            </Button>\r\n\r\n            <div className=\"flex items-center justify-center pt-2 mt-2 border-t space-y-2\">\r\n                <p className=\"text-sm text-muted-foreground text-center m-0\">\r\n                    {t('no_account')}\r\n                </p>\r\n                <Button\r\n                    type=\"button\"\r\n                    variant=\"link\"\r\n                    onClick={onSwitchToRegister}\r\n                    className=\"p-0 m-0 h-auto text-primary underline\"\r\n                    disabled={isLoading}\r\n                >\r\n                    {t('register_link')}\r\n                </Button>\r\n            </div>\r\n        </form>\r\n    )\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;AA6BO,SAAS,UAAU,EAAE,SAAS,EAAE,kBAAkB,EAAkB;;IACvE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QAC/C,OAAO;QACP,UAAU;IACd;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,CAAC;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAC/B,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,aAAa,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IACvE,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,MAAM,eAAe;QACjB,MAAM,YAAwB,CAAC;QAE/B,mBAAmB;QACnB,IAAI,CAAC,SAAS,KAAK,EAAE;YACjB,UAAU,KAAK,GAAG,EAAE;QACxB,OAAO,IAAI,CAAC,2HAAA,CAAA,sBAAmB,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,KAAK,GAAG;YACxD,UAAU,KAAK,GAAG,EAAE;QACxB;QAEA,sBAAsB;QACtB,IAAI,CAAC,SAAS,QAAQ,EAAE;YACpB,UAAU,QAAQ,GAAG,EAAE;QAC3B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC7C;IAEA,MAAM,eAAe,OAAO;QACxB,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;QAErB,aAAa;QACb,UAAU,CAAC;QAEX,IAAI;YACA,MAAM,WAAW,MAAM,CAAA,GAAA,yJAAA,CAAA,YAAS,AAAD,EAAE;gBAC7B,OAAO,SAAS,KAAK;gBACrB,UAAU,SAAS,QAAQ;YAC/B;YAEA,IAAI,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,EAAE;gBAClC,oBAAoB;gBACpB,QAAQ,SAAS,IAAI,CAAC,IAAI;gBAE1B,oBAAoB;gBACpB,UAAU;gBACV,WAAW,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO;gBACrC,cAAc;oBACV,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,EAAE;oBACzB,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI;oBAC7B,SAAS,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO;gBACvC;gBAEA;YACJ,OAAO;gBACH,UAAU;oBAAE,SAAS,SAAS,KAAK,IAAI,EAAE;gBAA8B;YAC3E;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,UAAU;gBAAE,SAAS,EAAE;YAA8B;QACzD,SAAU;YACN,aAAa;QACjB;IACJ;IAEA,MAAM,oBAAoB,CAAC,QAA0B,CAAC;YAClD,YAAY,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE,EAAE,MAAM,CAAC,KAAK;gBAAC,CAAC;YACzD,4CAA4C;YAC5C,IAAI,MAAM,CAAC,MAAM,EAAE;gBACf,UAAU,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,CAAC,MAAM,EAAE;oBAAU,CAAC;YACtD;QACJ;IAEA,qBACI,6LAAC;QAAK,UAAU;QAAc,WAAU;;YACnC,OAAO,OAAO,kBACX,6LAAC;gBAAI,WAAU;0BACV,OAAO,OAAO;;;;;;0BAIvB,6LAAC;gBAAI,WAAU;;kCACX,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;kCACV,EAAE;;;;;;kCAEP,6LAAC,oIAAA,CAAA,QAAK;wBACF,IAAG;wBACH,MAAK;wBACL,OAAO,SAAS,KAAK;wBACrB,UAAU,kBAAkB;wBAC5B,aAAa,EAAE;wBACf,WAAW,OAAO,KAAK,GAAG,mBAAmB;wBAC7C,UAAU;;;;;;oBAEb,OAAO,KAAK,kBACT,6LAAC;wBAAE,WAAU;kCAAwB,OAAO,KAAK;;;;;;;;;;;;0BAIzD,6LAAC;gBAAI,WAAU;;kCACX,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;kCACV,EAAE;;;;;;kCAEP,6LAAC;wBAAI,WAAU;;0CACX,6LAAC,oIAAA,CAAA,QAAK;gCACF,IAAG;gCACH,MAAM,eAAe,SAAS;gCAC9B,OAAO,SAAS,QAAQ;gCACxB,UAAU,kBAAkB;gCAC5B,aAAa,EAAE;gCACf,WAAW,OAAO,QAAQ,GAAG,yBAAyB;gCACtD,UAAU;;;;;;0CAEd,6LAAC;gCACG,MAAK;gCACL,SAAS,IAAM,gBAAgB,CAAC;gCAChC,WAAU;gCACV,UAAU;0CAET,6BACG,6LAAC,6MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;yDAElB,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAI1B,OAAO,QAAQ,kBACZ,6LAAC;wBAAE,WAAU;kCAAwB,OAAO,QAAQ;;;;;;;;;;;;0BAI5D,6LAAC,qIAAA,CAAA,SAAM;gBACH,MAAK;gBACL,WAAU;gBACV,UAAU;0BAET,0BACG;;sCACI,6LAAC,2MAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBAChB,EAAE;;iDAGP;;sCACI,6LAAC,2MAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBAChB,EAAE;;;;;;;;0BAKf,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAE,WAAU;kCACR,EAAE;;;;;;kCAEP,6LAAC,qIAAA,CAAA,SAAM;wBACH,MAAK;wBACL,SAAQ;wBACR,SAAS;wBACT,WAAU;wBACV,UAAU;kCAET,EAAE;;;;;;;;;;;;;;;;;;AAKvB;GA3KgB;;QASQ,+HAAA,CAAA,eAAY;QAC0B,iIAAA,CAAA,iBAAc;QAC9D,yMAAA,CAAA,kBAAe;;;KAXb", "debugId": null}}, {"offset": {"line": 1373, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { useLocale } from \"next-intl\"\r\n\r\nfunction Select({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\r\n}\r\n\r\nfunction SelectGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\r\n}\r\n\r\nfunction SelectValue({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  size = \"default\",\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\r\n  size?: \"sm\" | \"default\"\r\n}) {\r\n  const locale = useLocale()\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      dir={locale === \"ar\" ? \"rtl\" : \"ltr\"}\r\n      data-slot=\"select-trigger\"\r\n      data-size={size}\r\n      className={cn(\r\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-full items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  )\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = \"popper\",\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  const locale = useLocale()\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        dir={locale === \"ar\" ? \"rtl\" : \"ltr\"}\r\n        data-slot=\"select-content\"\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\r\n          position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n          className\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            \"p-1\",\r\n            position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction SelectLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot=\"select-label\"\r\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot=\"select-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  )\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot=\"select-separator\"\r\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot=\"select-scroll-up-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  )\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot=\"select-scroll-down-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  )\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AACA;;;AAPA;;;;;AASA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;;IACC,MAAM,SAAS,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD;IACvB,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK,WAAW,OAAO,QAAQ;QAC/B,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,izBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;GA1BS;;QAQQ,qKAAA,CAAA,YAAS;;;MARjB;AA4BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;;IACrD,MAAM,SAAS,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD;IACvB,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,KAAK,WAAW,OAAO,QAAQ;YAC/B,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACb,mIACA;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACb;8BAGD;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;IAnCS;;QAMQ,qKAAA,CAAA,YAAS;;;MANjB;AAqCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 1641, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/actions/auth.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { get, post } from \"@/services/api\";\r\nimport {\r\n  setSecure<PERSON>ookie,\r\n  clearSecure<PERSON><PERSON>ie,\r\n  getSecure<PERSON>ookie,\r\n} from \"@/services/secure-cookies\";\r\nimport { isCountryAllowed } from \"@/constants/auth\";\r\n\r\ninterface RegisterUser {\r\n  name: string;\r\n  email: string;\r\n  password: string;\r\n  phone: string;\r\n  smoker: boolean;\r\n  age: string;\r\n  gender: string;\r\n  nationality: string;\r\n  country: string;\r\n  occupation: string;\r\n}\r\n\r\ninterface LoginUser {\r\n  email: string;\r\n  password: string;\r\n}\r\n\r\nexport const registerUser = async (user: RegisterUser) => {\r\n  console.log(user);\r\n  // Validate country restriction before sending to API\r\n  if (!isCountryAllowed(user.country)) {\r\n    return {\r\n      error:\r\n        \"Registration is only available for Egypt, UAE, Saudi Arabia, and Jordannnn\",\r\n      status: 400,\r\n    };\r\n  }\r\n\r\n  const response = await post(\"/auth/register\", user);\r\n\r\n  console.log(\"response\", response);\r\n\r\n  if (response.data && response.data.access_token) {\r\n    // Store token in secure httpOnly cookie\r\n    await setSecureCookie(\"roommate-finder-token\", response.data.access_token);\r\n  }\r\n\r\n  return response;\r\n};\r\n\r\nexport const loginUser = async (user: LoginUser) => {\r\n  const response = await post(\"/auth/login\", user);\r\n\r\n  if (response.data && response.data.access_token) {\r\n    // Store token in secure httpOnly cookie\r\n    await setSecureCookie(\"roommate-finder-token\", response.data.access_token);\r\n  }\r\n\r\n  return response;\r\n};\r\n\r\nexport const logoutUser = async () => {\r\n  await clearSecureCookie(\"roommate-finder-token\");\r\n  return { success: true };\r\n};\r\n\r\nexport const getUser = async () => {\r\n  const response = await get(\"/users/me\");\r\n  return response;\r\n};\r\n\r\nexport const hasValidToken = async (): Promise<boolean> => {\r\n  const token = await getSecureCookie(\"roommate-finder-token\");\r\n  return !!token;\r\n};\r\n"], "names": [], "mappings": ";;;;;;IA4Ba,eAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1657, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/auth/register-form.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\nimport { UserPlus, ArrowLeft, ArrowRight, Eye, EyeOff } from 'lucide-react'\r\nimport { Button } from '@/components/ui/button'\r\nimport { Input } from '@/components/ui/input'\r\nimport { Label } from '@/components/ui/label'\r\nimport {\r\n    Select,\r\n    SelectContent,\r\n    SelectItem,\r\n    SelectTrigger,\r\n    SelectValue\r\n} from '@/components/ui/select'\r\nimport { registerUser } from '@/actions/auth'\r\nimport { useUserStore } from '@/store/useUserStore'\r\nimport { useHeaderStore } from '@/store/useHeaderStore'\r\nimport {\r\n    ALLOWED_COUNTRIES,\r\n    VALIDATION_PATTERNS,\r\n    REGISTRATION_STEPS,\r\n    GENDER_OPTIONS,\r\n} from '@/constants/auth'\r\n\r\ninterface RegisterFormProps {\r\n    onSuccess?: () => void\r\n    onSwitchToLogin?: () => void\r\n}\r\n\r\ninterface FormData {\r\n    // Step 1 - Personal Info\r\n    name: string\r\n    email: string\r\n    password: string\r\n    confirmPassword: string\r\n\r\n    // Step 2 - Contact Info\r\n    phone: string\r\n    country: string\r\n    nationality: string\r\n\r\n    // Step 3 - Additional Info\r\n    age: string\r\n    gender: string\r\n    occupation: string\r\n    smoker: boolean | null\r\n}\r\n\r\ninterface FormErrors {\r\n    [key: string]: string | undefined\r\n}\r\n\r\nconst TOTAL_STEPS = 4\r\n\r\nexport function RegisterForm({ onSuccess, onSwitchToLogin }: RegisterFormProps) {\r\n    const locale = useLocale()\r\n    const [currentStep, setCurrentStep] = useState(1)\r\n    const [formData, setFormData] = useState<FormData>({\r\n        name: '',\r\n        email: '',\r\n        password: '',\r\n        confirmPassword: '',\r\n        phone: '',\r\n        country: '',\r\n        nationality: '',\r\n        age: '',\r\n        gender: '',\r\n        occupation: '',\r\n        smoker: null\r\n    })\r\n    const [errors, setErrors] = useState<FormErrors>({})\r\n    const [isLoading, setIsLoading] = useState(false)\r\n    const [showPassword, setShowPassword] = useState(false)\r\n    const [showConfirmPassword, setShowConfirmPassword] = useState(false)\r\n\r\n    const { setUser } = useUserStore()\r\n    const { setIsUser, setIsAdmin, setUser: setHeaderUser } = useHeaderStore()\r\n    const t = useTranslations('header.modals.register')\r\n\r\n    const validateStep = (step: number): boolean => {\r\n        const newErrors: FormErrors = {}\r\n\r\n        switch (step) {\r\n            case REGISTRATION_STEPS.PERSONAL_INFO:\r\n                if (!formData.name.trim()) {\r\n                    newErrors.name = t('errors.name_required')\r\n                } else if (formData.name.trim().length < 2) {\r\n                    newErrors.name = t('errors.name_min')\r\n                }\r\n\r\n                if (!formData.email) {\r\n                    newErrors.email = t('errors.email_required')\r\n                } else if (!VALIDATION_PATTERNS.email.test(formData.email)) {\r\n                    newErrors.email = t('errors.email_invalid')\r\n                }\r\n\r\n                if (!formData.password) {\r\n                    newErrors.password = t('errors.password_required')\r\n                } else if (!VALIDATION_PATTERNS.password.test(formData.password)) {\r\n                    newErrors.password = t('errors.password_weak')\r\n                }\r\n\r\n                if (!formData.confirmPassword) {\r\n                    newErrors.confirmPassword = t('errors.password_required')\r\n                } else if (formData.password !== formData.confirmPassword) {\r\n                    newErrors.confirmPassword = t('errors.password_match')\r\n                }\r\n                break\r\n\r\n            case REGISTRATION_STEPS.CONTACT_INFO:\r\n                if (!formData.phone) {\r\n                    newErrors.phone = t('errors.phone_required')\r\n                }\r\n\r\n                if (!formData.country) {\r\n                    newErrors.country = t('errors.country_required')\r\n                } else if (!ALLOWED_COUNTRIES.some(c => c.name_en === formData.country)) {\r\n                    newErrors.country = t('errors.country_not_allowed')\r\n                }\r\n\r\n                if (!formData.nationality) {\r\n                    newErrors.nationality = t('errors.nationality_required')\r\n                }\r\n                break\r\n\r\n            case REGISTRATION_STEPS.ADDITIONAL_INFO:\r\n                if (!formData.age.trim()) {\r\n                    newErrors.age = t('errors.age_required')\r\n                } else if (isNaN(Number(formData.age)) || Number(formData.age) < 18 || Number(formData.age) > 100) {\r\n                    newErrors.age = t('errors.age_invalid')\r\n                }\r\n\r\n                if (!formData.gender) {\r\n                    newErrors.gender = t('errors.gender_required')\r\n                }\r\n\r\n                if (!formData.occupation.trim()) {\r\n                    newErrors.occupation = t('errors.occupation_required')\r\n                }\r\n\r\n                if (formData.smoker === null) {\r\n                    newErrors.smoker = t('errors.smoker_required')\r\n                }\r\n                break\r\n        }\r\n\r\n        setErrors(newErrors)\r\n        return Object.keys(newErrors).length === 0\r\n    }\r\n\r\n    const handleNext = () => {\r\n        if (validateStep(currentStep)) {\r\n            setCurrentStep(prev => Math.min(prev + 1, TOTAL_STEPS))\r\n        }\r\n    }\r\n\r\n    const handlePrevious = () => {\r\n        setCurrentStep(prev => Math.max(prev - 1, 1))\r\n        setErrors({})\r\n    }\r\n\r\n    const handleSubmit = async () => {\r\n        if (!validateStep(REGISTRATION_STEPS.ADDITIONAL_INFO)) return\r\n\r\n        setIsLoading(true)\r\n        setErrors({})\r\n\r\n        try {\r\n            const response = await registerUser({\r\n                name: formData.name.trim(),\r\n                email: formData.email,\r\n                password: formData.password,\r\n                phone: formData.phone,\r\n                smoker: formData.smoker as boolean,\r\n                age: formData.age,\r\n                gender: formData.gender,\r\n                nationality: formData.nationality,\r\n                country: formData.country,\r\n                occupation: formData.occupation.trim()\r\n            })\r\n\r\n            if (response.data && !response.error) {\r\n                // Set user in store\r\n                setUser(response.data.user)\r\n\r\n                // Sync header store\r\n                setIsUser(true)\r\n                setIsAdmin(response.data.user.isAdmin)\r\n                setHeaderUser({\r\n                    id: response.data.user.id,\r\n                    name: response.data.user.name,\r\n                    isAdmin: response.data.user.isAdmin\r\n                })\r\n\r\n                onSuccess?.()\r\n            } else {\r\n                setErrors({ general: response.error || 'Registration failed' })\r\n            }\r\n        } catch (error) {\r\n            console.error('Registration error:', error)\r\n            setErrors({ general: 'Registration failed. Please try again.' })\r\n        } finally {\r\n            setIsLoading(false)\r\n        }\r\n    }\r\n\r\n    const handleInputChange = (field: keyof FormData) => (e: React.ChangeEvent<HTMLInputElement>) => {\r\n        setFormData(prev => ({ ...prev, [field]: e.target.value }))\r\n        // Clear field error when user starts typing\r\n        if (errors[field]) {\r\n            setErrors(prev => ({ ...prev, [field]: undefined }))\r\n        }\r\n    }\r\n\r\n    const handleSelectChange = (field: keyof FormData) => (value: string) => {\r\n        setFormData(prev => ({ ...prev, [field]: value }))\r\n        if (errors[field]) {\r\n            setErrors(prev => ({ ...prev, [field]: undefined }))\r\n        }\r\n    }\r\n\r\n    const handleSmokerChange = (value: boolean) => {\r\n        setFormData(prev => ({ ...prev, smoker: value }))\r\n        if (errors.smoker) {\r\n            setErrors(prev => ({ ...prev, smoker: undefined }))\r\n        }\r\n    }\r\n\r\n    const renderPersonalInfoStep = () => (\r\n        <div className=\"space-y-3\">\r\n            <div className=\"text-center mb-6\">\r\n                <h3 className=\"text-lg font-semibold\">{t('steps.personal.title')}</h3>\r\n            </div>\r\n\r\n            <div className=\"space-y-2\">\r\n                <Label htmlFor=\"name\" className=\"text-sm font-medium\">\r\n                    {t('steps.personal.name')}\r\n                </Label>\r\n                <Input\r\n                    id=\"name\"\r\n                    type=\"text\"\r\n                    value={formData.name}\r\n                    onChange={handleInputChange('name')}\r\n                    placeholder={t('steps.personal.name')}\r\n                    className={errors.name ? 'border-red-500' : ''}\r\n                />\r\n                {errors.name && (\r\n                    <p className=\"text-sm text-red-600\">{errors.name}</p>\r\n                )}\r\n            </div>\r\n\r\n            <div className=\"space-y-2\">\r\n                <Label htmlFor=\"email\" className=\"text-sm font-medium\">\r\n                    {t('steps.personal.email')}\r\n                </Label>\r\n                <Input\r\n                    id=\"email\"\r\n                    type=\"email\"\r\n                    value={formData.email}\r\n                    onChange={handleInputChange('email')}\r\n                    placeholder={t('steps.personal.email')}\r\n                    className={errors.email ? 'border-red-500' : ''}\r\n                />\r\n                {errors.email && (\r\n                    <p className=\"text-sm text-red-600\">{errors.email}</p>\r\n                )}\r\n            </div>\r\n\r\n            <div className=\"space-y-2\">\r\n                <Label htmlFor=\"password\" className=\"text-sm font-medium\">\r\n                    {t('steps.personal.password')}\r\n                </Label>\r\n                <div className=\"relative\">\r\n                    <Input\r\n                        id=\"password\"\r\n                        type={showPassword ? 'text' : 'password'}\r\n                        value={formData.password}\r\n                        onChange={handleInputChange('password')}\r\n                        placeholder={t('steps.personal.password')}\r\n                        className={errors.password ? 'border-red-500' : ''}\r\n                    />\r\n                    <button\r\n                        type=\"button\"\r\n                        onClick={() => setShowPassword(!showPassword)}\r\n                        className={`absolute ${locale === 'ar' ? 'left-3' : 'right-3'} top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none`}\r\n                    >\r\n                        {showPassword ? (\r\n                            <EyeOff className=\"h-4 w-4\" />\r\n                        ) : (\r\n                            <Eye className=\"h-4 w-4\" />\r\n                        )}\r\n                    </button>\r\n                </div>\r\n                {errors.password && (\r\n                    <p className=\"text-sm text-red-600\">{errors.password}</p>\r\n                )}\r\n            </div>\r\n\r\n            <div className=\"space-y-2\">\r\n                <Label htmlFor=\"confirmPassword\" className=\"text-sm font-medium\">\r\n                    {t('steps.personal.confirm_password')}\r\n                </Label>\r\n                <div className=\"relative\">\r\n                    <Input\r\n                        id=\"confirmPassword\"\r\n                        type={showConfirmPassword ? 'text' : 'password'}\r\n                        value={formData.confirmPassword}\r\n                        onChange={handleInputChange('confirmPassword')}\r\n                        placeholder={t('steps.personal.confirm_password')}\r\n                        className={errors.confirmPassword ? 'border-red-500' : ''}\r\n                    />\r\n                    <button\r\n                        type=\"button\"\r\n                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}\r\n                        className={`absolute ${locale === 'ar' ? 'left-3' : 'right-3'} top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none`}\r\n                    >\r\n                        {showConfirmPassword ? (\r\n                            <EyeOff className=\"h-4 w-4\" />\r\n                        ) : (\r\n                            <Eye className=\"h-4 w-4\" />\r\n                        )}\r\n                    </button>\r\n                </div>\r\n                {errors.confirmPassword && (\r\n                    <p className=\"text-sm text-red-600\">{errors.confirmPassword}</p>\r\n                )}\r\n            </div>\r\n        </div >\r\n    )\r\n\r\n    const renderContactInfoStep = () => (\r\n        <div className=\"space-y-3\">\r\n            <div className=\"text-center mb-6\">\r\n                <h3 className=\"text-lg font-semibold\">{t('steps.contact.title')}</h3>\r\n            </div>\r\n\r\n            <div className=\"space-y-2\">\r\n                <Label htmlFor=\"phone\" className=\"text-sm font-medium\">\r\n                    {t('steps.contact.phone')}\r\n                </Label>\r\n                <Input\r\n                    id=\"phone\"\r\n                    value={formData.phone}\r\n                    onChange={handleInputChange('phone')}\r\n                    placeholder={t('steps.contact.phone')}\r\n                    className={errors.phone ? 'border-red-500' : ''}\r\n                />\r\n                {errors.phone && (\r\n                    <p className=\"text-sm text-red-600\">{errors.phone}</p>\r\n                )}\r\n            </div>\r\n\r\n            <div className=\"space-y-2\">\r\n                <Label htmlFor=\"country\" className=\"text-sm font-medium\">\r\n                    {t('steps.contact.country')}\r\n                </Label>\r\n                <Select onValueChange={handleSelectChange('country')} value={formData.country}>\r\n                    <SelectTrigger className=\"w-full\">\r\n                        <SelectValue placeholder={t('steps.contact.country')} />\r\n                    </SelectTrigger>\r\n                    <SelectContent>\r\n                        {ALLOWED_COUNTRIES.map(country => (\r\n                            <SelectItem key={country.code} value={country.name_en}>\r\n                                {locale === 'ar' ? country.name_ar : country.name_en}\r\n                            </SelectItem>\r\n                        ))}\r\n                    </SelectContent>\r\n                </Select>\r\n                {errors.country && (\r\n                    <p className=\"text-sm text-red-600\">{errors.country}</p>\r\n                )}\r\n            </div>\r\n\r\n            <div className=\"space-y-2\">\r\n                <Label htmlFor=\"nationality\" className=\"text-sm font-medium\">\r\n                    {t('steps.contact.nationality')}\r\n                </Label>\r\n                <Input\r\n                    id=\"nationality\"\r\n                    type=\"text\"\r\n                    value={formData.nationality}\r\n                    onChange={handleInputChange('nationality')}\r\n                    placeholder={t('steps.contact.nationality')}\r\n                    className={errors.nationality ? 'border-red-500' : ''}\r\n                />\r\n                {errors.nationality && (\r\n                    <p className=\"text-sm text-red-600\">{errors.nationality}</p>\r\n                )}\r\n            </div>\r\n        </div>\r\n    )\r\n\r\n    const renderAdditionalInfoStep = () => (\r\n        <div className=\"space-y-3\">\r\n            <div className=\"text-center mb-6\">\r\n                <h3 className=\"text-lg font-semibold\">{t('steps.additional.title')}</h3>\r\n            </div>\r\n\r\n            <div className=\"space-y-2\">\r\n                <Label htmlFor=\"age\" className=\"text-sm font-medium\">\r\n                    {t('steps.additional.age')}\r\n                </Label>\r\n                <Input\r\n                    id=\"age\"\r\n                    type=\"text\"\r\n                    value={formData.age}\r\n                    onChange={handleInputChange('age')}\r\n                    placeholder={t('steps.additional.age')}\r\n                    className={errors.age ? 'border-red-500' : ''}\r\n                />\r\n                {errors.age && (\r\n                    <p className=\"text-sm text-red-600\">{errors.age}</p>\r\n                )}\r\n            </div>\r\n\r\n            <div className=\"space-y-2\">\r\n                <Label htmlFor=\"gender\" className=\"text-sm font-medium\">\r\n                    {t('steps.additional.gender')}\r\n                </Label>\r\n                <Select onValueChange={handleSelectChange('gender')} value={formData.gender}>\r\n                    <SelectTrigger className=\"w-full\">\r\n                        <SelectValue placeholder={t('steps.additional.gender')} />\r\n                    </SelectTrigger>\r\n                    <SelectContent>\r\n                        {GENDER_OPTIONS.map(option => (\r\n                            <SelectItem key={option.gender} value={option.gender}>\r\n                                {locale === 'ar' ? option.gender_ar : option.gender}\r\n                            </SelectItem>\r\n                        ))}\r\n                    </SelectContent>\r\n                </Select>\r\n                {errors.gender && (\r\n                    <p className=\"text-sm text-red-600\">{errors.gender}</p>\r\n                )}\r\n            </div>\r\n\r\n            <div className=\"space-y-2\">\r\n                <Label htmlFor=\"occupation\" className=\"text-sm font-medium\">\r\n                    {t('steps.additional.occupation')}\r\n                </Label>\r\n                <Input\r\n                    id=\"occupation\"\r\n                    type=\"text\"\r\n                    value={formData.occupation}\r\n                    onChange={handleInputChange('occupation')}\r\n                    placeholder={t('steps.additional.occupation')}\r\n                    className={errors.occupation ? 'border-red-500' : ''}\r\n                />\r\n                {errors.occupation && (\r\n                    <p className=\"text-sm text-red-600\">{errors.occupation}</p>\r\n                )}\r\n            </div>\r\n\r\n            <div className=\"space-y-2\">\r\n                <Label className=\"text-sm font-medium\">\r\n                    {t('steps.additional.smoker')}\r\n                </Label>\r\n                <div className=\"flex gap-4\">\r\n                    <Button\r\n                        type=\"button\"\r\n                        variant={formData.smoker === true ? 'default' : 'outline'}\r\n                        onClick={() => handleSmokerChange(true)}\r\n                        className=\"flex-1\"\r\n                    >\r\n                        {t('steps.additional.yes')}\r\n                    </Button>\r\n                    <Button\r\n                        type=\"button\"\r\n                        variant={formData.smoker === false ? 'default' : 'outline'}\r\n                        onClick={() => handleSmokerChange(false)}\r\n                        className=\"flex-1\"\r\n                    >\r\n                        {t('steps.additional.no')}\r\n                    </Button>\r\n                </div>\r\n                {errors.smoker && (\r\n                    <p className=\"text-sm text-red-600\">{errors.smoker}</p>\r\n                )}\r\n            </div>\r\n        </div>\r\n    )\r\n\r\n    const renderVerificationStep = () => (\r\n        <div className=\"space-y-5\">\r\n            <div className=\"text-center mb-8\">\r\n                <h3 className=\"text-lg font-semibold\">{t('steps.verification.title')}</h3>\r\n                <p className=\"text-sm text-muted-foreground mt-3\">\r\n                    {t('steps.verification.review_text')}\r\n                </p>\r\n            </div>\r\n\r\n            <div className=\"space-y-3 bg-gray-50 p-4 rounded-lg\">\r\n                <div className=\"grid grid-cols-2 gap-2 text-sm\">\r\n                    <span className=\"font-medium\">{t('steps.personal.name')}:</span>\r\n                    <span>{formData.name}</span>\r\n\r\n                    <span className=\"font-medium\">{t('steps.personal.email')}:</span>\r\n                    <span>{formData.email}</span>\r\n\r\n                    <span className=\"font-medium\">{t('steps.contact.phone')}:</span>\r\n                    <span>{formData.phone}</span>\r\n\r\n                    <span className=\"font-medium\">{t('steps.contact.country')}:</span>\r\n                    <span>{formData.country}</span>\r\n\r\n                    <span className=\"font-medium\">{t('steps.contact.nationality')}:</span>\r\n                    <span>{formData.nationality}</span>\r\n\r\n                    <span className=\"font-medium\">{t('steps.additional.age')}:</span>\r\n                    <span>{formData.age}</span>\r\n\r\n                    <span className=\"font-medium\">{t('steps.additional.gender')}:</span>\r\n                    <span>{formData.gender}</span>\r\n\r\n                    <span className=\"font-medium\">{t('steps.additional.occupation')}:</span>\r\n                    <span>{formData.occupation}</span>\r\n\r\n                    <span className=\"font-medium\">{t('steps.additional.smoker')}:</span>\r\n                    <span>{formData.smoker ? t('steps.additional.yes') : t('steps.additional.no')}</span>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    )\r\n\r\n    const renderStepContent = () => {\r\n        switch (currentStep) {\r\n            case REGISTRATION_STEPS.PERSONAL_INFO:\r\n                return renderPersonalInfoStep()\r\n            case REGISTRATION_STEPS.CONTACT_INFO:\r\n                return renderContactInfoStep()\r\n            case REGISTRATION_STEPS.ADDITIONAL_INFO:\r\n                return renderAdditionalInfoStep()\r\n            case REGISTRATION_STEPS.VERIFICATION:\r\n                return renderVerificationStep()\r\n            default:\r\n                return null\r\n        }\r\n    }\r\n\r\n    return (\r\n        <div className=\"space-y-8\">\r\n            {/* Progress indicator */}\r\n            <div className=\"text-center\">\r\n                <p className=\"text-sm text-muted-foreground mb-3\">\r\n                    {t('step', { current: currentStep, total: TOTAL_STEPS })}\r\n                </p>\r\n                <div className=\"w-full bg-gray-200 rounded-full h-2\">\r\n                    <div\r\n                        className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\r\n                        style={{ width: `${(currentStep / TOTAL_STEPS) * 100}%` }}\r\n                    />\r\n                </div>\r\n            </div>\r\n\r\n            {/* Error message */}\r\n            {errors.general && (\r\n                <div className=\"p-3 text-sm text-red-600 bg-red-50 rounded-md border border-red-200\">\r\n                    {errors.general}\r\n                </div>\r\n            )}\r\n\r\n            {/* Step content */}\r\n            {renderStepContent()}\r\n\r\n            {/* Navigation buttons */}\r\n            <div className=\"flex justify-between pt-4\">\r\n                <Button\r\n                    type=\"button\"\r\n                    variant=\"outline\"\r\n                    onClick={handlePrevious}\r\n                    disabled={currentStep === 1 || isLoading}\r\n                    className=\"flex items-center gap-2\"\r\n                >\r\n                    <ArrowLeft className={`${locale === 'ar' ? 'rotate-180' : ''} h-4 w-4`} />\r\n                    {t('previous')}\r\n                </Button>\r\n\r\n                {currentStep < TOTAL_STEPS ? (\r\n                    <Button\r\n                        type=\"button\"\r\n                        onClick={handleNext}\r\n                        disabled={isLoading}\r\n                        className=\"flex items-center gap-2\"\r\n                    >\r\n                        {t('next')}\r\n                        <ArrowRight className={`${locale === 'ar' ? 'rotate-180' : ''} h-4 w-4`} />\r\n                    </Button>\r\n                ) : (\r\n                    <Button\r\n                        type=\"button\"\r\n                        onClick={handleSubmit}\r\n                        disabled={isLoading}\r\n                        className=\"flex items-center gap-2\"\r\n                    >\r\n                        {isLoading ? (\r\n                            <>\r\n                                <UserPlus className=\"mr-2 h-4 w-4 animate-spin\" />\r\n                                {t('loading')}\r\n                            </>\r\n                        ) : (\r\n                            <>\r\n                                <UserPlus className=\"mr-2 h-4 w-4\" />\r\n                                {t('submit')}\r\n                            </>\r\n                        )}\r\n                    </Button>\r\n                )}\r\n            </div>\r\n\r\n            {/* Switch to login */}\r\n            <div className=\"flex items-center justify-center pt-6 mt-6 border-t\">\r\n                <p className=\"text-sm text-muted-foreground text-center\">\r\n                    {t('have_account')}\r\n                </p>\r\n                <Button\r\n                    type=\"button\"\r\n                    variant=\"link\"\r\n                    onClick={onSwitchToLogin}\r\n                    className=\"p-0 h-auto text-primary hover:underline ml-2\"\r\n                    disabled={isLoading}\r\n                >\r\n                    {t('login_link')}\r\n                </Button>\r\n            </div>\r\n        </div>\r\n    )\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;;;AAlBA;;;;;;;;;;;;AAqDA,MAAM,cAAc;AAEb,SAAS,aAAa,EAAE,SAAS,EAAE,eAAe,EAAqB;;IAC1E,MAAM,SAAS,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QAC/C,MAAM;QACN,OAAO;QACP,UAAU;QACV,iBAAiB;QACjB,OAAO;QACP,SAAS;QACT,aAAa;QACb,KAAK;QACL,QAAQ;QACR,YAAY;QACZ,QAAQ;IACZ;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,CAAC;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAC/B,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,aAAa,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IACvE,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,MAAM,eAAe,CAAC;QAClB,MAAM,YAAwB,CAAC;QAE/B,OAAQ;YACJ,KAAK,2HAAA,CAAA,qBAAkB,CAAC,aAAa;gBACjC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;oBACvB,UAAU,IAAI,GAAG,EAAE;gBACvB,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;oBACxC,UAAU,IAAI,GAAG,EAAE;gBACvB;gBAEA,IAAI,CAAC,SAAS,KAAK,EAAE;oBACjB,UAAU,KAAK,GAAG,EAAE;gBACxB,OAAO,IAAI,CAAC,2HAAA,CAAA,sBAAmB,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,KAAK,GAAG;oBACxD,UAAU,KAAK,GAAG,EAAE;gBACxB;gBAEA,IAAI,CAAC,SAAS,QAAQ,EAAE;oBACpB,UAAU,QAAQ,GAAG,EAAE;gBAC3B,OAAO,IAAI,CAAC,2HAAA,CAAA,sBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,QAAQ,GAAG;oBAC9D,UAAU,QAAQ,GAAG,EAAE;gBAC3B;gBAEA,IAAI,CAAC,SAAS,eAAe,EAAE;oBAC3B,UAAU,eAAe,GAAG,EAAE;gBAClC,OAAO,IAAI,SAAS,QAAQ,KAAK,SAAS,eAAe,EAAE;oBACvD,UAAU,eAAe,GAAG,EAAE;gBAClC;gBACA;YAEJ,KAAK,2HAAA,CAAA,qBAAkB,CAAC,YAAY;gBAChC,IAAI,CAAC,SAAS,KAAK,EAAE;oBACjB,UAAU,KAAK,GAAG,EAAE;gBACxB;gBAEA,IAAI,CAAC,SAAS,OAAO,EAAE;oBACnB,UAAU,OAAO,GAAG,EAAE;gBAC1B,OAAO,IAAI,CAAC,2HAAA,CAAA,oBAAiB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,SAAS,OAAO,GAAG;oBACrE,UAAU,OAAO,GAAG,EAAE;gBAC1B;gBAEA,IAAI,CAAC,SAAS,WAAW,EAAE;oBACvB,UAAU,WAAW,GAAG,EAAE;gBAC9B;gBACA;YAEJ,KAAK,2HAAA,CAAA,qBAAkB,CAAC,eAAe;gBACnC,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,IAAI;oBACtB,UAAU,GAAG,GAAG,EAAE;gBACtB,OAAO,IAAI,MAAM,OAAO,SAAS,GAAG,MAAM,OAAO,SAAS,GAAG,IAAI,MAAM,OAAO,SAAS,GAAG,IAAI,KAAK;oBAC/F,UAAU,GAAG,GAAG,EAAE;gBACtB;gBAEA,IAAI,CAAC,SAAS,MAAM,EAAE;oBAClB,UAAU,MAAM,GAAG,EAAE;gBACzB;gBAEA,IAAI,CAAC,SAAS,UAAU,CAAC,IAAI,IAAI;oBAC7B,UAAU,UAAU,GAAG,EAAE;gBAC7B;gBAEA,IAAI,SAAS,MAAM,KAAK,MAAM;oBAC1B,UAAU,MAAM,GAAG,EAAE;gBACzB;gBACA;QACR;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC7C;IAEA,MAAM,aAAa;QACf,IAAI,aAAa,cAAc;YAC3B,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,OAAO,GAAG;QAC9C;IACJ;IAEA,MAAM,iBAAiB;QACnB,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,OAAO,GAAG;QAC1C,UAAU,CAAC;IACf;IAEA,MAAM,eAAe;QACjB,IAAI,CAAC,aAAa,2HAAA,CAAA,qBAAkB,CAAC,eAAe,GAAG;QAEvD,aAAa;QACb,UAAU,CAAC;QAEX,IAAI;YACA,MAAM,WAAW,MAAM,CAAA,GAAA,yJAAA,CAAA,eAAY,AAAD,EAAE;gBAChC,MAAM,SAAS,IAAI,CAAC,IAAI;gBACxB,OAAO,SAAS,KAAK;gBACrB,UAAU,SAAS,QAAQ;gBAC3B,OAAO,SAAS,KAAK;gBACrB,QAAQ,SAAS,MAAM;gBACvB,KAAK,SAAS,GAAG;gBACjB,QAAQ,SAAS,MAAM;gBACvB,aAAa,SAAS,WAAW;gBACjC,SAAS,SAAS,OAAO;gBACzB,YAAY,SAAS,UAAU,CAAC,IAAI;YACxC;YAEA,IAAI,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,EAAE;gBAClC,oBAAoB;gBACpB,QAAQ,SAAS,IAAI,CAAC,IAAI;gBAE1B,oBAAoB;gBACpB,UAAU;gBACV,WAAW,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO;gBACrC,cAAc;oBACV,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,EAAE;oBACzB,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI;oBAC7B,SAAS,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO;gBACvC;gBAEA;YACJ,OAAO;gBACH,UAAU;oBAAE,SAAS,SAAS,KAAK,IAAI;gBAAsB;YACjE;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,uBAAuB;YACrC,UAAU;gBAAE,SAAS;YAAyC;QAClE,SAAU;YACN,aAAa;QACjB;IACJ;IAEA,MAAM,oBAAoB,CAAC,QAA0B,CAAC;YAClD,YAAY,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE,EAAE,MAAM,CAAC,KAAK;gBAAC,CAAC;YACzD,4CAA4C;YAC5C,IAAI,MAAM,CAAC,MAAM,EAAE;gBACf,UAAU,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,CAAC,MAAM,EAAE;oBAAU,CAAC;YACtD;QACJ;IAEA,MAAM,qBAAqB,CAAC,QAA0B,CAAC;YACnD,YAAY,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAM,CAAC;YAChD,IAAI,MAAM,CAAC,MAAM,EAAE;gBACf,UAAU,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,CAAC,MAAM,EAAE;oBAAU,CAAC;YACtD;QACJ;IAEA,MAAM,qBAAqB,CAAC;QACxB,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,QAAQ;YAAM,CAAC;QAC/C,IAAI,OAAO,MAAM,EAAE;YACf,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,QAAQ;gBAAU,CAAC;QACrD;IACJ;IAEA,MAAM,yBAAyB,kBAC3B,6LAAC;YAAI,WAAU;;8BACX,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBAAG,WAAU;kCAAyB,EAAE;;;;;;;;;;;8BAG7C,6LAAC;oBAAI,WAAU;;sCACX,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAO,WAAU;sCAC3B,EAAE;;;;;;sCAEP,6LAAC,oIAAA,CAAA,QAAK;4BACF,IAAG;4BACH,MAAK;4BACL,OAAO,SAAS,IAAI;4BACpB,UAAU,kBAAkB;4BAC5B,aAAa,EAAE;4BACf,WAAW,OAAO,IAAI,GAAG,mBAAmB;;;;;;wBAE/C,OAAO,IAAI,kBACR,6LAAC;4BAAE,WAAU;sCAAwB,OAAO,IAAI;;;;;;;;;;;;8BAIxD,6LAAC;oBAAI,WAAU;;sCACX,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAQ,WAAU;sCAC5B,EAAE;;;;;;sCAEP,6LAAC,oIAAA,CAAA,QAAK;4BACF,IAAG;4BACH,MAAK;4BACL,OAAO,SAAS,KAAK;4BACrB,UAAU,kBAAkB;4BAC5B,aAAa,EAAE;4BACf,WAAW,OAAO,KAAK,GAAG,mBAAmB;;;;;;wBAEhD,OAAO,KAAK,kBACT,6LAAC;4BAAE,WAAU;sCAAwB,OAAO,KAAK;;;;;;;;;;;;8BAIzD,6LAAC;oBAAI,WAAU;;sCACX,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAW,WAAU;sCAC/B,EAAE;;;;;;sCAEP,6LAAC;4BAAI,WAAU;;8CACX,6LAAC,oIAAA,CAAA,QAAK;oCACF,IAAG;oCACH,MAAM,eAAe,SAAS;oCAC9B,OAAO,SAAS,QAAQ;oCACxB,UAAU,kBAAkB;oCAC5B,aAAa,EAAE;oCACf,WAAW,OAAO,QAAQ,GAAG,mBAAmB;;;;;;8CAEpD,6LAAC;oCACG,MAAK;oCACL,SAAS,IAAM,gBAAgB,CAAC;oCAChC,WAAW,CAAC,SAAS,EAAE,WAAW,OAAO,WAAW,UAAU,wFAAwF,CAAC;8CAEtJ,6BACG,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;6DAElB,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;;;;;;;wBAI1B,OAAO,QAAQ,kBACZ,6LAAC;4BAAE,WAAU;sCAAwB,OAAO,QAAQ;;;;;;;;;;;;8BAI5D,6LAAC;oBAAI,WAAU;;sCACX,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAkB,WAAU;sCACtC,EAAE;;;;;;sCAEP,6LAAC;4BAAI,WAAU;;8CACX,6LAAC,oIAAA,CAAA,QAAK;oCACF,IAAG;oCACH,MAAM,sBAAsB,SAAS;oCACrC,OAAO,SAAS,eAAe;oCAC/B,UAAU,kBAAkB;oCAC5B,aAAa,EAAE;oCACf,WAAW,OAAO,eAAe,GAAG,mBAAmB;;;;;;8CAE3D,6LAAC;oCACG,MAAK;oCACL,SAAS,IAAM,uBAAuB,CAAC;oCACvC,WAAW,CAAC,SAAS,EAAE,WAAW,OAAO,WAAW,UAAU,wFAAwF,CAAC;8CAEtJ,oCACG,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;6DAElB,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;;;;;;;wBAI1B,OAAO,eAAe,kBACnB,6LAAC;4BAAE,WAAU;sCAAwB,OAAO,eAAe;;;;;;;;;;;;;;;;;;IAM3E,MAAM,wBAAwB,kBAC1B,6LAAC;YAAI,WAAU;;8BACX,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBAAG,WAAU;kCAAyB,EAAE;;;;;;;;;;;8BAG7C,6LAAC;oBAAI,WAAU;;sCACX,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAQ,WAAU;sCAC5B,EAAE;;;;;;sCAEP,6LAAC,oIAAA,CAAA,QAAK;4BACF,IAAG;4BACH,OAAO,SAAS,KAAK;4BACrB,UAAU,kBAAkB;4BAC5B,aAAa,EAAE;4BACf,WAAW,OAAO,KAAK,GAAG,mBAAmB;;;;;;wBAEhD,OAAO,KAAK,kBACT,6LAAC;4BAAE,WAAU;sCAAwB,OAAO,KAAK;;;;;;;;;;;;8BAIzD,6LAAC;oBAAI,WAAU;;sCACX,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAU,WAAU;sCAC9B,EAAE;;;;;;sCAEP,6LAAC,qIAAA,CAAA,SAAM;4BAAC,eAAe,mBAAmB;4BAAY,OAAO,SAAS,OAAO;;8CACzE,6LAAC,qIAAA,CAAA,gBAAa;oCAAC,WAAU;8CACrB,cAAA,6LAAC,qIAAA,CAAA,cAAW;wCAAC,aAAa,EAAE;;;;;;;;;;;8CAEhC,6LAAC,qIAAA,CAAA,gBAAa;8CACT,2HAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,CAAA,wBACnB,6LAAC,qIAAA,CAAA,aAAU;4CAAoB,OAAO,QAAQ,OAAO;sDAChD,WAAW,OAAO,QAAQ,OAAO,GAAG,QAAQ,OAAO;2CADvC,QAAQ,IAAI;;;;;;;;;;;;;;;;wBAMxC,OAAO,OAAO,kBACX,6LAAC;4BAAE,WAAU;sCAAwB,OAAO,OAAO;;;;;;;;;;;;8BAI3D,6LAAC;oBAAI,WAAU;;sCACX,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAc,WAAU;sCAClC,EAAE;;;;;;sCAEP,6LAAC,oIAAA,CAAA,QAAK;4BACF,IAAG;4BACH,MAAK;4BACL,OAAO,SAAS,WAAW;4BAC3B,UAAU,kBAAkB;4BAC5B,aAAa,EAAE;4BACf,WAAW,OAAO,WAAW,GAAG,mBAAmB;;;;;;wBAEtD,OAAO,WAAW,kBACf,6LAAC;4BAAE,WAAU;sCAAwB,OAAO,WAAW;;;;;;;;;;;;;;;;;;IAMvE,MAAM,2BAA2B,kBAC7B,6LAAC;YAAI,WAAU;;8BACX,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBAAG,WAAU;kCAAyB,EAAE;;;;;;;;;;;8BAG7C,6LAAC;oBAAI,WAAU;;sCACX,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAM,WAAU;sCAC1B,EAAE;;;;;;sCAEP,6LAAC,oIAAA,CAAA,QAAK;4BACF,IAAG;4BACH,MAAK;4BACL,OAAO,SAAS,GAAG;4BACnB,UAAU,kBAAkB;4BAC5B,aAAa,EAAE;4BACf,WAAW,OAAO,GAAG,GAAG,mBAAmB;;;;;;wBAE9C,OAAO,GAAG,kBACP,6LAAC;4BAAE,WAAU;sCAAwB,OAAO,GAAG;;;;;;;;;;;;8BAIvD,6LAAC;oBAAI,WAAU;;sCACX,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAS,WAAU;sCAC7B,EAAE;;;;;;sCAEP,6LAAC,qIAAA,CAAA,SAAM;4BAAC,eAAe,mBAAmB;4BAAW,OAAO,SAAS,MAAM;;8CACvE,6LAAC,qIAAA,CAAA,gBAAa;oCAAC,WAAU;8CACrB,cAAA,6LAAC,qIAAA,CAAA,cAAW;wCAAC,aAAa,EAAE;;;;;;;;;;;8CAEhC,6LAAC,qIAAA,CAAA,gBAAa;8CACT,2HAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,CAAA,uBAChB,6LAAC,qIAAA,CAAA,aAAU;4CAAqB,OAAO,OAAO,MAAM;sDAC/C,WAAW,OAAO,OAAO,SAAS,GAAG,OAAO,MAAM;2CADtC,OAAO,MAAM;;;;;;;;;;;;;;;;wBAMzC,OAAO,MAAM,kBACV,6LAAC;4BAAE,WAAU;sCAAwB,OAAO,MAAM;;;;;;;;;;;;8BAI1D,6LAAC;oBAAI,WAAU;;sCACX,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAa,WAAU;sCACjC,EAAE;;;;;;sCAEP,6LAAC,oIAAA,CAAA,QAAK;4BACF,IAAG;4BACH,MAAK;4BACL,OAAO,SAAS,UAAU;4BAC1B,UAAU,kBAAkB;4BAC5B,aAAa,EAAE;4BACf,WAAW,OAAO,UAAU,GAAG,mBAAmB;;;;;;wBAErD,OAAO,UAAU,kBACd,6LAAC;4BAAE,WAAU;sCAAwB,OAAO,UAAU;;;;;;;;;;;;8BAI9D,6LAAC;oBAAI,WAAU;;sCACX,6LAAC,oIAAA,CAAA,QAAK;4BAAC,WAAU;sCACZ,EAAE;;;;;;sCAEP,6LAAC;4BAAI,WAAU;;8CACX,6LAAC,qIAAA,CAAA,SAAM;oCACH,MAAK;oCACL,SAAS,SAAS,MAAM,KAAK,OAAO,YAAY;oCAChD,SAAS,IAAM,mBAAmB;oCAClC,WAAU;8CAET,EAAE;;;;;;8CAEP,6LAAC,qIAAA,CAAA,SAAM;oCACH,MAAK;oCACL,SAAS,SAAS,MAAM,KAAK,QAAQ,YAAY;oCACjD,SAAS,IAAM,mBAAmB;oCAClC,WAAU;8CAET,EAAE;;;;;;;;;;;;wBAGV,OAAO,MAAM,kBACV,6LAAC;4BAAE,WAAU;sCAAwB,OAAO,MAAM;;;;;;;;;;;;;;;;;;IAMlE,MAAM,yBAAyB,kBAC3B,6LAAC;YAAI,WAAU;;8BACX,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAG,WAAU;sCAAyB,EAAE;;;;;;sCACzC,6LAAC;4BAAE,WAAU;sCACR,EAAE;;;;;;;;;;;;8BAIX,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAAuB;;;;;;;0CACxD,6LAAC;0CAAM,SAAS,IAAI;;;;;;0CAEpB,6LAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAAwB;;;;;;;0CACzD,6LAAC;0CAAM,SAAS,KAAK;;;;;;0CAErB,6LAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAAuB;;;;;;;0CACxD,6LAAC;0CAAM,SAAS,KAAK;;;;;;0CAErB,6LAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAAyB;;;;;;;0CAC1D,6LAAC;0CAAM,SAAS,OAAO;;;;;;0CAEvB,6LAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAA6B;;;;;;;0CAC9D,6LAAC;0CAAM,SAAS,WAAW;;;;;;0CAE3B,6LAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAAwB;;;;;;;0CACzD,6LAAC;0CAAM,SAAS,GAAG;;;;;;0CAEnB,6LAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAA2B;;;;;;;0CAC5D,6LAAC;0CAAM,SAAS,MAAM;;;;;;0CAEtB,6LAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAA+B;;;;;;;0CAChE,6LAAC;0CAAM,SAAS,UAAU;;;;;;0CAE1B,6LAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAA2B;;;;;;;0CAC5D,6LAAC;0CAAM,SAAS,MAAM,GAAG,EAAE,0BAA0B,EAAE;;;;;;;;;;;;;;;;;;;;;;;IAMvE,MAAM,oBAAoB;QACtB,OAAQ;YACJ,KAAK,2HAAA,CAAA,qBAAkB,CAAC,aAAa;gBACjC,OAAO;YACX,KAAK,2HAAA,CAAA,qBAAkB,CAAC,YAAY;gBAChC,OAAO;YACX,KAAK,2HAAA,CAAA,qBAAkB,CAAC,eAAe;gBACnC,OAAO;YACX,KAAK,2HAAA,CAAA,qBAAkB,CAAC,YAAY;gBAChC,OAAO;YACX;gBACI,OAAO;QACf;IACJ;IAEA,qBACI,6LAAC;QAAI,WAAU;;0BAEX,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAE,WAAU;kCACR,EAAE,QAAQ;4BAAE,SAAS;4BAAa,OAAO;wBAAY;;;;;;kCAE1D,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BACG,WAAU;4BACV,OAAO;gCAAE,OAAO,GAAG,AAAC,cAAc,cAAe,IAAI,CAAC,CAAC;4BAAC;;;;;;;;;;;;;;;;;YAMnE,OAAO,OAAO,kBACX,6LAAC;gBAAI,WAAU;0BACV,OAAO,OAAO;;;;;;YAKtB;0BAGD,6LAAC;gBAAI,WAAU;;kCACX,6LAAC,qIAAA,CAAA,SAAM;wBACH,MAAK;wBACL,SAAQ;wBACR,SAAS;wBACT,UAAU,gBAAgB,KAAK;wBAC/B,WAAU;;0CAEV,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAW,GAAG,WAAW,OAAO,eAAe,GAAG,QAAQ,CAAC;;;;;;4BACrE,EAAE;;;;;;;oBAGN,cAAc,4BACX,6LAAC,qIAAA,CAAA,SAAM;wBACH,MAAK;wBACL,SAAS;wBACT,UAAU;wBACV,WAAU;;4BAET,EAAE;0CACH,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAW,GAAG,WAAW,OAAO,eAAe,GAAG,QAAQ,CAAC;;;;;;;;;;;6CAG3E,6LAAC,qIAAA,CAAA,SAAM;wBACH,MAAK;wBACL,SAAS;wBACT,UAAU;wBACV,WAAU;kCAET,0BACG;;8CACI,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCACnB,EAAE;;yDAGP;;8CACI,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCACnB,EAAE;;;;;;;;;;;;;;0BAQvB,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAE,WAAU;kCACR,EAAE;;;;;;kCAEP,6LAAC,qIAAA,CAAA,SAAM;wBACH,MAAK;wBACL,SAAQ;wBACR,SAAS;wBACT,WAAU;wBACV,UAAU;kCAET,EAAE;;;;;;;;;;;;;;;;;;AAKvB;GA5jBgB;;QACG,qKAAA,CAAA,YAAS;QAoBJ,+HAAA,CAAA,eAAY;QAC0B,iIAAA,CAAA,iBAAc;QAC9D,yMAAA,CAAA,kBAAe;;;KAvBb", "debugId": null}}, {"offset": {"line": 2897, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/header/auth-buttons.tsx"], "sourcesContent": ["import { LogIn, UserPlus } from 'lucide-react'\r\nimport { Button } from '@/components/ui/button'\r\nimport {\r\n    <PERSON><PERSON>,\r\n    DialogContent,\r\n    DialogHeader,\r\n    DialogTitle,\r\n} from '@/components/ui/dialog'\r\nimport { useHeaderStore } from '@/store/useHeaderStore'\r\nimport { cn } from '@/lib/utils'\r\nimport { useTranslations } from 'next-intl'\r\nimport { LoginForm } from '@/components/auth/login-form'\r\nimport { RegisterForm } from '@/components/auth/register-form'\r\n\r\ninterface AuthButtonsProps {\r\n    className?: string\r\n}\r\n\r\nexport function AuthButtons({ className }: AuthButtonsProps) {\r\n    const {\r\n        isLoginModalOpen,\r\n        isRegisterModalOpen,\r\n        openLoginModal,\r\n        closeLoginModal,\r\n        openRegisterModal,\r\n        closeRegisterModal,\r\n        toggleModal,\r\n    } = useHeaderStore()\r\n\r\n    const t = useTranslations('header')\r\n\r\n    return (\r\n        <div className={cn(\"flex items-center gap-2\", className)}>\r\n            {/* <PERSON><PERSON> */}\r\n            <Button\r\n                variant=\"ghost\"\r\n                size=\"sm\"\r\n                onClick={openLoginModal}\r\n                className=\"flex items-center gap-2\"\r\n            >\r\n                <LogIn className=\"h-4 w-4\" />\r\n                <span className=\"hidden sm:inline\">{t('login')}</span>\r\n            </Button>\r\n\r\n            {/* Register Button */}\r\n            <Button\r\n                variant=\"default\"\r\n                size=\"sm\"\r\n                onClick={openRegisterModal}\r\n                className=\"flex items-center gap-2\"\r\n            >\r\n                <UserPlus className=\"h-4 w-4\" />\r\n                <span className=\"hidden sm:inline\">{t('register')}</span>\r\n            </Button>\r\n\r\n            {/* Login Modal */}\r\n            <Dialog open={isLoginModalOpen} onOpenChange={closeLoginModal}>\r\n                <DialogContent className=\"sm:max-w-md\">\r\n                    <DialogHeader>\r\n                        <DialogTitle className=\"flex items-center gap-2\">\r\n                            <LogIn className=\"h-5 w-5\" />\r\n                            {t('modals.login.title')}\r\n                        </DialogTitle>\r\n                    </DialogHeader>\r\n                    <LoginForm\r\n                        onSuccess={closeLoginModal}\r\n                        onSwitchToRegister={() => toggleModal('register')}\r\n                    />\r\n                </DialogContent>\r\n            </Dialog>\r\n\r\n            {/* Register Modal */}\r\n            <Dialog open={isRegisterModalOpen} onOpenChange={closeRegisterModal}>\r\n                <DialogContent className=\"sm:max-w-lg max-h-[90vh] overflow-y-auto\">\r\n                    <DialogHeader>\r\n                        <DialogTitle className=\"flex items-center gap-2\">\r\n                            <UserPlus className=\"h-5 w-5\" />\r\n                            {t('modals.register.title')}\r\n                        </DialogTitle>\r\n                    </DialogHeader>\r\n                    <RegisterForm\r\n                        onSuccess={closeRegisterModal}\r\n                        onSwitchToLogin={() => toggleModal('login')}\r\n                    />\r\n                </DialogContent>\r\n            </Dialog>\r\n        </div>\r\n    )\r\n} "], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;AAMA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAMO,SAAS,YAAY,EAAE,SAAS,EAAoB;;IACvD,MAAM,EACF,gBAAgB,EAChB,mBAAmB,EACnB,cAAc,EACd,eAAe,EACf,iBAAiB,EACjB,kBAAkB,EAClB,WAAW,EACd,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAEjB,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,qBACI,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;;0BAE1C,6LAAC,qIAAA,CAAA,SAAM;gBACH,SAAQ;gBACR,MAAK;gBACL,SAAS;gBACT,WAAU;;kCAEV,6LAAC,2MAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,6LAAC;wBAAK,WAAU;kCAAoB,EAAE;;;;;;;;;;;;0BAI1C,6LAAC,qIAAA,CAAA,SAAM;gBACH,SAAQ;gBACR,MAAK;gBACL,SAAS;gBACT,WAAU;;kCAEV,6LAAC,iNAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC;wBAAK,WAAU;kCAAoB,EAAE;;;;;;;;;;;;0BAI1C,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAkB,cAAc;0BAC1C,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACrB,6LAAC,qIAAA,CAAA,eAAY;sCACT,cAAA,6LAAC,qIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACnB,6LAAC,2MAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAChB,EAAE;;;;;;;;;;;;sCAGX,6LAAC,8IAAA,CAAA,YAAS;4BACN,WAAW;4BACX,oBAAoB,IAAM,YAAY;;;;;;;;;;;;;;;;;0BAMlD,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAqB,cAAc;0BAC7C,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACrB,6LAAC,qIAAA,CAAA,eAAY;sCACT,cAAA,6LAAC,qIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACnB,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCACnB,EAAE;;;;;;;;;;;;sCAGX,6LAAC,iJAAA,CAAA,eAAY;4BACT,WAAW;4BACX,iBAAiB,IAAM,YAAY;;;;;;;;;;;;;;;;;;;;;;;AAM3D;GAtEgB;;QASR,iIAAA,CAAA,iBAAc;QAER,yMAAA,CAAA,kBAAe;;;KAXb", "debugId": null}}, {"offset": {"line": 3102, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/i18n/routing.ts"], "sourcesContent": ["import { defineRouting } from \"next-intl/routing\";\r\n\r\nexport const routing = defineRouting({\r\n  // A list of all locales that are supported\r\n  locales: [\"en\", \"ar\"],\r\n\r\n  // Used when no locale matches\r\n  defaultLocale: \"ar\",\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,UAAU,CAAA,GAAA,qOAAA,CAAA,gBAAa,AAAD,EAAE;IACnC,2CAA2C;IAC3C,SAAS;QAAC;QAAM;KAAK;IAErB,8BAA8B;IAC9B,eAAe;AACjB", "debugId": null}}, {"offset": {"line": 3125, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/i18n/navigation.ts"], "sourcesContent": ["import { createNavigation } from \"next-intl/navigation\";\r\nimport { routing } from \"./routing\";\r\n\r\n// Lightweight wrappers around Next.js' navigation\r\n// APIs that consider the routing configuration\r\nexport const { Link, redirect, usePathname, useRouter, getPathname } =\r\n  createNavigation(routing);\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAIO,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,GAClE,CAAA,GAAA,iQAAA,CAAA,mBAAgB,AAAD,EAAE,yHAAA,CAAA,UAAO", "debugId": null}}, {"offset": {"line": 3146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/header/conditional-buttons.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { Plus, LayoutDashboard } from 'lucide-react'\r\nimport { Button } from '@/components/ui/button'\r\nimport { useHeaderStore } from '@/store/useHeaderStore'\r\nimport { cn } from '@/lib/utils'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useRouter } from '@/i18n/navigation'\r\n\r\ninterface ConditionalButtonsProps {\r\n    className?: string\r\n}\r\n\r\nexport function ConditionalButtons({ className }: ConditionalButtonsProps) {\r\n    const router = useRouter()\r\n    const { isUser, isAdmin } = useHeaderStore()\r\n    const t = useTranslations('header.buttons')\r\n\r\n    if (!isUser && !isAdmin) return null\r\n\r\n    return (\r\n        <div className={cn(\"flex items-center gap-2\", className)}>\r\n            {/* Add Property Button - shown when user is logged in */}\r\n            {isUser && (\r\n                <Button\r\n                    variant=\"outline\"\r\n                    size=\"sm\"\r\n                    className=\"flex items-center gap-2\"\r\n                    onClick={() => router.push('/add-property')}\r\n                >\r\n                    <Plus className=\"h-4 w-4\" />\r\n                    <span className=\"hidden sm:inline\">{t('add_property')}</span>\r\n                </Button>\r\n            )}\r\n\r\n            {/* Dashboard Button - shown when user is admin */}\r\n            {isAdmin && (\r\n                <Button\r\n                    variant=\"secondary\"\r\n                    size=\"sm\"\r\n                    className=\"flex items-center gap-2\"\r\n                    onClick={() => router.push('/dashboard')}\r\n                >\r\n                    <LayoutDashboard className=\"h-4 w-4\" />\r\n                    <span className=\"hidden sm:inline\">{t('dashboard')}</span>\r\n                </Button>\r\n            )}\r\n        </div>\r\n    )\r\n} "], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAaO,SAAS,mBAAmB,EAAE,SAAS,EAA2B;;IACrE,MAAM,SAAS,CAAA,GAAA,4HAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IACzC,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,IAAI,CAAC,UAAU,CAAC,SAAS,OAAO;IAEhC,qBACI,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;;YAEzC,wBACG,6LAAC,qIAAA,CAAA,SAAM;gBACH,SAAQ;gBACR,MAAK;gBACL,WAAU;gBACV,SAAS,IAAM,OAAO,IAAI,CAAC;;kCAE3B,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;kCAChB,6LAAC;wBAAK,WAAU;kCAAoB,EAAE;;;;;;;;;;;;YAK7C,yBACG,6LAAC,qIAAA,CAAA,SAAM;gBACH,SAAQ;gBACR,MAAK;gBACL,WAAU;gBACV,SAAS,IAAM,OAAO,IAAI,CAAC;;kCAE3B,6LAAC,+NAAA,CAAA,kBAAe;wBAAC,WAAU;;;;;;kCAC3B,6LAAC;wBAAK,WAAU;kCAAoB,EAAE;;;;;;;;;;;;;;;;;;AAK1D;GApCgB;;QACG,4HAAA,CAAA,YAAS;QACI,iIAAA,CAAA,iBAAc;QAChC,yMAAA,CAAA,kBAAe;;;KAHb", "debugId": null}}, {"offset": {"line": 3255, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/header/language-switcher.tsx"], "sourcesContent": ["import { Globe } from 'lucide-react'\r\nimport {\r\n    Select,\r\n    SelectContent,\r\n    SelectItem,\r\n    SelectTrigger,\r\n    SelectValue,\r\n} from '@/components/ui/select'\r\nimport { cn } from '@/lib/utils'\r\nimport { useTranslations, useLocale } from 'next-intl'\r\nimport { useRouter, usePathname } from '@/i18n/navigation'\r\n\r\ninterface LanguageSwitcherProps {\r\n    className?: string\r\n}\r\n\r\nconst languages = [\r\n    { value: 'en', flag: '🇺🇸' },\r\n    { value: 'ar', flag: '🇸🇦' },\r\n]\r\n\r\nexport function LanguageSwitcher({ className }: LanguageSwitcherProps) {\r\n    const t = useTranslations('header.language')\r\n    const locale = useLocale()\r\n    const router = useRouter()\r\n    const pathname = usePathname()\r\n\r\n    const currentLang = languages.find(lang => lang.value === locale)\r\n\r\n    const handleLanguageChange = (newLocale: string) => {\r\n        router.replace(pathname, { locale: newLocale })\r\n    }\r\n\r\n    return (\r\n        <Select value={locale} onValueChange={handleLanguageChange}>\r\n            <SelectTrigger className={cn(\"w-auto min-w-[80px] h-9\", className)}>\r\n                <div className=\"flex items-center gap-2\">\r\n                    <Globe className=\"h-4 w-4\" />\r\n                    <span className=\"text-sm\">{currentLang?.flag}</span>\r\n                    {/* <SelectValue placeholder={t('label')} /> */}\r\n                </div>\r\n            </SelectTrigger>\r\n            <SelectContent>\r\n                {languages.map((language) => (\r\n                    <SelectItem key={language.value} value={language.value}>\r\n                        <div className=\"flex items-center gap-2\">\r\n                            <span>{language.flag}</span>\r\n                            <span>{language.value === 'en' ? t('english') : t('arabic')}</span>\r\n                        </div>\r\n                    </SelectItem>\r\n                ))}\r\n            </SelectContent>\r\n        </Select>\r\n    )\r\n} "], "names": [], "mappings": ";;;;AAAA;AACA;AAOA;AACA;AAAA;AACA;;;;;;;;AAMA,MAAM,YAAY;IACd;QAAE,OAAO;QAAM,MAAM;IAAO;IAC5B;QAAE,OAAO;QAAM,MAAM;IAAO;CAC/B;AAEM,SAAS,iBAAiB,EAAE,SAAS,EAAyB;;IACjE,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,SAAS,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,4HAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,cAAc,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK;IAE1D,MAAM,uBAAuB,CAAC;QAC1B,OAAO,OAAO,CAAC,UAAU;YAAE,QAAQ;QAAU;IACjD;IAEA,qBACI,6LAAC,qIAAA,CAAA,SAAM;QAAC,OAAO;QAAQ,eAAe;;0BAClC,6LAAC,qIAAA,CAAA,gBAAa;gBAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;0BACpD,cAAA,6LAAC;oBAAI,WAAU;;sCACX,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;sCACjB,6LAAC;4BAAK,WAAU;sCAAW,aAAa;;;;;;;;;;;;;;;;;0BAIhD,6LAAC,qIAAA,CAAA,gBAAa;0BACT,UAAU,GAAG,CAAC,CAAC,yBACZ,6LAAC,qIAAA,CAAA,aAAU;wBAAsB,OAAO,SAAS,KAAK;kCAClD,cAAA,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;8CAAM,SAAS,IAAI;;;;;;8CACpB,6LAAC;8CAAM,SAAS,KAAK,KAAK,OAAO,EAAE,aAAa,EAAE;;;;;;;;;;;;uBAHzC,SAAS,KAAK;;;;;;;;;;;;;;;;AAUnD;GAjCgB;;QACF,yMAAA,CAAA,kBAAe;QACV,qKAAA,CAAA,YAAS;QACT,4HAAA,CAAA,YAAS;QACP,4HAAA,CAAA,cAAW;;;KAJhB", "debugId": null}}, {"offset": {"line": 3392, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/header/user-dropdown.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useRef, useEffect } from 'react'\r\nimport { useRouter } from '@/i18n/navigation'\r\nimport { useTranslations } from 'next-intl'\r\nimport { cn } from '@/lib/utils'\r\nimport {\r\n    Building,\r\n    HandCoins,\r\n    Calendar,\r\n    Bell,\r\n    Heart,\r\n    User,\r\n    LogOut,\r\n    ChevronDown\r\n} from 'lucide-react'\r\nimport { useUserStore } from '@/store/useUserStore'\r\nimport { logoutUser } from '@/actions/auth'\r\n\r\ninterface UserDropdownProps {\r\n    className?: string\r\n}\r\n\r\nexport function UserDropdown({ className }: UserDropdownProps) {\r\n    const [isOpen, setIsOpen] = useState(false)\r\n    const [isLoggingOut, setIsLoggingOut] = useState(false)\r\n    const dropdownRef = useRef<HTMLDivElement>(null)\r\n    const router = useRouter()\r\n    const t = useTranslations('header.user_menu')\r\n    const { user, clearUser } = useUserStore()\r\n\r\n    // Close dropdown when clicking outside\r\n    useEffect(() => {\r\n        function handleClickOutside(event: MouseEvent) {\r\n            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\r\n                setIsOpen(false)\r\n            }\r\n        }\r\n\r\n        document.addEventListener('mousedown', handleClickOutside)\r\n        return () => document.removeEventListener('mousedown', handleClickOutside)\r\n    }, [])\r\n\r\n    const menuItems = [\r\n        {\r\n            icon: Building,\r\n            label: t('my_ads'),\r\n            href: '/my-ads',\r\n            key: 'myads'\r\n        },\r\n        {\r\n            icon: HandCoins,\r\n            label: t('offers'),\r\n            href: '/offers',\r\n            key: 'offers'\r\n        },\r\n        {\r\n            icon: Calendar,\r\n            label: t('bookings'),\r\n            href: '/bookings',\r\n            key: 'bookings'\r\n        },\r\n        {\r\n            icon: Bell,\r\n            label: t('notifications'),\r\n            href: '/notifications',\r\n            key: 'notifications'\r\n        },\r\n        {\r\n            icon: Heart,\r\n            label: t('favorites'),\r\n            href: '/favorites',\r\n            key: 'favorites'\r\n        },\r\n    ]\r\n\r\n    const handleItemClick = (href: string) => {\r\n        router.push(href)\r\n        setIsOpen(false)\r\n    }\r\n\r\n    const handleLogout = async () => {\r\n        setIsLoggingOut(true)\r\n        try {\r\n            await logoutUser()\r\n            clearUser()\r\n            router.push('/')\r\n        } catch (error) {\r\n            console.error('Logout failed:', error)\r\n        } finally {\r\n            setIsLoggingOut(false)\r\n            setIsOpen(false)\r\n        }\r\n    }\r\n\r\n    // Get user's first name for display\r\n    const getUserDisplayName = () => {\r\n        if (!user?.name) return 'User'\r\n        return user.name.split(' ')[0]\r\n    }\r\n\r\n    return (\r\n        <div className={cn(\"relative\", className)} ref={dropdownRef}>\r\n            {/* User Avatar Button */}\r\n            <button\r\n                onClick={() => setIsOpen(!isOpen)}\r\n                className=\"flex items-center gap-2 h-8 px-2 rounded-full bg-primary/10 hover:bg-primary/20 transition-colors\"\r\n            >\r\n                <div className=\"h-6 w-6 rounded-full bg-primary/20 flex items-center justify-center\">\r\n                    <User className=\"h-3 w-3 text-primary\" />\r\n                </div>\r\n                <span className=\"text-sm font-medium text-primary hidden sm:block\">\r\n                    {getUserDisplayName()}\r\n                </span>\r\n                <ChevronDown className={cn(\r\n                    \"h-3 w-3 text-primary transition-transform duration-200\",\r\n                    isOpen && \"rotate-180\"\r\n                )} />\r\n            </button>\r\n\r\n            {/* Dropdown Menu */}\r\n            {isOpen && (\r\n                <div className=\"absolute right-0 top-full mt-2 w-48 bg-background border rounded-lg shadow-lg py-1 z-50\">\r\n                    {/* Welcome message */}\r\n                    <div className=\"px-4 py-2 text-sm text-muted-foreground border-b\">\r\n                        {t('welcome', { name: getUserDisplayName() })}\r\n                    </div>\r\n\r\n                    {menuItems.map((item) => {\r\n                        const Icon = item.icon\r\n                        return (\r\n                            <button\r\n                                key={item.key}\r\n                                onClick={() => handleItemClick(item.href)}\r\n                                className=\"flex cursor-pointer items-center gap-3 w-full px-4 py-2 text-sm hover:bg-accent hover:text-accent-foreground transition-colors\"\r\n                            >\r\n                                <Icon className=\"h-4 w-4\" />\r\n                                {item.label}\r\n                            </button>\r\n                        )\r\n                    })}\r\n\r\n                    {/* Divider */}\r\n                    <div className=\"border-t my-1\" />\r\n\r\n                    {/* Logout */}\r\n                    <button\r\n                        onClick={handleLogout}\r\n                        disabled={isLoggingOut}\r\n                        className=\"flex cursor-pointer items-center gap-3 w-full px-4 py-2 text-sm hover:bg-accent hover:text-accent-foreground transition-colors text-red-600 disabled:opacity-50\"\r\n                    >\r\n                        <LogOut className=\"h-4 w-4\" />\r\n                        {isLoggingOut ? t('logging_out') : t('logout')}\r\n                    </button>\r\n                </div>\r\n            )}\r\n        </div>\r\n    )\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;;;AAjBA;;;;;;;;AAuBO,SAAS,aAAa,EAAE,SAAS,EAAqB;;IACzD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,SAAS,CAAA,GAAA,4HAAA,CAAA,YAAS,AAAD;IACvB,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAEvC,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACN,SAAS,mBAAmB,KAAiB;gBACzC,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;oBAC5E,UAAU;gBACd;YACJ;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;0CAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;QAC3D;iCAAG,EAAE;IAEL,MAAM,YAAY;QACd;YACI,MAAM,6MAAA,CAAA,WAAQ;YACd,OAAO,EAAE;YACT,MAAM;YACN,KAAK;QACT;QACA;YACI,MAAM,mNAAA,CAAA,YAAS;YACf,OAAO,EAAE;YACT,MAAM;YACN,KAAK;QACT;QACA;YACI,MAAM,6MAAA,CAAA,WAAQ;YACd,OAAO,EAAE;YACT,MAAM;YACN,KAAK;QACT;QACA;YACI,MAAM,qMAAA,CAAA,OAAI;YACV,OAAO,EAAE;YACT,MAAM;YACN,KAAK;QACT;QACA;YACI,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO,EAAE;YACT,MAAM;YACN,KAAK;QACT;KACH;IAED,MAAM,kBAAkB,CAAC;QACrB,OAAO,IAAI,CAAC;QACZ,UAAU;IACd;IAEA,MAAM,eAAe;QACjB,gBAAgB;QAChB,IAAI;YACA,MAAM,CAAA,GAAA,yJAAA,CAAA,aAAU,AAAD;YACf;YACA,OAAO,IAAI,CAAC;QAChB,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,kBAAkB;QACpC,SAAU;YACN,gBAAgB;YAChB,UAAU;QACd;IACJ;IAEA,oCAAoC;IACpC,MAAM,qBAAqB;QACvB,IAAI,CAAC,MAAM,MAAM,OAAO;QACxB,OAAO,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;IAClC;IAEA,qBACI,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAY,KAAK;;0BAE5C,6LAAC;gBACG,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAEpB,6LAAC;wBAAK,WAAU;kCACX;;;;;;kCAEL,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACrB,0DACA,UAAU;;;;;;;;;;;;YAKjB,wBACG,6LAAC;gBAAI,WAAU;;kCAEX,6LAAC;wBAAI,WAAU;kCACV,EAAE,WAAW;4BAAE,MAAM;wBAAqB;;;;;;oBAG9C,UAAU,GAAG,CAAC,CAAC;wBACZ,MAAM,OAAO,KAAK,IAAI;wBACtB,qBACI,6LAAC;4BAEG,SAAS,IAAM,gBAAgB,KAAK,IAAI;4BACxC,WAAU;;8CAEV,6LAAC;oCAAK,WAAU;;;;;;gCACf,KAAK,KAAK;;2BALN,KAAK,GAAG;;;;;oBAQzB;kCAGA,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBACG,SAAS;wBACT,UAAU;wBACV,WAAU;;0CAEV,6LAAC,6MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BACjB,eAAe,EAAE,iBAAiB,EAAE;;;;;;;;;;;;;;;;;;;AAM7D;GAvIgB;;QAIG,4HAAA,CAAA,YAAS;QACd,yMAAA,CAAA,kBAAe;QACG,+HAAA,CAAA,eAAY;;;KAN5B", "debugId": null}}, {"offset": {"line": 3631, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/header/mobile-bottom-bar.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useRouter } from '@/i18n/navigation'\r\nimport { useTranslations } from 'next-intl'\r\nimport { cn } from '@/lib/utils'\r\nimport { useEffect, useState, useCallback } from 'react'\r\nimport {\r\n    Building,\r\n    HandCoins,\r\n    Bell,\r\n    Heart,\r\n    Plus\r\n} from 'lucide-react'\r\n\r\ninterface MobileBottomBarProps {\r\n    className?: string\r\n}\r\n\r\nexport function MobileBottomBar({ className }: MobileBottomBarProps) {\r\n    const router = useRouter()\r\n    const t = useTranslations('header.user_menu')\r\n    const tButtons = useTranslations('header.buttons')\r\n\r\n    const [isVisible, setIsVisible] = useState(true)\r\n    const [lastScrollY, setLastScrollY] = useState(0)\r\n    const [scrollTimeout, setScrollTimeout] = useState<NodeJS.Timeout | null>(null)\r\n\r\n    const handleNavigation = (href: string) => {\r\n        router.push(href)\r\n    }\r\n\r\n    const handleScroll = useCallback(() => {\r\n        const currentScrollY = window.scrollY\r\n        const documentHeight = document.documentElement.scrollHeight\r\n        const windowHeight = window.innerHeight\r\n        const footerHeight = 80 // Approximate footer height, adjust as needed\r\n\r\n        // Check if we're near the bottom (footer area)\r\n        const isNearFooter = currentScrollY + windowHeight >= documentHeight - footerHeight\r\n\r\n        if (isNearFooter) {\r\n            setIsVisible(false)\r\n            return\r\n        }\r\n\r\n        // Always show at the top of the page\r\n        if (currentScrollY <= 50) {\r\n            setIsVisible(true)\r\n        }\r\n        // Hide when scrolling down, show when scrolling up\r\n        else if (currentScrollY > lastScrollY && currentScrollY > 100) {\r\n            setIsVisible(false)\r\n        } else if (currentScrollY < lastScrollY) {\r\n            setIsVisible(true)\r\n        }\r\n\r\n        setLastScrollY(currentScrollY)\r\n\r\n        // Clear existing timeout\r\n        if (scrollTimeout) {\r\n            clearTimeout(scrollTimeout)\r\n        }\r\n\r\n        // Show bar when scrolling stops (but not in footer area)\r\n        const newTimeout = setTimeout(() => {\r\n            if (!isNearFooter) {\r\n                setIsVisible(true)\r\n            }\r\n        }, 150)\r\n\r\n        setScrollTimeout(newTimeout)\r\n    }, [lastScrollY, scrollTimeout])\r\n\r\n    useEffect(() => {\r\n        window.addEventListener('scroll', handleScroll, { passive: true })\r\n\r\n        return () => {\r\n            window.removeEventListener('scroll', handleScroll)\r\n            if (scrollTimeout) {\r\n                clearTimeout(scrollTimeout)\r\n            }\r\n        }\r\n    }, [handleScroll, scrollTimeout])\r\n\r\n    const navItems = [\r\n        {\r\n            icon: Building,\r\n            label: t('my_ads'),\r\n            href: '/my-ads',\r\n            key: 'myads'\r\n        },\r\n        {\r\n            icon: HandCoins,\r\n            label: t('offers'),\r\n            href: '/offers',\r\n            key: 'offers'\r\n        },\r\n        {\r\n            icon: Bell,\r\n            label: t('notifications'),\r\n            href: '/notifications',\r\n            key: 'notifications'\r\n        },\r\n        {\r\n            icon: Heart,\r\n            label: t('favorites'),\r\n            href: '/favorites',\r\n            key: 'favorites'\r\n        },\r\n    ]\r\n\r\n    return (\r\n        <div className={cn(\r\n            \"fixed bottom-0 left-0 right-0 z-50 bg-background border-t md:hidden transition-transform duration-300 ease-in-out\",\r\n            isVisible ? \"translate-y-0\" : \"translate-y-full\",\r\n            className\r\n        )}>\r\n            <div className=\"flex items-center justify-center h-16\">\r\n                <div className=\"flex items-center justify-around w-full max-w-md px-4\">\r\n                    {/* First two nav items */}\r\n                    {navItems.slice(0, 2).map((item) => {\r\n                        const Icon = item.icon\r\n                        return (\r\n                            <button\r\n                                key={item.key}\r\n                                onClick={() => handleNavigation(item.href)}\r\n                                className=\"flex flex-col items-center justify-center gap-1 p-2 rounded-lg hover:bg-accent transition-colors\"\r\n                            >\r\n                                <Icon className=\"h-5 w-5 text-foreground\" />\r\n                                <span className=\"text-xs text-muted-foreground\">{item.label}</span>\r\n                            </button>\r\n                        )\r\n                    })}\r\n\r\n                    {/* Add Property Button in the middle */}\r\n                    <button\r\n                        onClick={() => handleNavigation('/add-property')}\r\n                        className=\"flex items-center justify-center w-12 h-12 bg-primary text-primary-foreground rounded-full hover:bg-primary/90 transition-colors shadow-lg\"\r\n                        title={tButtons('add_property')}\r\n                    >\r\n                        <Plus className=\"h-6 w-6\" />\r\n                    </button>\r\n\r\n                    {/* Last two nav items */}\r\n                    {navItems.slice(2).map((item) => {\r\n                        const Icon = item.icon\r\n                        return (\r\n                            <button\r\n                                key={item.key}\r\n                                onClick={() => handleNavigation(item.href)}\r\n                                className=\"flex flex-col items-center justify-center gap-1 p-2 rounded-lg hover:bg-accent transition-colors\"\r\n                            >\r\n                                <Icon className=\"h-5 w-5 text-foreground\" />\r\n                                <span className=\"text-xs text-muted-foreground\">{item.label}</span>\r\n                            </button>\r\n                        )\r\n                    })}\r\n                </div>\r\n            </div>\r\n        </div>\r\n    )\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAkBO,SAAS,gBAAgB,EAAE,SAAS,EAAwB;;IAC/D,MAAM,SAAS,CAAA,GAAA,4HAAA,CAAA,YAAS,AAAD;IACvB,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAEjC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAE1E,MAAM,mBAAmB,CAAC;QACtB,OAAO,IAAI,CAAC;IAChB;IAEA,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE;YAC7B,MAAM,iBAAiB,OAAO,OAAO;YACrC,MAAM,iBAAiB,SAAS,eAAe,CAAC,YAAY;YAC5D,MAAM,eAAe,OAAO,WAAW;YACvC,MAAM,eAAe,GAAG,8CAA8C;;YAEtE,+CAA+C;YAC/C,MAAM,eAAe,iBAAiB,gBAAgB,iBAAiB;YAEvE,IAAI,cAAc;gBACd,aAAa;gBACb;YACJ;YAEA,qCAAqC;YACrC,IAAI,kBAAkB,IAAI;gBACtB,aAAa;YACjB,OAEK,IAAI,iBAAiB,eAAe,iBAAiB,KAAK;gBAC3D,aAAa;YACjB,OAAO,IAAI,iBAAiB,aAAa;gBACrC,aAAa;YACjB;YAEA,eAAe;YAEf,yBAAyB;YACzB,IAAI,eAAe;gBACf,aAAa;YACjB;YAEA,yDAAyD;YACzD,MAAM,aAAa;wEAAW;oBAC1B,IAAI,CAAC,cAAc;wBACf,aAAa;oBACjB;gBACJ;uEAAG;YAEH,iBAAiB;QACrB;oDAAG;QAAC;QAAa;KAAc;IAE/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACN,OAAO,gBAAgB,CAAC,UAAU,cAAc;gBAAE,SAAS;YAAK;YAEhE;6CAAO;oBACH,OAAO,mBAAmB,CAAC,UAAU;oBACrC,IAAI,eAAe;wBACf,aAAa;oBACjB;gBACJ;;QACJ;oCAAG;QAAC;QAAc;KAAc;IAEhC,MAAM,WAAW;QACb;YACI,MAAM,6MAAA,CAAA,WAAQ;YACd,OAAO,EAAE;YACT,MAAM;YACN,KAAK;QACT;QACA;YACI,MAAM,mNAAA,CAAA,YAAS;YACf,OAAO,EAAE;YACT,MAAM;YACN,KAAK;QACT;QACA;YACI,MAAM,qMAAA,CAAA,OAAI;YACV,OAAO,EAAE;YACT,MAAM;YACN,KAAK;QACT;QACA;YACI,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO,EAAE;YACT,MAAM;YACN,KAAK;QACT;KACH;IAED,qBACI,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACb,qHACA,YAAY,kBAAkB,oBAC9B;kBAEA,cAAA,6LAAC;YAAI,WAAU;sBACX,cAAA,6LAAC;gBAAI,WAAU;;oBAEV,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;wBACvB,MAAM,OAAO,KAAK,IAAI;wBACtB,qBACI,6LAAC;4BAEG,SAAS,IAAM,iBAAiB,KAAK,IAAI;4BACzC,WAAU;;8CAEV,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;oCAAK,WAAU;8CAAiC,KAAK,KAAK;;;;;;;2BALtD,KAAK,GAAG;;;;;oBAQzB;kCAGA,6LAAC;wBACG,SAAS,IAAM,iBAAiB;wBAChC,WAAU;wBACV,OAAO,SAAS;kCAEhB,cAAA,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;oBAInB,SAAS,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;wBACpB,MAAM,OAAO,KAAK,IAAI;wBACtB,qBACI,6LAAC;4BAEG,SAAS,IAAM,iBAAiB,KAAK,IAAI;4BACzC,WAAU;;8CAEV,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;oCAAK,WAAU;8CAAiC,KAAK,KAAK;;;;;;;2BALtD,KAAK,GAAG;;;;;oBAQzB;;;;;;;;;;;;;;;;;AAKpB;GA/IgB;;QACG,4HAAA,CAAA,YAAS;QACd,yMAAA,CAAA,kBAAe;QACR,yMAAA,CAAA,kBAAe;;;KAHpB", "debugId": null}}, {"offset": {"line": 3862, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/header/index.tsx"], "sourcesContent": ["'use client'\r\nimport { cn } from '@/lib/utils'\r\nimport { Logo } from './logo'\r\nimport { SearchBar } from './search-bar'\r\nimport { AuthButtons } from './auth-buttons'\r\nimport { ConditionalButtons } from './conditional-buttons'\r\nimport { LanguageSwitcher } from './language-switcher'\r\nimport { UserDropdown } from './user-dropdown'\r\nimport { MobileBottomBar } from './mobile-bottom-bar'\r\nimport { useUserStore } from '@/store/useUserStore'\r\nimport { useRouter } from '@/i18n/navigation'\r\n\r\ninterface HeaderProps {\r\n    className?: string\r\n}\r\n\r\nexport function Header({ className }: HeaderProps) {\r\n    const router = useRouter()\r\n    // Get user data from the main user store (updated by AuthProvider)\r\n    const { isAuthenticated } = useUserStore()\r\n\r\n    const handleLogoClick = () => {\r\n        router.push('/')\r\n    }\r\n\r\n    return (\r\n        <>\r\n            <header\r\n                className={cn(\r\n                    \"sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\r\n                    className\r\n                )}\r\n            >\r\n                <div className=\"flex h-20 items-center justify-center mx-auto\">\r\n                    {/* Centered Content */}\r\n                    <div className=\"flex items-center justify-between gap-8 w-full max-w-7xl\">\r\n                        {/* Logo */}\r\n                        <div className=\"flex items-center\">\r\n                            <Logo onClick={handleLogoClick} />\r\n                        </div>\r\n\r\n                        {/* Center Section: Search Bar */}\r\n                        <div className=\"hidden md:flex flex-1 justify-center max-w-2xl\">\r\n                            <SearchBar />\r\n                        </div>\r\n\r\n                        {/* Right Section: Actions */}\r\n                        <div className=\"flex items-center gap-3\">\r\n                            {/* Conditional Buttons (Add Property / Dashboard) */}\r\n                            <ConditionalButtons className=\"hidden sm:flex\" />\r\n\r\n                            {/* Authentication Buttons or User Actions */}\r\n                            {!isAuthenticated ? (\r\n                                <AuthButtons />\r\n                            ) : (\r\n                                <div className=\"flex items-center gap-2\">\r\n                                    <ConditionalButtons className=\"sm:hidden\" />\r\n                                    {/* User Dropdown Menu */}\r\n                                    <UserDropdown />\r\n                                </div>\r\n                            )}\r\n\r\n                            {/* Language Switcher */}\r\n                            <LanguageSwitcher />\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Mobile Search Bar */}\r\n                <div className=\"md:hidden border-t bg-background px-4 py-3\">\r\n                    <SearchBar isMobile={true} />\r\n                </div>\r\n            </header>\r\n\r\n            {/* Mobile Bottom Bar - Only show for authenticated users */}\r\n            {isAuthenticated && <MobileBottomBar />}\r\n        </>\r\n    )\r\n}\r\n\r\n// Export all components for external use\r\nexport { Logo } from './logo'\r\nexport { SearchBar } from './search-bar'\r\nexport { AuthButtons } from './auth-buttons'\r\nexport { ConditionalButtons } from './conditional-buttons'\r\nexport { LanguageSwitcher } from './language-switcher'\r\nexport { UserDropdown } from './user-dropdown'\r\nexport { MobileBottomBar } from './mobile-bottom-bar' "], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;;AAgBO,SAAS,OAAO,EAAE,SAAS,EAAe;;IAC7C,MAAM,SAAS,CAAA,GAAA,4HAAA,CAAA,YAAS,AAAD;IACvB,mEAAmE;IACnE,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAEvC,MAAM,kBAAkB;QACpB,OAAO,IAAI,CAAC;IAChB;IAEA,qBACI;;0BACI,6LAAC;gBACG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,gHACA;;kCAGJ,6LAAC;wBAAI,WAAU;kCAEX,cAAA,6LAAC;4BAAI,WAAU;;8CAEX,6LAAC;oCAAI,WAAU;8CACX,cAAA,6LAAC,uIAAA,CAAA,OAAI;wCAAC,SAAS;;;;;;;;;;;8CAInB,6LAAC;oCAAI,WAAU;8CACX,cAAA,6LAAC,gJAAA,CAAA,YAAS;;;;;;;;;;8CAId,6LAAC;oCAAI,WAAU;;sDAEX,6LAAC,yJAAA,CAAA,qBAAkB;4CAAC,WAAU;;;;;;wCAG7B,CAAC,gCACE,6LAAC,kJAAA,CAAA,cAAW;;;;iEAEZ,6LAAC;4CAAI,WAAU;;8DACX,6LAAC,yJAAA,CAAA,qBAAkB;oDAAC,WAAU;;;;;;8DAE9B,6LAAC,mJAAA,CAAA,eAAY;;;;;;;;;;;sDAKrB,6LAAC,uJAAA,CAAA,mBAAgB;;;;;;;;;;;;;;;;;;;;;;kCAM7B,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC,gJAAA,CAAA,YAAS;4BAAC,UAAU;;;;;;;;;;;;;;;;;YAK5B,iCAAmB,6LAAC,0JAAA,CAAA,kBAAe;;;;;;;AAGhD;GA9DgB;;QACG,4HAAA,CAAA,YAAS;QAEI,+HAAA,CAAA,eAAY;;;KAH5B", "debugId": null}}, {"offset": {"line": 4104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/layout/main-content.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { cn } from '@/lib/utils'\r\nimport { useUserStore } from '@/store/useUserStore'\r\n\r\ninterface MainContentProps {\r\n    children: React.ReactNode\r\n    className?: string\r\n}\r\n\r\nexport function MainContent({ children, className }: MainContentProps) {\r\n    const { isAuthenticated } = useUserStore()\r\n\r\n    return (\r\n        <main\r\n            className={cn(\r\n                \"flex-1 w-full\",\r\n                // Add bottom padding on mobile when user is authenticated (for mobile bottom bar)\r\n                isAuthenticated && \"pb-20 md:pb-0\",\r\n                className\r\n            )}\r\n        >\r\n            {children}\r\n        </main>\r\n    )\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAUO,SAAS,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAoB;;IACjE,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAEvC,qBACI,6LAAC;QACG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,iBACA,kFAAkF;QAClF,mBAAmB,iBACnB;kBAGH;;;;;;AAGb;GAfgB;;QACgB,+HAAA,CAAA,eAAY;;;KAD5B", "debugId": null}}]}
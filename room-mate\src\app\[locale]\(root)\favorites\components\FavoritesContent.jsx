'use client'

import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useTranslations } from 'next-intl'

import { TypeToggle } from '@/components/homepage/type-toggle'
import { useFavoriteProperties } from '@/hooks/useFavoriteProperties'
import { useFavoritePersons } from '@/hooks/useFavoritePersons'
import { getFavoriteProperties, toggleFavorite as togglePropertyFavorite } from '@/actions/properties'
import { getFavoritePersons, toggleFavorite as togglePersonFavorite } from '@/actions/persons'
import { PropertyCard } from '@/components/homepage/property-card'
import { cn } from '@/lib/utils'


export function FavoritesContent() {
  const t = useTranslations('favorites page')
  const queryClient = useQueryClient()
  const [viewType, setViewType] = useState('properties')
  const [viewMode] = useState('grid')

  const { isPropertyFavorite } = useFavoriteProperties()
  const { isPersonFavorite } = useFavoritePersons()

  // Favorite Properties query
  const propertiesQuery = useQuery({
    queryKey: ['favorites', 'properties'],
    queryFn: getFavoriteProperties,
    enabled: viewType === 'properties',
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  // Favorite Persons query
  const personsQuery = useQuery({
    queryKey: ['favorites', 'persons'],
    queryFn: getFavoritePersons,
    enabled: viewType === 'persons',
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  // Mutations for favorite toggle
  const togglePropertyFavoriteMutation = useMutation({
    mutationFn: (id) => togglePropertyFavorite(id),
    onSuccess: () => {
      // Invalidate and refetch favorites
      queryClient.invalidateQueries({ queryKey: ['favorites', 'properties'] })
    },
    onError: (error) => {
      console.error('Failed to toggle property favorite:', error)
    }
  })

  const togglePersonFavoriteMutation = useMutation({
    mutationFn: (id) => togglePersonFavorite(id),
    onSuccess: () => {
      // Invalidate and refetch favorites
      queryClient.invalidateQueries({ queryKey: ['favorites', 'persons'] })
    },
    onError: (error) => {
      console.error('Failed to toggle person favorite:', error)
    }
  })

  // Handle different API response structures for favorites
  const currentData = viewType === 'properties'
    ? propertiesQuery.data?.data?.data || []
    : personsQuery.data?.data?.data || []

  // Handle favorite toggle
  const handleFavoriteToggle = (id) => {
    if (viewType === 'properties') {
      togglePropertyFavoriteMutation.mutate(id)
    } else {
      togglePersonFavoriteMutation.mutate(id)
    }
  }

  // Check if data is loading
  const isLoading = viewType === 'properties' ? propertiesQuery.isLoading : personsQuery.isLoading

  return (
    <div className="flex flex-col gap-8">
      <div className="flex items-center justify-between">
        <TypeToggle
          value={viewType}
          onChange={setViewType}
          options={[
            { value: 'properties', label: t('properties') },
            { value: 'persons', label: t('persons') },
          ]}
        />
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center py-8">
          <div className="text-muted-foreground">{t('loading')}</div>
        </div>
      ) : currentData.length === 0 ? (
        <div className="flex justify-center items-center py-8">
          <div className="text-muted-foreground">
            {viewType === 'properties' ? t('noFavoriteProperties') : t('noFavoritePersons')}
          </div>
        </div>
      ) : (
        <div className={cn(
          "grid gap-6",
          viewMode === 'grid'
            ? "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
            : "grid-cols-1"
        )}>
          {currentData.map((item) => (
            <PropertyCard
              key={item.id}
              property={item}
              onFavoriteToggle={handleFavoriteToggle}
              viewType={viewType}
              isFavoriteLoading={viewType === 'properties' ? togglePropertyFavoriteMutation.isPending : togglePersonFavoriteMutation.isPending}
              isFavorite={viewType === 'properties' ? isPropertyFavorite(item.id) : isPersonFavorite(item.id)}
            />
          ))}
        </div>
      )}
    </div>
  )
}
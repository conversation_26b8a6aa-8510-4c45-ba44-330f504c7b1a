"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.responseExamples = exports.apiEndpointExamples = exports.validationExamples = exports.queryExamples = exports.changePasswordExample = exports.updateUserExample = exports.minimalUserExample = exports.createUserExample = void 0;
exports.createUserExample = {
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'Strong<PERSON>@ssw0rd123',
    phone: '+************',
    smoker: false,
    age: '28',
    gender: 'male',
    nationality: 'Egyptian',
    occupation: 'Marketing Manager',
    country: 'Egypt',
};
exports.minimalUserExample = {
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'SecureP@ss456',
    phone: '+************',
    smoker: false,
    age: '28',
    gender: 'male',
    nationality: 'Egyptian',
    occupation: 'Marketing Manager',
    country: 'Egypt',
};
exports.updateUserExample = {
    name: '<PERSON>',
    phone: '+201987654321',
    occupation: 'Senior Marketing Manager',
    nationality: 'Egyptian',
};
exports.changePasswordExample = {
    currentPassword: 'Strong<PERSON>@ssw0rd123',
    newPassword: 'NewSecureP@ss789',
};
exports.queryExamples = {
    getAllUsers: {
        page: 1,
        limit: 10,
        sortBy: 'created_at',
        sortOrder: 'desc',
    },
    searchByName: {
        search: 'Ahmed',
        page: 1,
        limit: 5,
        sortBy: 'name',
        sortOrder: 'asc',
    },
    filterByGender: {
        gender: 'male',
        page: 1,
        limit: 20,
    },
    filterSmokers: {
        smoker: true,
        page: 1,
        limit: 10,
    },
    filterByAge: {
        ageFrom: '25',
        ageTo: '35',
        page: 1,
        limit: 15,
    },
    complexFilter: {
        search: 'Engineer',
        gender: 'male',
        smoker: false,
        ageFrom: '20',
        ageTo: '40',
        page: 1,
        limit: 10,
        sortBy: 'created_at',
        sortOrder: 'desc',
    },
};
exports.validationExamples = {
    validGenders: ['male', 'female', 'other'],
    validAges: ['18', '25', '30', '45', '60'],
    validPhones: [
        '+1234567890',
        '+************',
        '+44 20 7946 0958',
        '+33 1 42 86 83 26',
    ],
    validPasswords: [
        'MySecureP@ss123',
        'StrongP@ssw0rd!',
        'Complex123@Password',
        'ValidP@ssword789',
    ],
    invalidExamples: [
        { gender: 'unknown' },
        { gender: 'apache-helicopter' },
        { phone: '123' },
        { phone: 'not-a-phone' },
        { phone: '+' },
        { password: '123456' },
        { password: 'password' },
        { password: 'P@ss' },
        { name: 'A' },
        { name: '' },
    ],
};
exports.apiEndpointExamples = {
    create: {
        method: 'POST',
        url: '/api/users',
        headers: { Authorization: 'Bearer admin-jwt-token' },
        body: exports.createUserExample,
    },
    getAll: {
        method: 'GET',
        url: '/api/users?page=1&limit=10&sortBy=created_at&sortOrder=desc',
        headers: { Authorization: 'Bearer admin-jwt-token' },
    },
    search: {
        method: 'GET',
        url: '/api/users?search=Ahmed&gender=male&page=1&limit=5',
        headers: { Authorization: 'Bearer admin-jwt-token' },
    },
    getStats: {
        method: 'GET',
        url: '/api/users/stats',
        headers: { Authorization: 'Bearer admin-jwt-token' },
    },
    getProfile: {
        method: 'GET',
        url: '/api/users/profile',
        headers: { Authorization: 'Bearer user-jwt-token' },
    },
    getById: {
        method: 'GET',
        url: '/api/users/user-uuid-123',
        headers: { Authorization: 'Bearer admin-jwt-token' },
    },
    updateProfile: {
        method: 'PATCH',
        url: '/api/users/profile',
        headers: { Authorization: 'Bearer user-jwt-token' },
        body: exports.updateUserExample,
    },
    updateById: {
        method: 'PATCH',
        url: '/api/users/user-uuid-123',
        headers: { Authorization: 'Bearer admin-jwt-token' },
        body: exports.updateUserExample,
    },
    changePassword: {
        method: 'PATCH',
        url: '/api/users/change-password',
        headers: { Authorization: 'Bearer user-jwt-token' },
        body: exports.changePasswordExample,
    },
    delete: {
        method: 'DELETE',
        url: '/api/users/user-uuid-123',
        headers: { Authorization: 'Bearer admin-jwt-token' },
    },
};
exports.responseExamples = {
    userResponse: {
        id: 'user-uuid-123',
        name: 'Ahmed Hassan',
        email: '<EMAIL>',
        phone: '+************',
        smoker: false,
        age: '28',
        gender: 'male',
        nationality: 'Egyptian',
        occupation: 'Marketing Manager',
        avatar: 'https://example.com/avatars/ahmed.jpg',
        emailVerified: true,
        role: 'user',
        isActive: true,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-15T10:30:00.000Z',
        properties: [
            {
                id: 'property-uuid-456',
                title: 'Beautiful apartment in Downtown',
                type: 'APARTMENT',
                city: 'Cairo',
                country: 'Egypt',
            },
        ],
        offers: [
            {
                id: 'offer-uuid-789',
                message: 'Interested in your property',
                price: '5000',
                status: 'PENDING',
            },
        ],
    },
    paginatedResponse: {
        data: [],
        total: 45,
        page: 1,
        limit: 10,
        totalPages: 5,
        hasNext: true,
        hasPrev: false,
    },
    statsResponse: {
        totalUsers: 1250,
        activeUsers: 1180,
        verifiedUsers: 980,
        usersByGender: {
            male: 720,
            female: 480,
            other: 50,
        },
        usersByAge: {
            '18-25': 420,
            '26-35': 580,
            '36-45': 180,
            '46+': 70,
        },
        smokersCount: 280,
        nonSmokersCount: 970,
        usersWithProperties: 340,
        usersWithOffers: 580,
        newUsersThisMonth: 85,
    },
    updateSuccessResponse: {
        success: true,
        message: 'User profile updated successfully',
        data: {},
    },
    passwordChangeResponse: {
        success: true,
        message: 'Password changed successfully',
    },
};
//# sourceMappingURL=users.examples.js.map
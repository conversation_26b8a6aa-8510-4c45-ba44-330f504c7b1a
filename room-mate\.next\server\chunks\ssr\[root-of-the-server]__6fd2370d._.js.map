{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/roboto_ac6cc059.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"roboto_ac6cc059-module__7papDG__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/roboto_ac6cc059.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Roboto%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22weight%22:[%22400%22,%22500%22,%22700%22]}],%22variableName%22:%22roboto%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Roboto', 'Roboto Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,sJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,sJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,sJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/cairo_6821035d.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"cairo_6821035d-module__3MnmbG__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/cairo_6821035d.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Cairo%22,%22arguments%22:[{%22subsets%22:[%22arabic%22],%22weight%22:[%22400%22,%22500%22,%22700%22]}],%22variableName%22:%22cairo%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Cairo', 'Cairo Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/i18n/routing.ts"], "sourcesContent": ["import { defineRouting } from \"next-intl/routing\";\r\n\r\nexport const routing = defineRouting({\r\n  // A list of all locales that are supported\r\n  locales: [\"en\", \"ar\"],\r\n\r\n  // Used when no locale matches\r\n  defaultLocale: \"ar\",\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,UAAU,CAAA,GAAA,kOAAA,CAAA,gBAAa,AAAD,EAAE;IACnC,2CAA2C;IAC3C,SAAS;QAAC;QAAM;KAAK;IAErB,8BAA8B;IAC9B,eAAe;AACjB", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/i18n/request.ts"], "sourcesContent": ["import { getRequestConfig } from \"next-intl/server\";\r\nimport { hasLocale } from \"next-intl\";\r\nimport { routing } from \"./routing\";\r\n\r\nexport default getRequestConfig(async ({ requestLocale }) => {\r\n  // Typically corresponds to the `[locale]` segment\r\n  const requested = await requestLocale;\r\n  const locale = hasLocale(routing.locales, requested)\r\n    ? requested\r\n    : routing.defaultLocale;\r\n\r\n  return {\r\n    locale,\r\n    messages: (await import(`../../messages/${locale}.json`)).default,\r\n  };\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;uCAEe,CAAA,GAAA,0PAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,EAAE,aAAa,EAAE;IACtD,kDAAkD;IAClD,MAAM,YAAY,MAAM;IACxB,MAAM,SAAS,CAAA,GAAA,iLAAA,CAAA,YAAS,AAAD,EAAE,sHAAA,CAAA,UAAO,CAAC,OAAO,EAAE,aACtC,YACA,sHAAA,CAAA,UAAO,CAAC,aAAa;IAEzB,OAAO;QACL;QACA,UAAU,CAAC;;;;;;;;;kBAAa,CAAC,eAAe,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IACnE;AACF", "debugId": null}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/providers/query-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/providers/query-provider.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/query-provider.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/providers/query-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/providers/query-provider.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/query-provider.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/providers/auth-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/auth-provider.tsx <module evaluation>\",\n    \"AuthProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,iEACA", "debugId": null}}, {"offset": {"line": 172, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/providers/auth-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/auth-provider.tsx\",\n    \"AuthProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,6CACA", "debugId": null}}, {"offset": {"line": 186, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/header/index.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuthButtons = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthButtons() from the server but AuthButtons is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/header/index.tsx <module evaluation>\",\n    \"AuthButtons\",\n);\nexport const ConditionalButtons = registerClientReference(\n    function() { throw new Error(\"Attempted to call ConditionalButtons() from the server but ConditionalButtons is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/header/index.tsx <module evaluation>\",\n    \"ConditionalButtons\",\n);\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Head<PERSON>() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/header/index.tsx <module evaluation>\",\n    \"Header\",\n);\nexport const LanguageSwitcher = registerClientReference(\n    function() { throw new Error(\"Attempted to call LanguageSwitcher() from the server but LanguageSwitcher is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/header/index.tsx <module evaluation>\",\n    \"LanguageSwitcher\",\n);\nexport const Logo = registerClientReference(\n    function() { throw new Error(\"Attempted to call Logo() from the server but Logo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/header/index.tsx <module evaluation>\",\n    \"Logo\",\n);\nexport const MobileBottomBar = registerClientReference(\n    function() { throw new Error(\"Attempted to call MobileBottomBar() from the server but MobileBottomBar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/header/index.tsx <module evaluation>\",\n    \"MobileBottomBar\",\n);\nexport const SearchBar = registerClientReference(\n    function() { throw new Error(\"Attempted to call SearchBar() from the server but SearchBar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/header/index.tsx <module evaluation>\",\n    \"SearchBar\",\n);\nexport const UserDropdown = registerClientReference(\n    function() { throw new Error(\"Attempted to call UserDropdown() from the server but UserDropdown is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/header/index.tsx <module evaluation>\",\n    \"UserDropdown\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,iEACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,iEACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,iEACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,iEACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,iEACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,iEACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,iEACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,iEACA", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/header/index.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuthButtons = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthButtons() from the server but AuthButtons is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/header/index.tsx\",\n    \"AuthButtons\",\n);\nexport const ConditionalButtons = registerClientReference(\n    function() { throw new Error(\"Attempted to call ConditionalButtons() from the server but ConditionalButtons is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/header/index.tsx\",\n    \"ConditionalButtons\",\n);\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Header() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/header/index.tsx\",\n    \"Header\",\n);\nexport const LanguageSwitcher = registerClientReference(\n    function() { throw new Error(\"Attempted to call LanguageSwitcher() from the server but LanguageSwitcher is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/header/index.tsx\",\n    \"LanguageSwitcher\",\n);\nexport const Logo = registerClientReference(\n    function() { throw new Error(\"Attempted to call Logo() from the server but Logo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/header/index.tsx\",\n    \"Logo\",\n);\nexport const MobileBottomBar = registerClientReference(\n    function() { throw new Error(\"Attempted to call MobileBottomBar() from the server but MobileBottomBar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/header/index.tsx\",\n    \"MobileBottomBar\",\n);\nexport const SearchBar = registerClientReference(\n    function() { throw new Error(\"Attempted to call SearchBar() from the server but SearchBar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/header/index.tsx\",\n    \"SearchBar\",\n);\nexport const UserDropdown = registerClientReference(\n    function() { throw new Error(\"Attempted to call UserDropdown() from the server but UserDropdown is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/header/index.tsx\",\n    \"UserDropdown\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,6CACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,6CACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,6CACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,6CACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,6CACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,6CACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6CACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,6CACA", "debugId": null}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/footer/index.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { useTranslations } from 'next-intl';\r\n\r\nexport const Footer = () => {\r\n    const t = useTranslations('footer');\r\n    const currentYear = new Date().getFullYear();\r\n\r\n    return (\r\n        <footer className=\"bg-gray-900 border-t border-gray-700 mt-auto\">\r\n            <div className=\"max-w-7xl mx-auto px-4 py-4\">\r\n                <div className=\"flex justify-between items-center\">\r\n                    <p className=\"text-sm text-gray-300\">\r\n                        {t('copyright', { year: currentYear })}\r\n                    </p>\r\n                    <p className=\"text-sm text-gray-400\">\r\n                        {t('designedBy')}{' '}\r\n                        <a\r\n                            href=\"#\"\r\n                            className=\"text-blue-400 hover:text-blue-300 font-medium transition-colors\"\r\n                            target=\"_blank\"\r\n                            rel=\"noopener noreferrer\"\r\n                        >\r\n                            {t('developerName')}\r\n                        </a>\r\n                    </p>\r\n                </div>\r\n            </div>\r\n        </footer>\r\n    );\r\n}; "], "names": [], "mappings": ";;;;AACA;;;AAEO,MAAM,SAAS;IAClB,MAAM,IAAI,CAAA,GAAA,8OAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,qBACI,8OAAC;QAAO,WAAU;kBACd,cAAA,8OAAC;YAAI,WAAU;sBACX,cAAA,8OAAC;gBAAI,WAAU;;kCACX,8OAAC;wBAAE,WAAU;kCACR,EAAE,aAAa;4BAAE,MAAM;wBAAY;;;;;;kCAExC,8OAAC;wBAAE,WAAU;;4BACR,EAAE;4BAAe;0CAClB,8OAAC;gCACG,MAAK;gCACL,WAAU;gCACV,QAAO;gCACP,KAAI;0CAEH,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/B", "debugId": null}}, {"offset": {"line": 362, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/layout/main-content.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const MainContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call MainContent() from the server but MainContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/main-content.tsx <module evaluation>\",\n    \"MainContent\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,wEACA", "debugId": null}}, {"offset": {"line": 376, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/layout/main-content.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const MainContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call MainContent() from the server but MainContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/main-content.tsx\",\n    \"MainContent\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,oDACA", "debugId": null}}, {"offset": {"line": 390, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 400, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/app/%5Blocale%5D/layout.tsx"], "sourcesContent": ["import type { Metada<PERSON> } from \"next\";\r\nimport { Roboto, Cairo } from \"next/font/google\";\r\nimport \"./globals.css\";\r\nimport { NextIntlClientProvider, hasLocale } from \"next-intl\";\r\nimport { notFound } from \"next/navigation\";\r\nimport { routing } from \"@/i18n/routing\";\r\nimport { setRequestLocale } from \"next-intl/server\";\r\nimport QueryProvider from \"@/providers/query-provider\";\r\nimport { AuthProvider } from \"@/providers/auth-provider\";\r\nimport { Header } from \"@/components/header\";\r\nimport { Footer } from \"@/components/footer\";\r\nimport { MainContent } from \"@/components/layout/main-content\";\r\n\r\nconst roboto = Roboto({\r\n  subsets: [\"latin\"],\r\n  weight: [\"400\", \"500\", \"700\"],\r\n});\r\n\r\nconst cairo = Cairo({\r\n  subsets: [\"arabic\"],\r\n  weight: [\"400\", \"500\", \"700\"],\r\n});\r\n\r\nexport const metadata: Metadata = {\r\n  title: \"Create Next App\",\r\n  description: \"Generated by create next app\",\r\n};\r\n\r\nexport default async function LocaleLayout({\r\n  children,\r\n  params,\r\n}: {\r\n  children: React.ReactNode;\r\n  params: Promise<{ locale: string }>;\r\n}) {\r\n  // Ensure that the incoming `locale` is valid\r\n  const { locale } = await params;\r\n  if (!hasLocale(routing.locales, locale)) {\r\n    notFound();\r\n  }\r\n\r\n  setRequestLocale(locale);\r\n\r\n  return (\r\n    <html lang={locale} dir={locale === \"ar\" ? \"rtl\" : \"ltr\"}>\r\n      <body\r\n        className={`${locale === \"ar\" ? cairo.className : roboto.className} antialiased min-h-screen`}\r\n      >\r\n        <NextIntlClientProvider>\r\n          <QueryProvider>\r\n            <AuthProvider>\r\n              <Header />\r\n              <MainContent>\r\n                {children}\r\n              </MainContent>\r\n              <Footer />\r\n            </AuthProvider>\r\n          </QueryProvider>\r\n        </NextIntlClientProvider>\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AAYO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,eAAe,aAAa,EACzC,QAAQ,EACR,MAAM,EAIP;IACC,6CAA6C;IAC7C,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,IAAI,CAAC,CAAA,GAAA,iLAAA,CAAA,YAAS,AAAD,EAAE,sHAAA,CAAA,UAAO,CAAC,OAAO,EAAE,SAAS;QACvC,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,CAAA,GAAA,2QAAA,CAAA,mBAAgB,AAAD,EAAE;IAEjB,qBACE,8OAAC;QAAK,MAAM;QAAQ,KAAK,WAAW,OAAO,QAAQ;kBACjD,cAAA,8OAAC;YACC,WAAW,GAAG,WAAW,OAAO,yIAAA,CAAA,UAAK,CAAC,SAAS,GAAG,0IAAA,CAAA,UAAM,CAAC,SAAS,CAAC,yBAAyB,CAAC;sBAE7F,cAAA,8OAAC,kQAAA,CAAA,yBAAsB;0BACrB,cAAA,8OAAC,sIAAA,CAAA,UAAa;8BACZ,cAAA,8OAAC,qIAAA,CAAA,eAAY;;0CACX,8OAAC,qIAAA,CAAA,SAAM;;;;;0CACP,8OAAC,+IAAA,CAAA,cAAW;0CACT;;;;;;0CAEH,8OAAC,qIAAA,CAAA,SAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrB", "debugId": null}}]}
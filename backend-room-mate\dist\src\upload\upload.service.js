"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UploadService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const path = require("path");
const uuid_1 = require("uuid");
let UploadService = class UploadService {
    configService;
    constructor(configService) {
        this.configService = configService;
    }
    getBaseUrl() {
        return (this.configService.get('BASE_URL') || 'http://localhost:4000/nestjs');
    }
    generateFilename(originalName) {
        const fileExtension = path.extname(originalName);
        const baseName = path.basename(originalName, fileExtension);
        const timestamp = Date.now();
        const uuid = (0, uuid_1.v4)().substring(0, 8);
        return `${baseName}-${timestamp}-${uuid}${fileExtension}`;
    }
    processUploadedFile(file) {
        const baseUrl = this.getBaseUrl();
        return {
            filename: file.filename,
            originalName: file.originalname,
            url: `${baseUrl}/uploads/${file.filename}`,
            mimetype: file.mimetype,
            size: file.size,
        };
    }
    processMultipleUploadedFiles(files) {
        return files.map((file) => this.processUploadedFile(file));
    }
    validateImageFile(file) {
        const allowedMimeTypes = [
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/gif',
            'image/webp',
            'image/svg+xml',
        ];
        return allowedMimeTypes.includes(file.mimetype);
    }
};
exports.UploadService = UploadService;
exports.UploadService = UploadService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], UploadService);
//# sourceMappingURL=upload.service.js.map
import { CommentsService } from './comments.service';
import { CreateCommentDto } from './dto/create-comment.dto';
import { UpdateCommentDto } from './dto/update-comment.dto';
import { QueryCommentsDto } from './dto/query-comments.dto';
import { UserPayload } from './interfaces/user.interface';
export declare class CommentsController {
    private readonly commentsService;
    constructor(commentsService: CommentsService);
    create(createCommentDto: CreateCommentDto, user: UserPayload): Promise<{
        user: {
            id: string;
            name: string;
            email: string;
            isVerified: boolean;
        };
        property: {
            id: string;
            country: string | null;
            isVerified: boolean;
            createdAt: Date;
            updatedAt: Date;
            title: string | null;
            userId: string | null;
            slug: string | null;
            images: string[];
            city: string | null;
            neighborhood: string | null;
            address: string | null;
            description: string | null;
            latitude: string | null;
            longitude: string | null;
            type: import(".prisma/client").$Enums.PropertyType;
            roomType: import(".prisma/client").$Enums.RoomType;
            genderRequired: string | null;
            totalRooms: string | null;
            availableRooms: string | null;
            roomsToComplete: string | null;
            price: string | null;
            size: string | null;
            floor: string | null;
            bathrooms: string | null;
            separatedBathroom: boolean;
            residentsCount: string | null;
            availablePersons: string | null;
            rentTime: import(".prisma/client").$Enums.RentTime;
            paymentTime: import(".prisma/client").$Enums.PaymentTime;
            priceIncludeWaterAndElectricity: boolean;
            allowSmoking: boolean;
            includeFurniture: boolean;
            airConditioning: boolean;
            includeWaterHeater: boolean;
            parking: boolean;
            internet: boolean;
            nearToMetro: boolean;
            nearToMarket: boolean;
            elevator: boolean;
            trialPeriod: boolean;
            goodForForeigners: boolean;
            rating: number;
            totalRatings: number;
            termsAndConditions: string | null;
            isAvailable: boolean;
            categoryId: string;
            ownerId: string;
        } | null;
        person: {
            id: string;
            country: string | null;
            isVerified: boolean;
            createdAt: Date;
            updatedAt: Date;
            title: string | null;
            slug: string | null;
            images: string[];
            city: string | null;
            neighborhood: string | null;
            address: string | null;
            description: string | null;
            latitude: string | null;
            longitude: string | null;
            type: import(".prisma/client").$Enums.PropertyType;
            roomType: import(".prisma/client").$Enums.RoomType;
            genderRequired: string | null;
            totalRooms: string | null;
            availableRooms: string | null;
            price: string | null;
            size: string | null;
            floor: string | null;
            bathrooms: string | null;
            separatedBathroom: boolean;
            residentsCount: string | null;
            availablePersons: string | null;
            rentTime: import(".prisma/client").$Enums.RentTime;
            paymentTime: import(".prisma/client").$Enums.PaymentTime;
            priceIncludeWaterAndElectricity: boolean;
            allowSmoking: boolean;
            includeFurniture: boolean;
            airConditioning: boolean;
            includeWaterHeater: boolean;
            parking: boolean;
            internet: boolean;
            nearToMetro: boolean;
            nearToMarket: boolean;
            elevator: boolean;
            trialPeriod: boolean;
            goodForForeigners: boolean;
            rating: number;
            totalRatings: number;
            termsAndConditions: string | null;
            isAvailable: boolean;
            categoryId: string;
            ownerId: string;
        } | null;
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        propertyId: string | null;
        personId: string | null;
        content: string;
    }>;
    findAll(query: QueryCommentsDto): Promise<{
        comments: ({
            user: {
                id: string;
                name: string;
                email: string;
                isVerified: boolean;
            };
            property: {
                id: string;
                title: string | null;
                slug: string | null;
            } | null;
            person: {
                id: string;
                title: string | null;
                slug: string | null;
            } | null;
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            userId: string;
            propertyId: string | null;
            personId: string | null;
            content: string;
        })[];
        pagination: {
            total: number;
            page: number;
            limit: number;
            totalPages: number;
        };
    }>;
    getPropertyComments(propertyId: string, query: QueryCommentsDto): Promise<{
        comments: ({
            user: {
                id: string;
                name: string;
                email: string;
                isVerified: boolean;
            };
            property: {
                id: string;
                title: string | null;
                slug: string | null;
            } | null;
            person: {
                id: string;
                title: string | null;
                slug: string | null;
            } | null;
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            userId: string;
            propertyId: string | null;
            personId: string | null;
            content: string;
        })[];
        pagination: {
            total: number;
            page: number;
            limit: number;
            totalPages: number;
        };
    }>;
    getPersonComments(personId: string, query: QueryCommentsDto): Promise<{
        comments: ({
            user: {
                id: string;
                name: string;
                email: string;
                isVerified: boolean;
            };
            property: {
                id: string;
                title: string | null;
                slug: string | null;
            } | null;
            person: {
                id: string;
                title: string | null;
                slug: string | null;
            } | null;
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            userId: string;
            propertyId: string | null;
            personId: string | null;
            content: string;
        })[];
        pagination: {
            total: number;
            page: number;
            limit: number;
            totalPages: number;
        };
    }>;
    findOne(id: string): Promise<{
        user: {
            id: string;
            name: string;
            email: string;
            isVerified: boolean;
        };
        property: {
            id: string;
            title: string | null;
            slug: string | null;
        } | null;
        person: {
            id: string;
            title: string | null;
            slug: string | null;
        } | null;
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        propertyId: string | null;
        personId: string | null;
        content: string;
    }>;
    update(id: string, updateCommentDto: UpdateCommentDto, user: UserPayload): Promise<{
        user: {
            id: string;
            name: string;
            email: string;
            isVerified: boolean;
        };
        property: {
            id: string;
            title: string | null;
            slug: string | null;
        } | null;
        person: {
            id: string;
            title: string | null;
            slug: string | null;
        } | null;
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        propertyId: string | null;
        personId: string | null;
        content: string;
    }>;
    remove(id: string, user: UserPayload): Promise<{
        message: string;
    }>;
}

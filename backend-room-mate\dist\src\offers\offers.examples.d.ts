import { CreateOfferDto } from './dto/create-offer.dto';
import { UpdateOfferDto } from './dto/update-offer.dto';
import { QueryOffersDto } from './dto/query-offers.dto';
export declare const createOfferExample: CreateOfferDto;
export declare const simpleOfferExample: CreateOfferDto;
export declare const detailedOfferExample: CreateOfferDto;
export declare const shortTermOfferExample: CreateOfferDto;
export declare const updateOfferExample: UpdateOfferDto;
export declare const queryExamples: {
    getAllOffers: QueryOffersDto;
    filterByStatus: QueryOffersDto;
    filterByProperty: QueryOffersDto;
    filterByUser: QueryOffersDto;
    filterByPrice: QueryOffersDto;
    filterWithDeposit: QueryOffersDto;
    searchOffers: QueryOffersDto;
    complexFilter: QueryOffersDto;
};
export declare const validationExamples: {
    validStatuses: string[];
    validPrices: string[];
    validPhones: string[];
    validDurations: string[];
    validMessages: string[];
    invalidExamples: ({
        message: string;
        price?: undefined;
        phone?: undefined;
        propertyId?: undefined;
        duration?: undefined;
    } | {
        price: string;
        message?: undefined;
        phone?: undefined;
        propertyId?: undefined;
        duration?: undefined;
    } | {
        phone: string;
        message?: undefined;
        price?: undefined;
        propertyId?: undefined;
        duration?: undefined;
    } | {
        propertyId: string;
        message?: undefined;
        price?: undefined;
        phone?: undefined;
        duration?: undefined;
    } | {
        duration: string;
        message?: undefined;
        price?: undefined;
        phone?: undefined;
        propertyId?: undefined;
    })[];
};
export declare const apiEndpointExamples: {
    create: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
        body: CreateOfferDto;
    };
    getAll: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    getUserOffers: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    getPropertyOffers: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    filterByStatus: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    search: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    getStats: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    getById: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    update: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
        body: UpdateOfferDto;
    };
    accept: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    reject: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
        body: {
            reason: string;
        };
    };
    cancel: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    delete: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
};
export declare const responseExamples: {
    offerResponse: {
        id: string;
        message: string;
        price: string;
        phone: string;
        duration: string;
        deposit: boolean;
        status: string;
        createdAt: string;
        updatedAt: string;
        property: {
            id: string;
            title: string;
            city: string;
            country: string;
            type: string;
            price: string;
            images: string[];
            user: {
                id: string;
                name: string;
                email: string;
                phone: string;
            };
        };
        user: {
            id: string;
            name: string;
            email: string;
            phone: string;
            age: string;
            gender: string;
            occupation: string;
        };
        bookings: {
            id: string;
            startDate: string;
            endDate: string;
            status: string;
        }[];
    };
    paginatedResponse: {
        data: never[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };
    statsResponse: {
        totalOffers: number;
        pendingOffers: number;
        acceptedOffers: number;
        rejectedOffers: number;
        cancelledOffers: number;
        avgOfferPrice: number;
        avgResponseTime: number;
        offersByStatus: {
            PENDING: number;
            ACCEPTED: number;
            REJECTED: number;
            CANCELLED: number;
        };
        offersByPrice: {
            'Under 5000': number;
            '5000-8000': number;
            '8000-12000': number;
            'Above 12000': number;
        };
        topProperties: {
            id: string;
            title: string;
            offerCount: number;
            avgOfferPrice: number;
        }[];
        newOffersThisMonth: number;
    };
    createSuccessResponse: {
        success: boolean;
        message: string;
        data: {};
    };
    statusUpdateResponse: {
        success: boolean;
        message: string;
        data: {
            id: string;
            status: string;
            updatedAt: string;
        };
    };
};

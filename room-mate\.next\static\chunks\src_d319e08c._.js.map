{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/app/%5Blocale%5D/%28dashboard%29/dashboard/categories/components/CategoriesHeader.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { Plus } from \"lucide-react\";\r\nimport { useTranslations } from \"next-intl\";\r\n\r\ninterface CategoriesHeaderProps {\r\n    onAddClick: () => void;\r\n}\r\n\r\nexport default function CategoriesHeader({ onAddClick }: CategoriesHeaderProps) {\r\n    const t = useTranslations(\"categories\");\r\n\r\n    return (\r\n        <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 px-6\">\r\n            <div>\r\n                <h1 className=\"text-2xl font-bold text-gray-900\">{t(\"title\")}</h1>\r\n                <p className=\"text-gray-600 mt-1\">{t(\"subtitle\")}</p>\r\n            </div>\r\n            <Button onClick={onAddClick} className=\"flex items-center gap-2\">\r\n                <Plus className=\"h-4 w-4\" />\r\n                {t(\"addCategory\")}\r\n            </Button>\r\n        </div>\r\n    );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAUe,SAAS,iBAAiB,EAAE,UAAU,EAAyB;;IAC1E,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC;;kCACG,6LAAC;wBAAG,WAAU;kCAAoC,EAAE;;;;;;kCACpD,6LAAC;wBAAE,WAAU;kCAAsB,EAAE;;;;;;;;;;;;0BAEzC,6LAAC,qIAAA,CAAA,SAAM;gBAAC,SAAS;gBAAY,WAAU;;kCACnC,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBACf,EAAE;;;;;;;;;;;;;AAInB;GAfwB;;QACV,yMAAA,CAAA,kBAAe;;;KADL", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/actions/upload.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { post } from \"@/services/api\";\r\n\r\nexport const uploadFile = async (file: File) => {\r\n  const formData = new FormData();\r\n  formData.append(\"file\", file);\r\n  const response = await post(\"/upload\", formData);\r\n  return response;\r\n};\r\n\r\nexport const uploadFiles = async (files: File[]) => {\r\n  const formData = new FormData();\r\n  files.forEach((file) => {\r\n    formData.append(\"files\", file);\r\n  });\r\n  const response = await post(\"/upload/multiple\", formData);\r\n  return response;\r\n};\r\n"], "names": [], "mappings": ";;;;;;IAIa,aAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/actions/upload.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { post } from \"@/services/api\";\r\n\r\nexport const uploadFile = async (file: File) => {\r\n  const formData = new FormData();\r\n  formData.append(\"file\", file);\r\n  const response = await post(\"/upload\", formData);\r\n  return response;\r\n};\r\n\r\nexport const uploadFiles = async (files: File[]) => {\r\n  const formData = new FormData();\r\n  files.forEach((file) => {\r\n    formData.append(\"files\", file);\r\n  });\r\n  const response = await post(\"/upload/multiple\", formData);\r\n  return response;\r\n};\r\n"], "names": [], "mappings": ";;;;;;IAWa,cAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/shared/FileUpload.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useRef, useCallback } from \"react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { uploadFile, uploadFiles } from \"@/actions/upload\";\r\nimport {\r\n    Upload,\r\n    X,\r\n    FileImage,\r\n    FileVideo,\r\n    File,\r\n    FileText,\r\n    Loader2,\r\n    Check,\r\n} from \"lucide-react\";\r\nimport { toast } from \"react-hot-toast\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useTranslations } from \"next-intl\";\r\n\r\n// Types for upload responses\r\ninterface SingleUploadResponse {\r\n    filename: string;\r\n    originalName: string;\r\n    url: string;\r\n    mimetype: string;\r\n    size: number;\r\n    fileType: string;\r\n}\r\n\r\ninterface MultipleUploadResponse {\r\n    files: SingleUploadResponse[];\r\n    count: number;\r\n}\r\n\r\ninterface FileUploadProps {\r\n    accept?: string;\r\n    multiple?: boolean;\r\n    maxFiles?: number;\r\n    maxSize?: number; // in MB\r\n    onUploadSuccess?: (files: SingleUploadResponse | SingleUploadResponse[]) => void;\r\n    onUploadError?: (error: string) => void;\r\n    className?: string;\r\n    disabled?: boolean;\r\n    showPreview?: boolean;\r\n    allowedTypes?: string[];\r\n}\r\n\r\ninterface UploadedFilePreview extends SingleUploadResponse {\r\n    id: string;\r\n    preview?: string;\r\n}\r\n\r\nexport default function FileUpload({\r\n    accept = \"image/*,video/*,application/pdf\",\r\n    multiple = false,\r\n    maxFiles = 5,\r\n    maxSize = 10, // 10MB default\r\n    onUploadSuccess,\r\n    onUploadError,\r\n    className,\r\n    disabled = false,\r\n    showPreview = true,\r\n    allowedTypes = ['image', 'video', 'application/pdf']\r\n}: FileUploadProps) {\r\n    const t = useTranslations(\"fileUpload\");\r\n    const [isDragOver, setIsDragOver] = useState(false);\r\n    const [isUploading, setIsUploading] = useState(false);\r\n    const [uploadedFiles, setUploadedFiles] = useState<UploadedFilePreview[]>([]);\r\n    const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n    // Generate file preview\r\n    const generatePreview = (file: File): Promise<string> => {\r\n        return new Promise((resolve) => {\r\n            if (file.type.startsWith('image/')) {\r\n                const reader = new FileReader();\r\n                reader.onload = (e) => resolve(e.target?.result as string);\r\n                reader.readAsDataURL(file);\r\n            } else {\r\n                resolve(''); // No preview for non-images\r\n            }\r\n        });\r\n    };\r\n\r\n    // Get file icon based on type\r\n    const getFileIcon = (fileType: string, mimetype: string) => {\r\n        if (fileType === 'image' || mimetype.startsWith('image/')) {\r\n            return <FileImage className=\"h-8 w-8 text-blue-500\" />;\r\n        }\r\n        if (fileType === 'video' || mimetype.startsWith('video/')) {\r\n            return <FileVideo className=\"h-8 w-8 text-purple-500\" />;\r\n        }\r\n        if (fileType === 'application/pdf') {\r\n            return <FileText className=\"h-8 w-8 text-red-500\" />;\r\n        }\r\n        return <File className=\"h-8 w-8 text-gray-500\" />;\r\n    };\r\n\r\n    // Validate file\r\n    const validateFile = (file: File): string | null => {\r\n        // Check file size\r\n        if (file.size > maxSize * 1024 * 1024) {\r\n            return t(\"fileSizeExceeded\", { maxSize });\r\n        }\r\n\r\n        // Check file type\r\n        const fileType = file.type.split('/')[0];\r\n        if (allowedTypes.length > 0 && !allowedTypes.some(type =>\r\n            file.type.startsWith(type + '/') || fileType === type\r\n        )) {\r\n            return t(\"fileTypeNotAllowed\", { types: allowedTypes.join(', ') });\r\n        }\r\n\r\n        return null;\r\n    };\r\n\r\n    // Handle files\r\n    const handleFiles = useCallback(async (files: FileList) => {\r\n        if (disabled || isUploading) return;\r\n\r\n        const fileArray = Array.from(files);\r\n\r\n        // Validate file count\r\n        if (!multiple && fileArray.length > 1) {\r\n            toast.error(t(\"onlyOneFileAllowed\"));\r\n            return;\r\n        }\r\n\r\n        if (multiple && fileArray.length > maxFiles) {\r\n            toast.error(t(\"maxFilesExceeded\", { maxFiles }));\r\n            return;\r\n        }\r\n\r\n        // Validate each file\r\n        for (const file of fileArray) {\r\n            const error = validateFile(file);\r\n            if (error) {\r\n                toast.error(error);\r\n                return;\r\n            }\r\n        }\r\n\r\n        setIsUploading(true);\r\n\r\n        try {\r\n            let apiResponse;\r\n\r\n            if (multiple && fileArray.length > 1) {\r\n                apiResponse = await uploadFiles(fileArray);\r\n            } else {\r\n                apiResponse = await uploadFile(fileArray[0]);\r\n            }\r\n\r\n            // Check if API request was successful\r\n            if (apiResponse.error || !apiResponse.data) {\r\n                throw new Error(apiResponse.error || t(\"uploadFailed\"));\r\n            }\r\n\r\n            const response: SingleUploadResponse | MultipleUploadResponse = apiResponse.data;\r\n\r\n            // Process response\r\n            let processedFiles: SingleUploadResponse[];\r\n\r\n            if ('files' in response) {\r\n                // Multiple files response\r\n                processedFiles = response.files;\r\n            } else {\r\n                // Single file response\r\n                processedFiles = [response];\r\n            }\r\n\r\n            // Generate previews and create uploaded file objects\r\n            const newUploadedFiles: UploadedFilePreview[] = await Promise.all(\r\n                processedFiles.map(async (file, index) => {\r\n                    const originalFile = fileArray[index];\r\n                    const preview = originalFile ? await generatePreview(originalFile) : undefined;\r\n\r\n                    return {\r\n                        ...file,\r\n                        id: `${file.filename}-${Date.now()}`,\r\n                        preview\r\n                    };\r\n                })\r\n            );\r\n\r\n            if (multiple) {\r\n                setUploadedFiles(prev => [...prev, ...newUploadedFiles]);\r\n            } else {\r\n                setUploadedFiles(newUploadedFiles);\r\n            }\r\n\r\n            // Call success callback\r\n            if (onUploadSuccess) {\r\n                if ('files' in response) {\r\n                    onUploadSuccess(response.files);\r\n                } else {\r\n                    onUploadSuccess(response);\r\n                }\r\n            }\r\n\r\n            toast.success(t(\"filesUploadedSuccessfully\", { count: newUploadedFiles.length }));\r\n        } catch (error) {\r\n            const errorMessage = error instanceof Error ? error.message : t(\"uploadFailed\");\r\n            toast.error(errorMessage);\r\n            if (onUploadError) {\r\n                onUploadError(errorMessage);\r\n            }\r\n        } finally {\r\n            setIsUploading(false);\r\n            // Clear file input\r\n            if (fileInputRef.current) {\r\n                fileInputRef.current.value = '';\r\n            }\r\n        }\r\n    }, [disabled, isUploading, multiple, maxFiles, maxSize, allowedTypes, onUploadSuccess, onUploadError, t]);\r\n\r\n    // Remove uploaded file\r\n    const removeFile = (fileId: string) => {\r\n        setUploadedFiles(prev => prev.filter(file => file.id !== fileId));\r\n    };\r\n\r\n    // Drag and drop handlers\r\n    const handleDragOver = useCallback((e: React.DragEvent) => {\r\n        e.preventDefault();\r\n        if (!disabled && !isUploading) {\r\n            setIsDragOver(true);\r\n        }\r\n    }, [disabled, isUploading]);\r\n\r\n    const handleDragLeave = useCallback((e: React.DragEvent) => {\r\n        e.preventDefault();\r\n        setIsDragOver(false);\r\n    }, []);\r\n\r\n    const handleDrop = useCallback((e: React.DragEvent) => {\r\n        e.preventDefault();\r\n        setIsDragOver(false);\r\n\r\n        if (disabled || isUploading) return;\r\n\r\n        const files = e.dataTransfer.files;\r\n        if (files.length > 0) {\r\n            handleFiles(files);\r\n        }\r\n    }, [disabled, isUploading, handleFiles]);\r\n\r\n    // Click handler\r\n    const handleClick = () => {\r\n        if (!disabled && !isUploading && fileInputRef.current) {\r\n            fileInputRef.current.click();\r\n        }\r\n    };\r\n\r\n    // File input change handler\r\n    const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n        const files = e.target.files;\r\n        if (files && files.length > 0) {\r\n            handleFiles(files);\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div className={cn(\"w-full\", className)}>\r\n            {/* Upload Area */}\r\n            <div\r\n                onClick={handleClick}\r\n                onDragOver={handleDragOver}\r\n                onDragLeave={handleDragLeave}\r\n                onDrop={handleDrop}\r\n                className={cn(\r\n                    \"relative border-2 border-dashed rounded-lg p-8 text-center transition-all cursor-pointer\",\r\n                    \"hover:border-primary/50 hover:bg-primary/5\",\r\n                    isDragOver && \"border-primary bg-primary/10\",\r\n                    disabled && \"opacity-50 cursor-not-allowed\",\r\n                    isUploading && \"cursor-wait\"\r\n                )}\r\n            >\r\n                <input\r\n                    ref={fileInputRef}\r\n                    type=\"file\"\r\n                    accept={accept}\r\n                    multiple={multiple}\r\n                    onChange={handleFileInputChange}\r\n                    className=\"hidden\"\r\n                    disabled={disabled || isUploading}\r\n                />\r\n\r\n                <div className=\"flex flex-col items-center justify-center space-y-4\">\r\n                    {isUploading ? (\r\n                        <>\r\n                            <Loader2 className=\"h-12 w-12 text-primary animate-spin\" />\r\n                            <p className=\"text-sm text-gray-600\">{t(\"uploadingFiles\")}</p>\r\n                        </>\r\n                    ) : (\r\n                        <>\r\n                            <Upload className=\"h-12 w-12 text-gray-400\" />\r\n                            <div className=\"space-y-2\">\r\n                                <p className=\"text-sm font-medium text-gray-900\">\r\n                                    {multiple ? t(\"uploadFiles\") : t(\"uploadFile\")}\r\n                                </p>\r\n                                <p className=\"text-xs text-gray-500\">\r\n                                    {t(\"dragDropOrClick\")}\r\n                                </p>\r\n                                <p className=\"text-xs text-gray-400\">\r\n                                    {t(\"maxSize\", { maxSize })}\r\n                                    {multiple && ` • ${t(\"maxFiles\", { maxFiles })}`}\r\n                                    {allowedTypes.length > 0 && ` • ${t(\"allowedTypes\", { types: allowedTypes.join(', ') })}`}\r\n                                </p>\r\n                            </div>\r\n                        </>\r\n                    )}\r\n                </div>\r\n            </div>\r\n\r\n            {/* File Preview */}\r\n            {showPreview && uploadedFiles.length > 0 && (\r\n                <div className=\"mt-4 space-y-3\">\r\n                    <h4 className=\"text-sm font-medium text-gray-900\">{t(\"uploadedFiles\")}</h4>\r\n                    <div className=\"space-y-2\">\r\n                        {uploadedFiles.map((file) => (\r\n                            <div\r\n                                key={file.id}\r\n                                className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg border\"\r\n                            >\r\n                                <div className=\"flex items-center space-x-3\">\r\n                                    {file.preview ? (\r\n                                        <img\r\n                                            src={file.preview}\r\n                                            alt={file.originalName}\r\n                                            className=\"h-10 w-10 object-cover rounded\"\r\n                                        />\r\n                                    ) : (\r\n                                        getFileIcon(file.fileType, file.mimetype)\r\n                                    )}\r\n                                    <div className=\"flex-1 min-w-0\">\r\n                                        <p className=\"text-sm font-medium text-gray-900 truncate\">\r\n                                            {file.originalName}\r\n                                        </p>\r\n                                        <p className=\"text-xs text-gray-500\">\r\n                                            {t(\"fileSizeInMB\", { size: (file.size / 1024 / 1024).toFixed(2) })} • {t(\"fileType\", { type: file.fileType })}\r\n                                        </p>\r\n                                    </div>\r\n                                </div>\r\n                                <div className=\"flex items-center space-x-2\">\r\n                                    <Check className=\"h-4 w-4 text-green-500\" />\r\n                                    <Button\r\n                                        variant=\"ghost\"\r\n                                        size=\"sm\"\r\n                                        onClick={() => removeFile(file.id)}\r\n                                        className=\"h-8 w-8 p-0 hover:bg-red-100 hover:text-red-600\"\r\n                                    >\r\n                                        <X className=\"h-4 w-4\" />\r\n                                    </Button>\r\n                                </div>\r\n                            </div>\r\n                        ))}\r\n                    </div>\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;;;AAjBA;;;;;;;;AAoDe,SAAS,WAAW,EAC/B,SAAS,iCAAiC,EAC1C,WAAW,KAAK,EAChB,WAAW,CAAC,EACZ,UAAU,EAAE,EACZ,eAAe,EACf,aAAa,EACb,SAAS,EACT,WAAW,KAAK,EAChB,cAAc,IAAI,EAClB,eAAe;IAAC;IAAS;IAAS;CAAkB,EACtC;;IACd,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB,EAAE;IAC5E,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,wBAAwB;IACxB,MAAM,kBAAkB,CAAC;QACrB,OAAO,IAAI,QAAQ,CAAC;YAChB,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;gBAChC,MAAM,SAAS,IAAI;gBACnB,OAAO,MAAM,GAAG,CAAC,IAAM,QAAQ,EAAE,MAAM,EAAE;gBACzC,OAAO,aAAa,CAAC;YACzB,OAAO;gBACH,QAAQ,KAAK,4BAA4B;YAC7C;QACJ;IACJ;IAEA,8BAA8B;IAC9B,MAAM,cAAc,CAAC,UAAkB;QACnC,IAAI,aAAa,WAAW,SAAS,UAAU,CAAC,WAAW;YACvD,qBAAO,6LAAC,mNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;QAChC;QACA,IAAI,aAAa,WAAW,SAAS,UAAU,CAAC,WAAW;YACvD,qBAAO,6LAAC,mNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;QAChC;QACA,IAAI,aAAa,mBAAmB;YAChC,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;QAC/B;QACA,qBAAO,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IAC3B;IAEA,gBAAgB;IAChB,MAAM,eAAe,CAAC;QAClB,kBAAkB;QAClB,IAAI,KAAK,IAAI,GAAG,UAAU,OAAO,MAAM;YACnC,OAAO,EAAE,oBAAoB;gBAAE;YAAQ;QAC3C;QAEA,kBAAkB;QAClB,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QACxC,IAAI,aAAa,MAAM,GAAG,KAAK,CAAC,aAAa,IAAI,CAAC,CAAA,OAC9C,KAAK,IAAI,CAAC,UAAU,CAAC,OAAO,QAAQ,aAAa,OAClD;YACC,OAAO,EAAE,sBAAsB;gBAAE,OAAO,aAAa,IAAI,CAAC;YAAM;QACpE;QAEA,OAAO;IACX;IAEA,eAAe;IACf,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE,OAAO;YACnC,IAAI,YAAY,aAAa;YAE7B,MAAM,YAAY,MAAM,IAAI,CAAC;YAE7B,sBAAsB;YACtB,IAAI,CAAC,YAAY,UAAU,MAAM,GAAG,GAAG;gBACnC,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,EAAE;gBACd;YACJ;YAEA,IAAI,YAAY,UAAU,MAAM,GAAG,UAAU;gBACzC,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,EAAE,oBAAoB;oBAAE;gBAAS;gBAC7C;YACJ;YAEA,qBAAqB;YACrB,KAAK,MAAM,QAAQ,UAAW;gBAC1B,MAAM,QAAQ,aAAa;gBAC3B,IAAI,OAAO;oBACP,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ;gBACJ;YACJ;YAEA,eAAe;YAEf,IAAI;gBACA,IAAI;gBAEJ,IAAI,YAAY,UAAU,MAAM,GAAG,GAAG;oBAClC,cAAc,MAAM,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE;gBACpC,OAAO;oBACH,cAAc,MAAM,CAAA,GAAA,yJAAA,CAAA,aAAU,AAAD,EAAE,SAAS,CAAC,EAAE;gBAC/C;gBAEA,sCAAsC;gBACtC,IAAI,YAAY,KAAK,IAAI,CAAC,YAAY,IAAI,EAAE;oBACxC,MAAM,IAAI,MAAM,YAAY,KAAK,IAAI,EAAE;gBAC3C;gBAEA,MAAM,WAA0D,YAAY,IAAI;gBAEhF,mBAAmB;gBACnB,IAAI;gBAEJ,IAAI,WAAW,UAAU;oBACrB,0BAA0B;oBAC1B,iBAAiB,SAAS,KAAK;gBACnC,OAAO;oBACH,uBAAuB;oBACvB,iBAAiB;wBAAC;qBAAS;gBAC/B;gBAEA,qDAAqD;gBACrD,MAAM,mBAA0C,MAAM,QAAQ,GAAG,CAC7D,eAAe,GAAG;2DAAC,OAAO,MAAM;wBAC5B,MAAM,eAAe,SAAS,CAAC,MAAM;wBACrC,MAAM,UAAU,eAAe,MAAM,gBAAgB,gBAAgB;wBAErE,OAAO;4BACH,GAAG,IAAI;4BACP,IAAI,GAAG,KAAK,QAAQ,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;4BACpC;wBACJ;oBACJ;;gBAGJ,IAAI,UAAU;oBACV;+DAAiB,CAAA,OAAQ;mCAAI;mCAAS;6BAAiB;;gBAC3D,OAAO;oBACH,iBAAiB;gBACrB;gBAEA,wBAAwB;gBACxB,IAAI,iBAAiB;oBACjB,IAAI,WAAW,UAAU;wBACrB,gBAAgB,SAAS,KAAK;oBAClC,OAAO;wBACH,gBAAgB;oBACpB;gBACJ;gBAEA,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC,EAAE,6BAA6B;oBAAE,OAAO,iBAAiB,MAAM;gBAAC;YAClF,EAAE,OAAO,OAAO;gBACZ,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,EAAE;gBAChE,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,IAAI,eAAe;oBACf,cAAc;gBAClB;YACJ,SAAU;gBACN,eAAe;gBACf,mBAAmB;gBACnB,IAAI,aAAa,OAAO,EAAE;oBACtB,aAAa,OAAO,CAAC,KAAK,GAAG;gBACjC;YACJ;QACJ;8CAAG;QAAC;QAAU;QAAa;QAAU;QAAU;QAAS;QAAc;QAAiB;QAAe;KAAE;IAExG,uBAAuB;IACvB,MAAM,aAAa,CAAC;QAChB,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAC7D;IAEA,yBAAyB;IACzB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC;YAChC,EAAE,cAAc;YAChB,IAAI,CAAC,YAAY,CAAC,aAAa;gBAC3B,cAAc;YAClB;QACJ;iDAAG;QAAC;QAAU;KAAY;IAE1B,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,CAAC;YACjC,EAAE,cAAc;YAChB,cAAc;QAClB;kDAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE,CAAC;YAC5B,EAAE,cAAc;YAChB,cAAc;YAEd,IAAI,YAAY,aAAa;YAE7B,MAAM,QAAQ,EAAE,YAAY,CAAC,KAAK;YAClC,IAAI,MAAM,MAAM,GAAG,GAAG;gBAClB,YAAY;YAChB;QACJ;6CAAG;QAAC;QAAU;QAAa;KAAY;IAEvC,gBAAgB;IAChB,MAAM,cAAc;QAChB,IAAI,CAAC,YAAY,CAAC,eAAe,aAAa,OAAO,EAAE;YACnD,aAAa,OAAO,CAAC,KAAK;QAC9B;IACJ;IAEA,4BAA4B;IAC5B,MAAM,wBAAwB,CAAC;QAC3B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;YAC3B,YAAY;QAChB;IACJ;IAEA,qBACI,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;;0BAEzB,6LAAC;gBACG,SAAS;gBACT,YAAY;gBACZ,aAAa;gBACb,QAAQ;gBACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,4FACA,8CACA,cAAc,gCACd,YAAY,iCACZ,eAAe;;kCAGnB,6LAAC;wBACG,KAAK;wBACL,MAAK;wBACL,QAAQ;wBACR,UAAU;wBACV,UAAU;wBACV,WAAU;wBACV,UAAU,YAAY;;;;;;kCAG1B,6LAAC;wBAAI,WAAU;kCACV,4BACG;;8CACI,6LAAC,oNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,6LAAC;oCAAE,WAAU;8CAAyB,EAAE;;;;;;;yDAG5C;;8CACI,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAE,WAAU;sDACR,WAAW,EAAE,iBAAiB,EAAE;;;;;;sDAErC,6LAAC;4CAAE,WAAU;sDACR,EAAE;;;;;;sDAEP,6LAAC;4CAAE,WAAU;;gDACR,EAAE,WAAW;oDAAE;gDAAQ;gDACvB,YAAY,CAAC,GAAG,EAAE,EAAE,YAAY;oDAAE;gDAAS,IAAI;gDAC/C,aAAa,MAAM,GAAG,KAAK,CAAC,GAAG,EAAE,EAAE,gBAAgB;oDAAE,OAAO,aAAa,IAAI,CAAC;gDAAM,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;YAShH,eAAe,cAAc,MAAM,GAAG,mBACnC,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAG,WAAU;kCAAqC,EAAE;;;;;;kCACrD,6LAAC;wBAAI,WAAU;kCACV,cAAc,GAAG,CAAC,CAAC,qBAChB,6LAAC;gCAEG,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;4CACV,KAAK,OAAO,iBACT,6LAAC;gDACG,KAAK,KAAK,OAAO;gDACjB,KAAK,KAAK,YAAY;gDACtB,WAAU;;;;;uDAGd,YAAY,KAAK,QAAQ,EAAE,KAAK,QAAQ;0DAE5C,6LAAC;gDAAI,WAAU;;kEACX,6LAAC;wDAAE,WAAU;kEACR,KAAK,YAAY;;;;;;kEAEtB,6LAAC;wDAAE,WAAU;;4DACR,EAAE,gBAAgB;gEAAE,MAAM,CAAC,KAAK,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;4DAAG;4DAAG;4DAAI,EAAE,YAAY;gEAAE,MAAM,KAAK,QAAQ;4DAAC;;;;;;;;;;;;;;;;;;;kDAIvH,6LAAC;wCAAI,WAAU;;0DACX,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC,qIAAA,CAAA,SAAM;gDACH,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,WAAW,KAAK,EAAE;gDACjC,WAAU;0DAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;;;;;;;+BA9BhB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;AAwC5C;GApTwB;;QAYV,yMAAA,CAAA,kBAAe;;;KAZL", "debugId": null}}, {"offset": {"line": 635, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/actions/categories.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { post, get, patch, del } from \"@/services/api\";\r\n\r\ninterface Category {\r\n  id?: string;\r\n  name: string;\r\n  icon: string;\r\n}\r\n\r\nexport const createCategory = async (category: Category) => {\r\n  const response = await post(`/categories`, category);\r\n  return response;\r\n};\r\n\r\nexport const getCategories = async ({\r\n  search = \"\",\r\n  page = 1,\r\n}: {\r\n  search?: string;\r\n  page?: number;\r\n}) => {\r\n  const response = await get(`/categories?search=${search}&page=${page}`);\r\n  return response?.data;\r\n};\r\n\r\nexport const updateCategory = async (category: Category) => {\r\n  console.log(\"category\", category);\r\n  const response = await patch(`/categories/${category.id}`, {\r\n    name: category.name,\r\n    icon: category.icon,\r\n  });\r\n  console.log(\"response\", response);\r\n  return response;\r\n};\r\n\r\nexport const deleteCategory = async (id: string) => {\r\n  const response = await del(`/categories/${id}`);\r\n  return response;\r\n};\r\n"], "names": [], "mappings": ";;;;;;IAUa,iBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 651, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/actions/categories.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { post, get, patch, del } from \"@/services/api\";\r\n\r\ninterface Category {\r\n  id?: string;\r\n  name: string;\r\n  icon: string;\r\n}\r\n\r\nexport const createCategory = async (category: Category) => {\r\n  const response = await post(`/categories`, category);\r\n  return response;\r\n};\r\n\r\nexport const getCategories = async ({\r\n  search = \"\",\r\n  page = 1,\r\n}: {\r\n  search?: string;\r\n  page?: number;\r\n}) => {\r\n  const response = await get(`/categories?search=${search}&page=${page}`);\r\n  return response?.data;\r\n};\r\n\r\nexport const updateCategory = async (category: Category) => {\r\n  console.log(\"category\", category);\r\n  const response = await patch(`/categories/${category.id}`, {\r\n    name: category.name,\r\n    icon: category.icon,\r\n  });\r\n  console.log(\"response\", response);\r\n  return response;\r\n};\r\n\r\nexport const deleteCategory = async (id: string) => {\r\n  const response = await del(`/categories/${id}`);\r\n  return response;\r\n};\r\n"], "names": [], "mappings": ";;;;;;IA0Ba,iBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 667, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/app/%5Blocale%5D/%28dashboard%29/dashboard/categories/components/CategoryModal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from \"@/components/ui/dialog\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport FileUpload from \"@/components/shared/FileUpload\";\r\nimport { createCategory, updateCategory } from \"@/actions/categories\";\r\nimport { toast } from \"react-hot-toast\";\r\nimport { useTranslations } from \"next-intl\";\r\nimport { Loader2 } from \"lucide-react\";\r\n\r\ninterface Category {\r\n    id?: string;\r\n    name: string;\r\n    icon: string;\r\n}\r\n\r\ninterface CategoryModalProps {\r\n    isOpen: boolean;\r\n    onClose: () => void;\r\n    category?: Category;\r\n    onSuccess: () => void;\r\n}\r\n\r\nexport default function CategoryModal({ isOpen, onClose, category, onSuccess }: CategoryModalProps) {\r\n    const t = useTranslations(\"categories\");\r\n    const [isLoading, setIsLoading] = useState(false);\r\n    const [formData, setFormData] = useState({\r\n        name: \"\",\r\n        icon: \"\",\r\n    });\r\n\r\n    // Reset form when modal opens/closes or category changes\r\n    useEffect(() => {\r\n        if (isOpen && category) {\r\n            setFormData({\r\n                name: category.name,\r\n                icon: category.icon,\r\n            });\r\n        } else if (isOpen && !category) {\r\n            setFormData({\r\n                name: \"\",\r\n                icon: \"\",\r\n            });\r\n        }\r\n    }, [isOpen, category]);\r\n\r\n    const handleSubmit = async (e: React.FormEvent) => {\r\n        e.preventDefault();\r\n\r\n        if (!formData.name.trim()) {\r\n            toast.error(t(\"nameRequired\"));\r\n            return;\r\n        }\r\n\r\n        if (!formData.icon.trim()) {\r\n            toast.error(t(\"iconRequired\"));\r\n            return;\r\n        }\r\n\r\n        setIsLoading(true);\r\n\r\n        try {\r\n            const categoryData = {\r\n                ...formData,\r\n                ...(category?.id && { id: category.id }),\r\n            };\r\n\r\n            const response = category?.id\r\n                ? await updateCategory(categoryData)\r\n                : await createCategory(categoryData);\r\n\r\n            if (response.data && !response.error) {\r\n                toast.success(category?.id ? t(\"categoryUpdated\") : t(\"categoryCreated\"));\r\n                onSuccess();\r\n                onClose();\r\n            } else {\r\n                toast.error(response.error || t(\"operationFailed\"));\r\n            }\r\n        } catch (error) {\r\n            toast.error(t(\"operationFailed\"));\r\n            console.error(\"Category operation error:\", error);\r\n        } finally {\r\n            setIsLoading(false);\r\n        }\r\n    };\r\n\r\n    const handleFileUpload = (files: any) => {\r\n        if (Array.isArray(files) && files.length > 0) {\r\n            setFormData(prev => ({ ...prev, icon: files[0].url }));\r\n        } else if (files && files.url) {\r\n            setFormData(prev => ({ ...prev, icon: files.url }));\r\n        }\r\n    };\r\n\r\n    const handleClose = () => {\r\n        if (!isLoading) {\r\n            onClose();\r\n        }\r\n    };\r\n\r\n    return (\r\n        <Dialog open={isOpen} onOpenChange={handleClose}>\r\n            <DialogContent className=\"sm:max-w-[500px]\">\r\n                <DialogHeader>\r\n                    <DialogTitle>\r\n                        {category?.id ? t(\"editCategory\") : t(\"addCategory\")}\r\n                    </DialogTitle>\r\n                </DialogHeader>\r\n\r\n                <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n                    <div className=\"space-y-2\">\r\n                        <Label htmlFor=\"name\">{t(\"categoryName\")}</Label>\r\n                        <Input\r\n                            id=\"name\"\r\n                            type=\"text\"\r\n                            value={formData.name}\r\n                            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}\r\n                            placeholder={t(\"enterCategoryName\")}\r\n                            disabled={isLoading}\r\n                            required\r\n                        />\r\n                    </div>\r\n\r\n                    <div className=\"space-y-2\">\r\n                        <Label>{t(\"categoryIcon\")}</Label>\r\n                        <FileUpload\r\n                            accept=\"image/*\"\r\n                            multiple={false}\r\n                            maxFiles={1}\r\n                            maxSize={5}\r\n                            onUploadSuccess={handleFileUpload}\r\n                            onUploadError={(error) => toast.error(error)}\r\n                            allowedTypes={['image']}\r\n                            showPreview={true}\r\n                            disabled={isLoading}\r\n                        />\r\n                        {formData.icon && (\r\n                            <div className=\"mt-2\">\r\n                                <img\r\n                                    src={formData.icon}\r\n                                    alt=\"Category icon\"\r\n                                    className=\"w-16 h-16 object-cover rounded-lg border\"\r\n                                />\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n\r\n                    <div className=\"flex justify-end space-x-3 pt-4\">\r\n                        <Button\r\n                            type=\"button\"\r\n                            variant=\"outline\"\r\n                            onClick={handleClose}\r\n                            disabled={isLoading}\r\n                        >\r\n                            {t(\"cancel\")}\r\n                        </Button>\r\n                        <Button\r\n                            type=\"submit\"\r\n                            disabled={isLoading}\r\n                        >\r\n                            {isLoading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\r\n                            {category?.id ? t(\"updateCategory\") : t(\"createCategory\")}\r\n                        </Button>\r\n                    </div>\r\n                </form>\r\n            </DialogContent>\r\n        </Dialog>\r\n    );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;AA0Be,SAAS,cAAc,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAsB;;IAC9F,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,MAAM;QACN,MAAM;IACV;IAEA,yDAAyD;IACzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACN,IAAI,UAAU,UAAU;gBACpB,YAAY;oBACR,MAAM,SAAS,IAAI;oBACnB,MAAM,SAAS,IAAI;gBACvB;YACJ,OAAO,IAAI,UAAU,CAAC,UAAU;gBAC5B,YAAY;oBACR,MAAM;oBACN,MAAM;gBACV;YACJ;QACJ;kCAAG;QAAC;QAAQ;KAAS;IAErB,MAAM,eAAe,OAAO;QACxB,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACvB,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,EAAE;YACd;QACJ;QAEA,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACvB,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,EAAE;YACd;QACJ;QAEA,aAAa;QAEb,IAAI;YACA,MAAM,eAAe;gBACjB,GAAG,QAAQ;gBACX,GAAI,UAAU,MAAM;oBAAE,IAAI,SAAS,EAAE;gBAAC,CAAC;YAC3C;YAEA,MAAM,WAAW,UAAU,KACrB,MAAM,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD,EAAE,gBACrB,MAAM,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD,EAAE;YAE3B,IAAI,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,EAAE;gBAClC,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE,qBAAqB,EAAE;gBACtD;gBACA;YACJ,OAAO;gBACH,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,KAAK,IAAI,EAAE;YACpC;QACJ,EAAE,OAAO,OAAO;YACZ,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,EAAE;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC/C,SAAU;YACN,aAAa;QACjB;IACJ;IAEA,MAAM,mBAAmB,CAAC;QACtB,IAAI,MAAM,OAAO,CAAC,UAAU,MAAM,MAAM,GAAG,GAAG;YAC1C,YAAY,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,MAAM,KAAK,CAAC,EAAE,CAAC,GAAG;gBAAC,CAAC;QACxD,OAAO,IAAI,SAAS,MAAM,GAAG,EAAE;YAC3B,YAAY,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,MAAM,MAAM,GAAG;gBAAC,CAAC;QACrD;IACJ;IAEA,MAAM,cAAc;QAChB,IAAI,CAAC,WAAW;YACZ;QACJ;IACJ;IAEA,qBACI,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACrB,6LAAC,qIAAA,CAAA,eAAY;8BACT,cAAA,6LAAC,qIAAA,CAAA,cAAW;kCACP,UAAU,KAAK,EAAE,kBAAkB,EAAE;;;;;;;;;;;8BAI9C,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACpC,6LAAC;4BAAI,WAAU;;8CACX,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAQ,EAAE;;;;;;8CACzB,6LAAC,oIAAA,CAAA,QAAK;oCACF,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,IAAI;oCACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACvE,aAAa,EAAE;oCACf,UAAU;oCACV,QAAQ;;;;;;;;;;;;sCAIhB,6LAAC;4BAAI,WAAU;;8CACX,6LAAC,oIAAA,CAAA,QAAK;8CAAE,EAAE;;;;;;8CACV,6LAAC,6IAAA,CAAA,UAAU;oCACP,QAAO;oCACP,UAAU;oCACV,UAAU;oCACV,SAAS;oCACT,iBAAiB;oCACjB,eAAe,CAAC,QAAU,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oCACtC,cAAc;wCAAC;qCAAQ;oCACvB,aAAa;oCACb,UAAU;;;;;;gCAEb,SAAS,IAAI,kBACV,6LAAC;oCAAI,WAAU;8CACX,cAAA,6LAAC;wCACG,KAAK,SAAS,IAAI;wCAClB,KAAI;wCACJ,WAAU;;;;;;;;;;;;;;;;;sCAM1B,6LAAC;4BAAI,WAAU;;8CACX,6LAAC,qIAAA,CAAA,SAAM;oCACH,MAAK;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;8CAET,EAAE;;;;;;8CAEP,6LAAC,qIAAA,CAAA,SAAM;oCACH,MAAK;oCACL,UAAU;;wCAET,2BAAa,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAChC,UAAU,KAAK,EAAE,oBAAoB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpE;GAjJwB;;QACV,yMAAA,CAAA,kBAAe;;;KADL", "debugId": null}}, {"offset": {"line": 951, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/hooks/usePaginatedData.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\r\nimport { useState, useEffect, useCallback, useRef } from \"react\";\r\nimport { useQuery } from \"@tanstack/react-query\";\r\n\r\nexport interface ApiResponse<T> {\r\n  success: boolean;\r\n  message: string;\r\n  status: number;\r\n  data: T;\r\n}\r\n\r\ninterface PaginationOptions<T, R> {\r\n  queryKey: string | string[];\r\n  fetchFn: (page: string) => Promise<ApiResponse<R>>;\r\n  getItems: (data: R) => T[];\r\n  getTotalCount: (data: R) => number;\r\n  getItemsPerPage: (data: R) => number;\r\n  initialPage?: number;\r\n}\r\n\r\nexport function usePaginatedData<T, R>({\r\n  queryKey,\r\n  fetchFn,\r\n  getItems,\r\n  getTotalCount,\r\n  getItemsPerPage,\r\n  initialPage = 1,\r\n}: PaginationOptions<T, R>) {\r\n  const [page, setPage] = useState(initialPage);\r\n  const [allItems, setAllItems] = useState<T[]>([]);\r\n  const queryKeyRef = useRef(queryKey);\r\n\r\n  // Update ref when queryKey changes\r\n  useEffect(() => {\r\n    if (JSON.stringify(queryKeyRef.current) !== JSON.stringify(queryKey)) {\r\n      queryKeyRef.current = queryKey;\r\n      setPage(1);\r\n      setAllItems([]);\r\n    }\r\n  }, [queryKey]);\r\n\r\n  const { data, isLoading } = useQuery({\r\n    queryKey: Array.isArray(queryKey) ? [...queryKey, page] : [queryKey, page],\r\n    queryFn: () => fetchFn(page.toString()),\r\n  });\r\n\r\n  // Use useRef to store the latest callback functions to avoid recreation\r\n  const getItemsRef = useRef(getItems);\r\n  const getTotalCountRef = useRef(getTotalCount);\r\n  const getItemsPerPageRef = useRef(getItemsPerPage);\r\n\r\n  // Update refs when functions change\r\n  useEffect(() => {\r\n    getItemsRef.current = getItems;\r\n    getTotalCountRef.current = getTotalCount;\r\n    getItemsPerPageRef.current = getItemsPerPage;\r\n  }, [getItems, getTotalCount, getItemsPerPage]);\r\n\r\n  const processNewItems = useCallback(\r\n    (newData: R | undefined, currentPage: number) => {\r\n      if (!newData) return;\r\n\r\n      const newItems = getItemsRef.current(newData);\r\n\r\n      setAllItems((prev) => {\r\n        if (currentPage === 1) return newItems;\r\n\r\n        const existingIds = new Set(prev.map((item: any) => item._id));\r\n        const uniqueNewItems = newItems.filter(\r\n          (item: any) => !existingIds.has(item._id)\r\n        );\r\n\r\n        return [...prev, ...uniqueNewItems];\r\n      });\r\n    },\r\n    [] // Empty dependency array since we use refs\r\n  );\r\n\r\n  useEffect(() => {\r\n    processNewItems(data?.data, page);\r\n  }, [data, page, processNewItems]);\r\n\r\n  const hasMorePages = data\r\n    ? getTotalCountRef.current(data?.data) >\r\n      page * getItemsPerPageRef.current(data?.data)\r\n    : false;\r\n\r\n  const loadMore = useCallback(() => {\r\n    setPage((prev) => prev + 1);\r\n  }, []);\r\n\r\n  const refresh = useCallback(() => {\r\n    setPage(1);\r\n    setAllItems([]);\r\n  }, []);\r\n\r\n  return {\r\n    data: allItems,\r\n    isLoading,\r\n    hasMore: hasMorePages,\r\n    loadMore,\r\n    page,\r\n    setPage,\r\n    rawData: data,\r\n    refresh,\r\n  };\r\n}\r\n"], "names": [], "mappings": "AAAA,qDAAqD;;;AACrD;AACA;;;;AAkBO,SAAS,iBAAuB,EACrC,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,aAAa,EACb,eAAe,EACf,cAAc,CAAC,EACS;;IACxB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO,EAAE;IAChD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE3B,mCAAmC;IACnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,KAAK,SAAS,CAAC,YAAY,OAAO,MAAM,KAAK,SAAS,CAAC,WAAW;gBACpE,YAAY,OAAO,GAAG;gBACtB,QAAQ;gBACR,YAAY,EAAE;YAChB;QACF;qCAAG;QAAC;KAAS;IAEb,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACnC,UAAU,MAAM,OAAO,CAAC,YAAY;eAAI;YAAU;SAAK,GAAG;YAAC;YAAU;SAAK;QAC1E,OAAO;yCAAE,IAAM,QAAQ,KAAK,QAAQ;;IACtC;IAEA,wEAAwE;IACxE,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAChC,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAElC,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,YAAY,OAAO,GAAG;YACtB,iBAAiB,OAAO,GAAG;YAC3B,mBAAmB,OAAO,GAAG;QAC/B;qCAAG;QAAC;QAAU;QAAe;KAAgB;IAE7C,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAChC,CAAC,SAAwB;YACvB,IAAI,CAAC,SAAS;YAEd,MAAM,WAAW,YAAY,OAAO,CAAC;YAErC;iEAAY,CAAC;oBACX,IAAI,gBAAgB,GAAG,OAAO;oBAE9B,MAAM,cAAc,IAAI,IAAI,KAAK,GAAG;yEAAC,CAAC,OAAc,KAAK,GAAG;;oBAC5D,MAAM,iBAAiB,SAAS,MAAM;wFACpC,CAAC,OAAc,CAAC,YAAY,GAAG,CAAC,KAAK,GAAG;;oBAG1C,OAAO;2BAAI;2BAAS;qBAAe;gBACrC;;QACF;wDACA,EAAE,CAAC,2CAA2C;;IAGhD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,gBAAgB,MAAM,MAAM;QAC9B;qCAAG;QAAC;QAAM;QAAM;KAAgB;IAEhC,MAAM,eAAe,OACjB,iBAAiB,OAAO,CAAC,MAAM,QAC/B,OAAO,mBAAmB,OAAO,CAAC,MAAM,QACxC;IAEJ,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YAC3B;0DAAQ,CAAC,OAAS,OAAO;;QAC3B;iDAAG,EAAE;IAEL,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YAC1B,QAAQ;YACR,YAAY,EAAE;QAChB;gDAAG,EAAE;IAEL,OAAO;QACL,MAAM;QACN;QACA,SAAS;QACT;QACA;QACA;QACA,SAAS;QACT;IACF;AACF;GAtFgB;;QAqBc,8KAAA,CAAA,WAAQ", "debugId": null}}, {"offset": {"line": 1074, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/actions/categories.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { post, get, patch, del } from \"@/services/api\";\r\n\r\ninterface Category {\r\n  id?: string;\r\n  name: string;\r\n  icon: string;\r\n}\r\n\r\nexport const createCategory = async (category: Category) => {\r\n  const response = await post(`/categories`, category);\r\n  return response;\r\n};\r\n\r\nexport const getCategories = async ({\r\n  search = \"\",\r\n  page = 1,\r\n}: {\r\n  search?: string;\r\n  page?: number;\r\n}) => {\r\n  const response = await get(`/categories?search=${search}&page=${page}`);\r\n  return response?.data;\r\n};\r\n\r\nexport const updateCategory = async (category: Category) => {\r\n  console.log(\"category\", category);\r\n  const response = await patch(`/categories/${category.id}`, {\r\n    name: category.name,\r\n    icon: category.icon,\r\n  });\r\n  console.log(\"response\", response);\r\n  return response;\r\n};\r\n\r\nexport const deleteCategory = async (id: string) => {\r\n  const response = await del(`/categories/${id}`);\r\n  return response;\r\n};\r\n"], "names": [], "mappings": ";;;;;;IAea,gBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1090, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/actions/categories.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { post, get, patch, del } from \"@/services/api\";\r\n\r\ninterface Category {\r\n  id?: string;\r\n  name: string;\r\n  icon: string;\r\n}\r\n\r\nexport const createCategory = async (category: Category) => {\r\n  const response = await post(`/categories`, category);\r\n  return response;\r\n};\r\n\r\nexport const getCategories = async ({\r\n  search = \"\",\r\n  page = 1,\r\n}: {\r\n  search?: string;\r\n  page?: number;\r\n}) => {\r\n  const response = await get(`/categories?search=${search}&page=${page}`);\r\n  return response?.data;\r\n};\r\n\r\nexport const updateCategory = async (category: Category) => {\r\n  console.log(\"category\", category);\r\n  const response = await patch(`/categories/${category.id}`, {\r\n    name: category.name,\r\n    icon: category.icon,\r\n  });\r\n  console.log(\"response\", response);\r\n  return response;\r\n};\r\n\r\nexport const deleteCategory = async (id: string) => {\r\n  const response = await del(`/categories/${id}`);\r\n  return response;\r\n};\r\n"], "names": [], "mappings": ";;;;;;IAoCa,iBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1106, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"table-container\"\r\n      className=\"relative w-full overflow-x-auto\"\r\n    >\r\n      <table\r\n        data-slot=\"table\"\r\n        className={cn(\"w-full caption-bottom text-sm\", className)}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\r\n  return (\r\n    <thead\r\n      data-slot=\"table-header\"\r\n      className={cn(\"[&_tr]:border-b\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\r\n  return (\r\n    <tbody\r\n      data-slot=\"table-body\"\r\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\r\n  return (\r\n    <tfoot\r\n      data-slot=\"table-footer\"\r\n      className={cn(\r\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\r\n  return (\r\n    <tr\r\n      data-slot=\"table-row\"\r\n      className={cn(\r\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\r\n  return (\r\n    <th\r\n      data-slot=\"table-head\"\r\n      className={cn(\r\n        \"text-foreground h-10 px-2 align-middle text-center font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\r\n  return (\r\n    <td\r\n      data-slot=\"table-cell\"\r\n      className={cn(\r\n        \"p-2 align-middle text-center whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCaption({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"caption\">) {\r\n  return (\r\n    <caption\r\n      data-slot=\"table-caption\"\r\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sHACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 1244, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/app/%5Blocale%5D/%28dashboard%29/dashboard/categories/components/CategoriesTable.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { usePaginatedData } from \"@/hooks/usePaginatedData\";\r\nimport { getCategories, deleteCategory } from \"@/actions/categories\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@/components/ui/table\";\r\nimport { Edit, Trash2, Loader2 } from \"lucide-react\";\r\nimport { toast } from \"react-hot-toast\";\r\nimport { useTranslations } from \"next-intl\";\r\n\r\ninterface Category {\r\n    id: string;\r\n    name: string;\r\n    icon: string;\r\n    createdAt?: string;\r\n    updatedAt?: string;\r\n}\r\n\r\ninterface CategoriesResponse {\r\n    data: Category[];\r\n    pagination: {\r\n        total: number;\r\n        page: number;\r\n        limit: number;\r\n        totalPages: number;\r\n        hasNext: boolean;\r\n        hasPrev: boolean;\r\n    };\r\n}\r\n\r\ninterface CategoriesTableProps {\r\n    onEdit: (category: Category) => void;\r\n    refreshTrigger: number;\r\n}\r\n\r\nexport default function CategoriesTable({ onEdit, refreshTrigger }: CategoriesTableProps) {\r\n    const t = useTranslations(\"categories\");\r\n    const [deletingId, setDeletingId] = useState<string | null>(null);\r\n\r\n    const {\r\n        data: categories,\r\n        isLoading,\r\n        hasMore,\r\n        loadMore,\r\n        refresh,\r\n    } = usePaginatedData<Category, CategoriesResponse>({\r\n        queryKey: [\"categories\", refreshTrigger.toString()],\r\n        fetchFn: async (page: string) => {\r\n            const response = await getCategories({ page: parseInt(page, 10) });\r\n            return {\r\n                success: true,\r\n                message: \"Success\",\r\n                status: 200,\r\n                data: response,\r\n            };\r\n        },\r\n        getItems: (data: CategoriesResponse) => data.data,\r\n        getTotalCount: (data: CategoriesResponse) => data.pagination.total,\r\n        getItemsPerPage: (data: CategoriesResponse) => data.pagination.limit,\r\n    });\r\n\r\n    console.log(categories);\r\n    \r\n\r\n    const handleDelete = async (id: string) => {\r\n        if (!confirm(t(\"deleteConfirmation\"))) return;\r\n\r\n        setDeletingId(id);\r\n        try {\r\n            const response = await deleteCategory(id);\r\n            if (response.data && !response.error) {\r\n                toast.success(t(\"categoryDeleted\"));\r\n                refresh();\r\n            } else {\r\n                toast.error(response.error || t(\"deleteFailed\"));\r\n            }\r\n        } catch (error) {\r\n            toast.error(t(\"deleteFailed\"));\r\n            console.error(\"Delete category error:\", error);\r\n        } finally {\r\n            setDeletingId(null);\r\n        }\r\n    };\r\n\r\n    if (isLoading && categories.length === 0) {\r\n        return (\r\n            <div className=\"flex justify-center items-center py-8\">\r\n                <Loader2 className=\"h-8 w-8 animate-spin\" />\r\n                <span className=\"ml-2\">{t(\"loading\")}</span>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    if (!isLoading && categories.length === 0) {\r\n        return (\r\n            <div className=\"text-center py-8\">\r\n                <p className=\"text-gray-500\">{t(\"noCategories\")}</p>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <div className=\"space-y-4\">\r\n            <div className=\"rounded-lg border shadow-sm overflow-hidden\">\r\n                <Table>\r\n                    <TableHeader className=\"bg-gray-50/50\">\r\n                        <TableRow>\r\n                            <TableHead className=\"text-center font-semibold\">{t(\"icon\")}</TableHead>\r\n                            <TableHead className=\"text-center font-semibold\">{t(\"name\")}</TableHead>\r\n                            <TableHead className=\"text-center font-semibold\">{t(\"createdAt\")}</TableHead>\r\n                            <TableHead className=\"text-center font-semibold\">{t(\"actions\")}</TableHead>\r\n                        </TableRow>\r\n                    </TableHeader>\r\n                    <TableBody>\r\n                        {categories.map((category) => (\r\n                            <TableRow key={category.id} className=\"hover:bg-gray-50/50 transition-colors\">\r\n                                <TableCell className=\"text-center py-4\">\r\n                                    <div className=\"flex justify-center\">\r\n                                        <img\r\n                                            src={category.icon}\r\n                                            alt={category.name}\r\n                                            className=\"w-12 h-12 object-cover rounded-lg shadow-sm border\"\r\n                                        />\r\n                                    </div>\r\n                                </TableCell>\r\n                                <TableCell className=\"text-center font-medium py-4\">{category.name}</TableCell>\r\n                                <TableCell className=\"text-center py-4\">\r\n                                    {category.createdAt\r\n                                        ? new Date(category.createdAt).toLocaleDateString()\r\n                                        : '-'\r\n                                    }\r\n                                </TableCell>\r\n                                <TableCell className=\"text-center py-4\">\r\n                                    <div className=\"flex justify-center space-x-2\">\r\n                                        <Button\r\n                                            variant=\"outline\"\r\n                                            size=\"sm\"\r\n                                            onClick={() => onEdit(category)}\r\n                                            className=\"hover:bg-blue-50 hover:border-blue-300 transition-colors\"\r\n                                        >\r\n                                            <Edit className=\"h-4 w-4\" />\r\n                                        </Button>\r\n                                        <Button\r\n                                            variant=\"outline\"\r\n                                            size=\"sm\"\r\n                                            onClick={() => handleDelete(category.id)}\r\n                                            disabled={deletingId === category.id}\r\n                                            className=\"text-red-600 hover:text-red-700 hover:bg-red-50 hover:border-red-300 transition-colors\"\r\n                                        >\r\n                                            {deletingId === category.id ? (\r\n                                                <Loader2 className=\"h-4 w-4 animate-spin\" />\r\n                                            ) : (\r\n                                                <Trash2 className=\"h-4 w-4\" />\r\n                                            )}\r\n                                        </Button>\r\n                                    </div>\r\n                                </TableCell>\r\n                            </TableRow>\r\n                        ))}\r\n                    </TableBody>\r\n                </Table>\r\n            </div>\r\n\r\n            {hasMore && (\r\n                <div className=\"flex justify-center\">\r\n                    <Button\r\n                        variant=\"outline\"\r\n                        onClick={loadMore}\r\n                        disabled={isLoading}\r\n                    >\r\n                        {isLoading ? (\r\n                            <>\r\n                                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\r\n                                {t(\"loading\")}\r\n                            </>\r\n                        ) : (\r\n                            t(\"loadMore\")\r\n                        )}\r\n                    </Button>\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;;;AATA;;;;;;;;;AAoCe,SAAS,gBAAgB,EAAE,MAAM,EAAE,cAAc,EAAwB;;IACpF,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE5D,MAAM,EACF,MAAM,UAAU,EAChB,SAAS,EACT,OAAO,EACP,QAAQ,EACR,OAAO,EACV,GAAG,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD,EAAgC;QAC/C,UAAU;YAAC;YAAc,eAAe,QAAQ;SAAG;QACnD,OAAO;gDAAE,OAAO;gBACZ,MAAM,WAAW,MAAM,CAAA,GAAA,yJAAA,CAAA,gBAAa,AAAD,EAAE;oBAAE,MAAM,SAAS,MAAM;gBAAI;gBAChE,OAAO;oBACH,SAAS;oBACT,SAAS;oBACT,QAAQ;oBACR,MAAM;gBACV;YACJ;;QACA,QAAQ;gDAAE,CAAC,OAA6B,KAAK,IAAI;;QACjD,aAAa;gDAAE,CAAC,OAA6B,KAAK,UAAU,CAAC,KAAK;;QAClE,eAAe;gDAAE,CAAC,OAA6B,KAAK,UAAU,CAAC,KAAK;;IACxE;IAEA,QAAQ,GAAG,CAAC;IAGZ,MAAM,eAAe,OAAO;QACxB,IAAI,CAAC,QAAQ,EAAE,wBAAwB;QAEvC,cAAc;QACd,IAAI;YACA,MAAM,WAAW,MAAM,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD,EAAE;YACtC,IAAI,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,EAAE;gBAClC,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC,EAAE;gBAChB;YACJ,OAAO;gBACH,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,KAAK,IAAI,EAAE;YACpC;QACJ,EAAE,OAAO,OAAO;YACZ,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,EAAE;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC5C,SAAU;YACN,cAAc;QAClB;IACJ;IAEA,IAAI,aAAa,WAAW,MAAM,KAAK,GAAG;QACtC,qBACI,6LAAC;YAAI,WAAU;;8BACX,6LAAC,oNAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;8BACnB,6LAAC;oBAAK,WAAU;8BAAQ,EAAE;;;;;;;;;;;;IAGtC;IAEA,IAAI,CAAC,aAAa,WAAW,MAAM,KAAK,GAAG;QACvC,qBACI,6LAAC;YAAI,WAAU;sBACX,cAAA,6LAAC;gBAAE,WAAU;0BAAiB,EAAE;;;;;;;;;;;IAG5C;IAEA,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC,oIAAA,CAAA,QAAK;;sCACF,6LAAC,oIAAA,CAAA,cAAW;4BAAC,WAAU;sCACnB,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;kDACL,6LAAC,oIAAA,CAAA,YAAS;wCAAC,WAAU;kDAA6B,EAAE;;;;;;kDACpD,6LAAC,oIAAA,CAAA,YAAS;wCAAC,WAAU;kDAA6B,EAAE;;;;;;kDACpD,6LAAC,oIAAA,CAAA,YAAS;wCAAC,WAAU;kDAA6B,EAAE;;;;;;kDACpD,6LAAC,oIAAA,CAAA,YAAS;wCAAC,WAAU;kDAA6B,EAAE;;;;;;;;;;;;;;;;;sCAG5D,6LAAC,oIAAA,CAAA,YAAS;sCACL,WAAW,GAAG,CAAC,CAAC,yBACb,6LAAC,oIAAA,CAAA,WAAQ;oCAAmB,WAAU;;sDAClC,6LAAC,oIAAA,CAAA,YAAS;4CAAC,WAAU;sDACjB,cAAA,6LAAC;gDAAI,WAAU;0DACX,cAAA,6LAAC;oDACG,KAAK,SAAS,IAAI;oDAClB,KAAK,SAAS,IAAI;oDAClB,WAAU;;;;;;;;;;;;;;;;sDAItB,6LAAC,oIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAgC,SAAS,IAAI;;;;;;sDAClE,6LAAC,oIAAA,CAAA,YAAS;4CAAC,WAAU;sDAChB,SAAS,SAAS,GACb,IAAI,KAAK,SAAS,SAAS,EAAE,kBAAkB,KAC/C;;;;;;sDAGV,6LAAC,oIAAA,CAAA,YAAS;4CAAC,WAAU;sDACjB,cAAA,6LAAC;gDAAI,WAAU;;kEACX,6LAAC,qIAAA,CAAA,SAAM;wDACH,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,OAAO;wDACtB,WAAU;kEAEV,cAAA,6LAAC,8MAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAEpB,6LAAC,qIAAA,CAAA,SAAM;wDACH,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,aAAa,SAAS,EAAE;wDACvC,UAAU,eAAe,SAAS,EAAE;wDACpC,WAAU;kEAET,eAAe,SAAS,EAAE,iBACvB,6LAAC,oNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;iFAEnB,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;mCArCvB,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;YAgDzC,yBACG,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACH,SAAQ;oBACR,SAAS;oBACT,UAAU;8BAET,0BACG;;0CACI,6LAAC,oNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BAClB,EAAE;;uCAGP,EAAE;;;;;;;;;;;;;;;;;AAO9B;GApJwB;;QACV,yMAAA,CAAA,kBAAe;QASrB,mIAAA,CAAA,mBAAgB;;;KAVA", "debugId": null}}, {"offset": {"line": 1593, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/CAMP2024/FRONT_END/My%20Full%20Projects/room-mate/room-mate/src/app/%5Blocale%5D/%28dashboard%29/dashboard/categories/components/CategoriesContent.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport CategoriesHeader from \"./CategoriesHeader\";\r\nimport CategoryModal from \"./CategoryModal\";\r\nimport CategoriesTable from \"./CategoriesTable\";\r\n\r\ninterface Category {\r\n    id?: string;\r\n    name: string;\r\n    icon: string;\r\n}\r\n\r\nexport default function CategoriesContent() {\r\n    const [isModalOpen, setIsModalOpen] = useState(false);\r\n    const [selectedCategory, setSelectedCategory] = useState<Category | undefined>(undefined);\r\n    const [refreshTrigger, setRefreshTrigger] = useState(0);\r\n\r\n    const handleAddClick = () => {\r\n        setSelectedCategory(undefined);\r\n        setIsModalOpen(true);\r\n    };\r\n\r\n    const handleEditClick = (category: Category) => {\r\n        setSelectedCategory(category);\r\n        setIsModalOpen(true);\r\n    };\r\n\r\n    const handleModalClose = () => {\r\n        setIsModalOpen(false);\r\n        setSelectedCategory(undefined);\r\n    };\r\n\r\n    const handleModalSuccess = () => {\r\n        setRefreshTrigger(prev => prev + 1);\r\n    };\r\n\r\n    return (\r\n        <div className=\"space-y-6\">\r\n            <CategoriesHeader onAddClick={handleAddClick} />\r\n            <CategoriesTable\r\n                onEdit={handleEditClick}\r\n                refreshTrigger={refreshTrigger}\r\n            />\r\n            <CategoryModal\r\n                isOpen={isModalOpen}\r\n                onClose={handleModalClose}\r\n                category={selectedCategory}\r\n                onSuccess={handleModalSuccess}\r\n            />\r\n        </div>\r\n    );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAae,SAAS;;IACpB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IAC/E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,iBAAiB;QACnB,oBAAoB;QACpB,eAAe;IACnB;IAEA,MAAM,kBAAkB,CAAC;QACrB,oBAAoB;QACpB,eAAe;IACnB;IAEA,MAAM,mBAAmB;QACrB,eAAe;QACf,oBAAoB;IACxB;IAEA,MAAM,qBAAqB;QACvB,kBAAkB,CAAA,OAAQ,OAAO;IACrC;IAEA,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC,0MAAA,CAAA,UAAgB;gBAAC,YAAY;;;;;;0BAC9B,6LAAC,yMAAA,CAAA,UAAe;gBACZ,QAAQ;gBACR,gBAAgB;;;;;;0BAEpB,6LAAC,uMAAA,CAAA,UAAa;gBACV,QAAQ;gBACR,SAAS;gBACT,UAAU;gBACV,WAAW;;;;;;;;;;;;AAI3B;GAvCwB;KAAA", "debugId": null}}]}
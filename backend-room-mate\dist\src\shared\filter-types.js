"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateFilters = exports.FILTER_CONSTANTS = exports.FILTER_EXAMPLES = void 0;
const client_1 = require("@prisma/client");
exports.FILTER_EXAMPLES = {
    locationOnly: {
        country: 'Egypt',
        city: 'Cairo',
        neighborhood: 'New Cairo',
    },
    premiumAmenities: {
        includeFurniture: true,
        airConditioning: true,
        parking: true,
        elevator: true,
        internet: true,
        nearToMetro: true,
    },
    budgetFriendly: {
        maxPrice: '4000',
        isAvailable: true,
        goodForForeigners: true,
    },
    highQuality: {
        minRating: 4.0,
        minTotalRatings: 5,
        isVerified: true,
        isAvailable: true,
    },
    recentListings: {
        createdAfter: '2024-01-01T00:00:00.000Z',
        sortBy: 'createdAt',
        sortOrder: 'desc',
    },
    complexPersonFilter: {
        search: 'professional engineer',
        country: 'Egypt',
        city: 'Cairo',
        type: 'house',
        roomType: 'single',
        minPrice: '4000',
        maxPrice: '8000',
        includeFurniture: true,
        airConditioning: true,
        internet: true,
        nearToMetro: true,
        goodForForeigners: true,
        isVerified: true,
        isAvailable: true,
        minRating: 3.5,
        page: 1,
        limit: 10,
        sortBy: 'rating',
        sortOrder: 'desc',
    },
    complexPropertyFilter: {
        search: 'furnished apartment',
        country: 'Egypt',
        city: 'Cairo',
        neighborhood: 'Zamalek',
        type: 'house',
        totalRooms: '2',
        roomsToComplete: '1',
        minPrice: '6000',
        maxPrice: '12000',
        separatedBathroom: true,
        includeFurniture: true,
        airConditioning: true,
        parking: true,
        elevator: true,
        goodForForeigners: true,
        isVerified: true,
        minRating: 4.0,
        page: 1,
        limit: 5,
        sortBy: 'rating',
        sortOrder: 'desc',
    },
};
exports.FILTER_CONSTANTS = {
    PropertyTypes: Object.values(client_1.PropertyType),
    RoomTypes: Object.values(client_1.RoomType),
    RentTimes: Object.values(client_1.RentTime),
    PaymentTimes: Object.values(client_1.PaymentTime),
    SortFields: ['createdAt', 'updatedAt', 'price', 'rating', 'title'],
    SortOrders: ['asc', 'desc'],
    GenderOptions: ['male', 'female', 'mixed'],
};
exports.validateFilters = {
    hasLocationFilters: (filters) => {
        return !!(filters.city ||
            filters.country ||
            filters.neighborhood ||
            filters.address ||
            filters.latitude ||
            filters.longitude);
    },
    hasAmenityFilters: (filters) => {
        return Object.values(filters).some((value) => typeof value === 'boolean');
    },
    hasPriceFilters: (filters) => {
        return !!(filters.minPrice || filters.maxPrice);
    },
    hasDateFilters: (filters) => {
        return !!(filters.createdAfter ||
            filters.createdBefore ||
            filters.updatedAfter ||
            filters.updatedBefore);
    },
};
//# sourceMappingURL=filter-types.js.map
'use client'

import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useTranslations } from 'next-intl'
import { Loader2, AlertCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { TypeToggle } from '@/components/homepage/type-toggle'
import { FiltersToolbar } from '@/components/homepage/filters-toolbar'
import { OfferCard } from '@/components/offers/offer-card'
import { getOffers } from '@/actions/offers'
import { Offer, ViewType, SortOption, ViewMode } from '@/lib/types'
import { cn } from '@/lib/utils'
interface OffersContentProps {
    className?: string
}

export function OffersContent({ className }: OffersContentProps) {
    const t = useTranslations('offers')

    // State management
    const [viewType, setViewType] = useState<ViewType>('properties')
    const [selectedCategory] = useState('all')
    const [currentPage, setCurrentPage] = useState(1)
    const [sortBy, setSortBy] = useState<SortOption>('newest')
    const [viewMode, setViewMode] = useState<ViewMode>('grid')

    // Offers query
    const offersQuery = useQuery({
        queryKey: ['offers', selectedCategory, currentPage, sortBy, viewType],
        queryFn: () => getOffers({
            page: currentPage,
            limit: 12,
            categoryId: selectedCategory === 'all' ? undefined : selectedCategory,
            sortBy: 'createdAt',
            sortOrder: sortBy === 'newest' || sortBy === 'popular' ? 'desc' : 'asc'
        }),
        staleTime: 5 * 60 * 1000, // 5 minutes
    })

    // Get current query based on view type
    const currentQuery = offersQuery

    // Handle different API response structures
    // API service wraps response in { data: actualResponse, status: number }
    const offers = currentQuery.data || []

    // Handle contact action
    const handleContact = (offerId: string) => {
        // Find the offer to get contact information
        const offer = offers.find((o: Offer) => o.id === offerId)
        if (offer) {
            // Open phone dialer or copy phone number
            window.open(`tel:${offer.phone}`, '_self')
        }
    }

    // Handle refresh
    const handleRefresh = () => {
        currentQuery.refetch()
    }

    // Loading state for initial load
    if (currentQuery.isLoading && currentPage === 1) {
        return (
            <div className="flex items-center justify-center min-h-[400px]">
                <div className="text-center space-y-4">
                    <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
                    <p className="text-muted-foreground">
                        {t('loading')}
                    </p>
                </div>
            </div>
        )
    }

    // Error state
    if (currentQuery.isError) {
        return (
            <div className="flex items-center justify-center min-h-[400px]">
                <div className="text-center space-y-4">
                    <AlertCircle className="h-8 w-8 mx-auto text-destructive" />
                    <div>
                        <h3 className="font-semibold mb-2">Something went wrong</h3>
                        <p className="text-muted-foreground mb-4">
                            {t('errors.loadingOffers')}
                        </p>
                        <Button onClick={handleRefresh}>
                            Try Again
                        </Button>
                    </div>
                </div>
            </div>
        )
    }

    return (
        <div className={cn("w-full space-y-6", className)}>
            {/* Filters Section */}
            <div className="space-y-4">
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 justify-between">
                    <TypeToggle value={viewType} onChange={setViewType} />
                    <FiltersToolbar
                        sortBy={sortBy}
                        onSortChange={setSortBy}
                        viewMode={viewMode}
                        onViewModeChange={setViewMode}
                    />
                </div>
            </div>

            {/* Results Section */}
            <div className="space-y-6">
                {/* Results Count */}
                {offers && offers.length > 0 && (
                    <div className="text-sm text-muted-foreground">
                        Showing {offers.length} offers
                    </div>
                )}

                {/* Offers */}
                {currentQuery.isLoading && currentPage > 1 ? (
                    <div className="flex justify-center items-center py-8">
                        <div className="text-center space-y-4">
                            <Loader2 className="h-6 w-6 animate-spin mx-auto text-primary" />
                            <p className="text-muted-foreground text-sm">Loading more offers...</p>
                        </div>
                    </div>
                ) : !offers || offers.length === 0 ? (
                    <div className="flex justify-center items-center py-8">
                        <div className="text-center space-y-4">
                            <div className="text-muted-foreground">
                                No offers found
                            </div>
                            <Button variant="outline" onClick={handleRefresh}>
                                Refresh
                            </Button>
                        </div>
                    </div>
                ) : (
                    <div className={cn(
                        "grid gap-6",
                        viewMode === 'grid'
                            ? "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
                            : "grid-cols-1"
                    )}>
                        {offers.map((offer: Offer) => (
                            <OfferCard
                                key={offer.id}
                                offer={offer}
                                onContact={handleContact}
                                viewType={viewType}
                            />
                        ))}
                    </div>
                )}

                {/* Pagination */}
                {offers && offers.length > 0 && (
                    <div className="flex justify-center items-center gap-4 pt-6">
                        <Button
                            variant="outline"
                            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                            disabled={currentPage === 1 || currentQuery.isLoading}
                        >
                            Previous
                        </Button>
                        <span className="text-sm text-muted-foreground">
                            Page {currentPage}
                        </span>
                        <Button
                            variant="outline"
                            onClick={() => setCurrentPage(prev => prev + 1)}
                            disabled={currentQuery.isLoading || offers.length < 12}
                        >
                            Next
                        </Button>
                    </div>
                )}
            </div>
        </div>
    )
}
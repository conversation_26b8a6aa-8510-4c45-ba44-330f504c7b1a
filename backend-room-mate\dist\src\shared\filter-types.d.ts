import { PropertyType, RoomType, RentTime, PaymentTime } from '@prisma/client';
export interface PersonFilters {
    page?: number;
    limit?: number;
    search?: string;
    title?: string;
    slug?: string;
    city?: string;
    country?: string;
    neighborhood?: string;
    address?: string;
    latitude?: string;
    longitude?: string;
    type?: PropertyType;
    roomType?: RoomType;
    genderRequired?: string;
    totalRooms?: string;
    availableRooms?: string;
    size?: string;
    floor?: string;
    bathrooms?: string;
    separatedBathroom?: boolean;
    residentsCount?: string;
    availablePersons?: string;
    minPrice?: string;
    maxPrice?: string;
    rentTime?: RentTime;
    paymentTime?: PaymentTime;
    priceIncludeWaterAndElectricity?: boolean;
    minRating?: number;
    minTotalRatings?: number;
    isVerified?: boolean;
    isAvailable?: boolean;
    categoryId?: string;
    ownerId?: string;
    createdAfter?: string;
    createdBefore?: string;
    updatedAfter?: string;
    updatedBefore?: string;
    allowSmoking?: boolean;
    includeFurniture?: boolean;
    airConditioning?: boolean;
    includeWaterHeater?: boolean;
    parking?: boolean;
    internet?: boolean;
    nearToMetro?: boolean;
    nearToMarket?: boolean;
    elevator?: boolean;
    trialPeriod?: boolean;
    goodForForeigners?: boolean;
    sortBy?: 'createdAt' | 'updatedAt' | 'price' | 'rating' | 'title';
    sortOrder?: 'asc' | 'desc';
}
export interface PropertyFilters {
    page?: number;
    limit?: number;
    search?: string;
    title?: string;
    slug?: string;
    city?: string;
    country?: string;
    neighborhood?: string;
    address?: string;
    latitude?: string;
    longitude?: string;
    type?: PropertyType;
    roomType?: RoomType;
    genderRequired?: string;
    totalRooms?: string;
    availableRooms?: string;
    roomsToComplete?: string;
    size?: string;
    floor?: string;
    bathrooms?: string;
    separatedBathroom?: boolean;
    residentsCount?: string;
    availablePersons?: string;
    minPrice?: string;
    maxPrice?: string;
    rentTime?: RentTime;
    paymentTime?: PaymentTime;
    priceIncludeWaterAndElectricity?: boolean;
    minRating?: number;
    minTotalRatings?: number;
    isVerified?: boolean;
    isAvailable?: boolean;
    categoryId?: string;
    ownerId?: string;
    createdAfter?: string;
    createdBefore?: string;
    updatedAfter?: string;
    updatedBefore?: string;
    allowSmoking?: boolean;
    includeFurniture?: boolean;
    airConditioning?: boolean;
    includeWaterHeater?: boolean;
    parking?: boolean;
    internet?: boolean;
    nearToMetro?: boolean;
    nearToMarket?: boolean;
    elevator?: boolean;
    trialPeriod?: boolean;
    goodForForeigners?: boolean;
    sortBy?: 'createdAt' | 'updatedAt' | 'price' | 'rating' | 'title';
    sortOrder?: 'asc' | 'desc';
}
export interface LocationFilters {
    city?: string;
    country?: string;
    neighborhood?: string;
    address?: string;
    latitude?: string;
    longitude?: string;
}
export interface AmenityFilters {
    allowSmoking?: boolean;
    includeFurniture?: boolean;
    airConditioning?: boolean;
    includeWaterHeater?: boolean;
    parking?: boolean;
    internet?: boolean;
    nearToMetro?: boolean;
    nearToMarket?: boolean;
    elevator?: boolean;
    trialPeriod?: boolean;
    goodForForeigners?: boolean;
    separatedBathroom?: boolean;
    priceIncludeWaterAndElectricity?: boolean;
}
export interface QualityFilters {
    minPrice?: string;
    maxPrice?: string;
    minRating?: number;
    minTotalRatings?: number;
    isVerified?: boolean;
    isAvailable?: boolean;
}
export interface DateRangeFilters {
    createdAfter?: string;
    createdBefore?: string;
    updatedAfter?: string;
    updatedBefore?: string;
}
export interface SpaceFilters {
    totalRooms?: string;
    availableRooms?: string;
    roomsToComplete?: string;
    size?: string;
    floor?: string;
    bathrooms?: string;
    residentsCount?: string;
    availablePersons?: string;
}
export interface PaginationFilters {
    page?: number;
    limit?: number;
    sortBy?: 'createdAt' | 'updatedAt' | 'price' | 'rating' | 'title';
    sortOrder?: 'asc' | 'desc';
}
export interface SearchFilters {
    search?: string;
    title?: string;
    slug?: string;
}
export interface BaseFilters extends LocationFilters, AmenityFilters, QualityFilters, DateRangeFilters, PaginationFilters, SearchFilters {
    type?: PropertyType;
    roomType?: RoomType;
    genderRequired?: string;
    totalRooms?: string;
    availableRooms?: string;
    size?: string;
    floor?: string;
    bathrooms?: string;
    residentsCount?: string;
    availablePersons?: string;
    rentTime?: RentTime;
    paymentTime?: PaymentTime;
    categoryId?: string;
    ownerId?: string;
}
export type AnyFilters = PersonFilters | PropertyFilters;
export declare const FILTER_EXAMPLES: {
    locationOnly: LocationFilters;
    premiumAmenities: AmenityFilters;
    budgetFriendly: QualityFilters;
    highQuality: QualityFilters;
    recentListings: DateRangeFilters & PaginationFilters;
    complexPersonFilter: PersonFilters;
    complexPropertyFilter: PropertyFilters;
};
export declare const FILTER_CONSTANTS: {
    PropertyTypes: ("house" | "room")[];
    RoomTypes: ("mixed" | "single")[];
    RentTimes: ("daily" | "weekly" | "monthly" | "quarterly" | "semiannual" | "annually")[];
    PaymentTimes: ("daily" | "weekly" | "monthly" | "quarterly" | "semiannual" | "annually")[];
    SortFields: readonly ["createdAt", "updatedAt", "price", "rating", "title"];
    SortOrders: readonly ["asc", "desc"];
    GenderOptions: readonly ["male", "female", "mixed"];
};
export declare const validateFilters: {
    hasLocationFilters: (filters: LocationFilters) => boolean;
    hasAmenityFilters: (filters: AmenityFilters) => boolean;
    hasPriceFilters: (filters: QualityFilters) => boolean;
    hasDateFilters: (filters: DateRangeFilters) => boolean;
};

import { RatingsService } from './ratings.service';
import { CreateRatingDto } from './dto/create-rating.dto';
import { UpdateRatingDto } from './dto/update-rating.dto';
import { CreateBookingRatingDto } from './dto/create-booking-rating.dto';
import { UserPayload } from '../users/interfaces/user.interface';
export declare class RatingsController {
    private readonly ratingsService;
    constructor(ratingsService: RatingsService);
    create(user: UserPayload, createRatingDto: CreateRatingDto): Promise<{
        user: {
            id: string;
            name: string;
        };
        property: {
            id: string;
            country: string | null;
            isVerified: boolean;
            createdAt: Date;
            updatedAt: Date;
            title: string | null;
            userId: string | null;
            slug: string | null;
            images: string[];
            city: string | null;
            neighborhood: string | null;
            address: string | null;
            description: string | null;
            latitude: string | null;
            longitude: string | null;
            type: import(".prisma/client").$Enums.PropertyType;
            roomType: import(".prisma/client").$Enums.RoomType;
            genderRequired: string | null;
            totalRooms: string | null;
            availableRooms: string | null;
            roomsToComplete: string | null;
            price: string | null;
            size: string | null;
            floor: string | null;
            bathrooms: string | null;
            separatedBathroom: boolean;
            residentsCount: string | null;
            availablePersons: string | null;
            rentTime: import(".prisma/client").$Enums.RentTime;
            paymentTime: import(".prisma/client").$Enums.PaymentTime;
            priceIncludeWaterAndElectricity: boolean;
            allowSmoking: boolean;
            includeFurniture: boolean;
            airConditioning: boolean;
            includeWaterHeater: boolean;
            parking: boolean;
            internet: boolean;
            nearToMetro: boolean;
            nearToMarket: boolean;
            elevator: boolean;
            trialPeriod: boolean;
            goodForForeigners: boolean;
            rating: number;
            totalRatings: number;
            termsAndConditions: string | null;
            isAvailable: boolean;
            categoryId: string;
            ownerId: string;
        } | null;
        person: {
            id: string;
            country: string | null;
            isVerified: boolean;
            createdAt: Date;
            updatedAt: Date;
            title: string | null;
            slug: string | null;
            images: string[];
            city: string | null;
            neighborhood: string | null;
            address: string | null;
            description: string | null;
            latitude: string | null;
            longitude: string | null;
            type: import(".prisma/client").$Enums.PropertyType;
            roomType: import(".prisma/client").$Enums.RoomType;
            genderRequired: string | null;
            totalRooms: string | null;
            availableRooms: string | null;
            price: string | null;
            size: string | null;
            floor: string | null;
            bathrooms: string | null;
            separatedBathroom: boolean;
            residentsCount: string | null;
            availablePersons: string | null;
            rentTime: import(".prisma/client").$Enums.RentTime;
            paymentTime: import(".prisma/client").$Enums.PaymentTime;
            priceIncludeWaterAndElectricity: boolean;
            allowSmoking: boolean;
            includeFurniture: boolean;
            airConditioning: boolean;
            includeWaterHeater: boolean;
            parking: boolean;
            internet: boolean;
            nearToMetro: boolean;
            nearToMarket: boolean;
            elevator: boolean;
            trialPeriod: boolean;
            goodForForeigners: boolean;
            rating: number;
            totalRatings: number;
            termsAndConditions: string | null;
            isAvailable: boolean;
            categoryId: string;
            ownerId: string;
        } | null;
    } & {
        id: string;
        createdAt: Date;
        userId: string;
        propertyId: string | null;
        personId: string | null;
        score: number;
        comment: string | null;
    }>;
    createFromBooking(user: UserPayload, createBookingRatingDto: CreateBookingRatingDto): Promise<{
        user: {
            id: string;
            name: string;
        };
        property: {
            id: string;
            title: string | null;
        } | null;
    } & {
        id: string;
        createdAt: Date;
        userId: string;
        propertyId: string | null;
        personId: string | null;
        score: number;
        comment: string | null;
    }>;
    findByProperty(propertyId: string): Promise<({
        user: {
            id: string;
            name: string;
        };
    } & {
        id: string;
        createdAt: Date;
        userId: string;
        propertyId: string | null;
        personId: string | null;
        score: number;
        comment: string | null;
    })[]>;
    getPropertyStats(propertyId: string): Promise<{
        averageRating: number;
        totalRatings: number;
    }>;
    findMyRatings(user: UserPayload): Promise<({
        property: {
            id: string;
            title: string | null;
            images: string[];
        } | null;
        person: {
            id: string;
            title: string | null;
            images: string[];
        } | null;
    } & {
        id: string;
        createdAt: Date;
        userId: string;
        propertyId: string | null;
        personId: string | null;
        score: number;
        comment: string | null;
    })[]>;
    findByUser(userId: string): Promise<({
        property: {
            id: string;
            title: string | null;
            images: string[];
        } | null;
        person: {
            id: string;
            title: string | null;
            images: string[];
        } | null;
    } & {
        id: string;
        createdAt: Date;
        userId: string;
        propertyId: string | null;
        personId: string | null;
        score: number;
        comment: string | null;
    })[]>;
    findMyRatingForProperty(user: UserPayload, propertyId: string): Promise<({
        property: {
            id: string;
            title: string | null;
        } | null;
    } & {
        id: string;
        createdAt: Date;
        userId: string;
        propertyId: string | null;
        personId: string | null;
        score: number;
        comment: string | null;
    }) | null>;
    update(user: UserPayload, id: string, updateRatingDto: UpdateRatingDto): Promise<{
        user: {
            id: string;
            name: string;
        };
        property: {
            id: string;
            title: string | null;
        } | null;
    } & {
        id: string;
        createdAt: Date;
        userId: string;
        propertyId: string | null;
        personId: string | null;
        score: number;
        comment: string | null;
    }>;
    remove(user: UserPayload, id: string): Promise<{
        message: string;
    }>;
}

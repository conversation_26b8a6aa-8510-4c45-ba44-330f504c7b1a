"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
const bcrypt = require("bcryptjs");
let UsersService = class UsersService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    userSelectFields = {
        id: true,
        name: true,
        email: true,
        phone: true,
        smoker: true,
        age: true,
        gender: true,
        nationality: true,
        occupation: true,
        country: true,
        isAdmin: true,
        isVerified: true,
        createdAt: true,
        updatedAt: true,
    };
    async create(createUserDto) {
        const { email, password, ...userData } = createUserDto;
        const existingUser = await this.prisma.user.findUnique({
            where: { email },
        });
        if (existingUser) {
            throw new common_1.ConflictException('User with this email already exists');
        }
        const saltRounds = 12;
        const hashedPassword = await bcrypt.hash(password, saltRounds);
        try {
            const user = await this.prisma.user.create({
                data: {
                    email,
                    password: hashedPassword,
                    ...userData,
                },
                select: this.userSelectFields,
            });
            return user;
        }
        catch {
            throw new common_1.BadRequestException('Failed to create user');
        }
    }
    async findAll(query, currentUser) {
        if (!currentUser.isAdmin) {
            throw new common_1.ForbiddenException('Only admins can view all users');
        }
        const { page = 1, limit = 10, search, gender, smoker, isVerified, isAdmin, nationality, sortBy = 'createdAt', sortOrder = 'desc', } = query;
        const skip = (page - 1) * limit;
        const where = {};
        if (search) {
            where.OR = [
                { name: { contains: search, mode: 'insensitive' } },
                { email: { contains: search, mode: 'insensitive' } },
                { occupation: { contains: search, mode: 'insensitive' } },
            ];
        }
        if (gender)
            where.gender = gender;
        if (typeof smoker === 'boolean')
            where.smoker = smoker;
        if (typeof isVerified === 'boolean')
            where.isVerified = isVerified;
        if (typeof isAdmin === 'boolean')
            where.isAdmin = isAdmin;
        if (nationality)
            where.nationality = nationality;
        const total = await this.prisma.user.count({ where });
        const users = await this.prisma.user.findMany({
            where,
            select: this.userSelectFields,
            skip,
            take: limit,
            orderBy: { [sortBy]: sortOrder },
        });
        const totalPages = Math.ceil(total / limit);
        return {
            users,
            pagination: {
                page,
                limit,
                total,
                totalPages,
                hasNext: page < totalPages,
                hasPrev: page > 1,
            },
        };
    }
    async findOne(id) {
        const user = await this.prisma.user.findUnique({
            where: { id },
            select: this.userSelectFields,
        });
        if (!user) {
            throw new common_1.NotFoundException(`User with ID ${id} not found`);
        }
        return user;
    }
    async update(id, updateUserDto, currentUser) {
        const existingUser = await this.findOne(id);
        if (!currentUser.isAdmin && currentUser.sub !== id) {
            throw new common_1.ForbiddenException('You can only update your own profile');
        }
        if (updateUserDto.email && updateUserDto.email !== existingUser.email) {
            const emailConflict = await this.prisma.user.findUnique({
                where: { email: updateUserDto.email },
            });
            if (emailConflict) {
                throw new common_1.ConflictException('Email already in use');
            }
        }
        try {
            const updatedUser = await this.prisma.user.update({
                where: { id },
                data: updateUserDto,
                select: this.userSelectFields,
            });
            return updatedUser;
        }
        catch {
            throw new common_1.BadRequestException('Failed to update user');
        }
    }
    async changePassword(id, changePasswordDto, currentUser) {
        if (!currentUser.isAdmin && currentUser.sub !== id) {
            throw new common_1.ForbiddenException('You can only change your own password');
        }
        const user = await this.prisma.user.findUnique({
            where: { id },
            select: { id: true, password: true },
        });
        if (!user) {
            throw new common_1.NotFoundException(`User with ID ${id} not found`);
        }
        if (!currentUser.isAdmin) {
            const isCurrentPasswordValid = await bcrypt.compare(changePasswordDto.currentPassword, user.password);
            if (!isCurrentPasswordValid) {
                throw new common_1.UnauthorizedException('Current password is incorrect');
            }
        }
        const saltRounds = 12;
        const hashedNewPassword = await bcrypt.hash(changePasswordDto.newPassword, saltRounds);
        try {
            await this.prisma.user.update({
                where: { id },
                data: { password: hashedNewPassword },
            });
            return { message: 'Password changed successfully' };
        }
        catch {
            throw new common_1.BadRequestException('Failed to change password');
        }
    }
    async remove(id, currentUser) {
        if (!currentUser.isAdmin) {
            throw new common_1.ForbiddenException('Only admins can delete users');
        }
        if (currentUser.sub === id) {
            throw new common_1.BadRequestException('You cannot delete your own account');
        }
        const user = await this.findOne(id);
        try {
            await this.prisma.user.delete({
                where: { id },
            });
            return { message: `User ${user.name} deleted successfully` };
        }
        catch {
            throw new common_1.BadRequestException('Failed to delete user');
        }
    }
    async toggleVerification(id, currentUser) {
        if (!currentUser.isAdmin) {
            throw new common_1.ForbiddenException('Only admins can toggle verification status');
        }
        const user = await this.findOne(id);
        try {
            const updatedUser = await this.prisma.user.update({
                where: { id },
                data: { isVerified: !user.isVerified },
                select: this.userSelectFields,
            });
            return updatedUser;
        }
        catch {
            throw new common_1.BadRequestException('Failed to toggle verification status');
        }
    }
    async toggleAdminStatus(id, currentUser) {
        if (!currentUser.isAdmin) {
            throw new common_1.ForbiddenException('Only admins can toggle admin status');
        }
        if (currentUser.sub === id) {
            throw new common_1.BadRequestException('You cannot change your own admin status');
        }
        const user = await this.findOne(id);
        try {
            const updatedUser = await this.prisma.user.update({
                where: { id },
                data: { isAdmin: !user.isAdmin },
                select: this.userSelectFields,
            });
            return updatedUser;
        }
        catch {
            throw new common_1.BadRequestException('Failed to toggle admin status');
        }
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], UsersService);
//# sourceMappingURL=users.service.js.map
import { UploadResponseDto, MultipleUploadResponseDto } from './upload.dto';
export declare const singleUploadResponse: UploadResponseDto;
export declare const multipleUploadResponse: MultipleUploadResponseDto;
export declare const apiEndpointExamples: {
    uploadSingle: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
            'Content-Type': string;
        };
        body: string;
    };
    uploadMultiple: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
            'Content-Type': string;
        };
        body: string;
    };
};
export declare const validationExamples: {
    allowedFileTypes: string[];
    maxFileSize: number;
    maxFiles: number;
};

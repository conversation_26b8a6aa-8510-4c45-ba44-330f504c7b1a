"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommentsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
let CommentsService = class CommentsService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createCommentDto, user) {
        const { propertyId, personId, content } = createCommentDto;
        if (!propertyId && !personId) {
            throw new common_1.BadRequestException('Either propertyId or personId must be provided');
        }
        if (propertyId && personId) {
            throw new common_1.BadRequestException('Cannot comment on both property and person simultaneously');
        }
        if (propertyId) {
            const property = await this.prisma.property.findUnique({
                where: { id: propertyId },
            });
            if (!property) {
                throw new common_1.NotFoundException('Property not found');
            }
        }
        if (personId) {
            const person = await this.prisma.person.findUnique({
                where: { id: personId },
            });
            if (!person) {
                throw new common_1.NotFoundException('Person not found');
            }
        }
        return this.prisma.comment.create({
            data: {
                content,
                userId: user.sub,
                propertyId,
                personId,
            },
            include: {
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        isVerified: true,
                    },
                },
                property: propertyId
                    ? {
                        select: {
                            id: true,
                            title: true,
                            slug: true,
                        },
                    }
                    : false,
                person: personId
                    ? {
                        select: {
                            id: true,
                            title: true,
                            slug: true,
                        },
                    }
                    : false,
            },
        });
    }
    async findAll(query) {
        const { page = '1', limit = '10', propertyId, personId, userId, search, } = query;
        const pageNumber = parseInt(page, 10);
        const limitNumber = parseInt(limit, 10);
        const skip = (pageNumber - 1) * limitNumber;
        const where = {};
        if (propertyId) {
            where.propertyId = propertyId;
        }
        if (personId) {
            where.personId = personId;
        }
        if (userId) {
            where.userId = userId;
        }
        if (search) {
            where.content = {
                contains: search,
                mode: 'insensitive',
            };
        }
        const [comments, total] = await Promise.all([
            this.prisma.comment.findMany({
                where,
                skip,
                take: limitNumber,
                orderBy: { createdAt: 'desc' },
                include: {
                    user: {
                        select: {
                            id: true,
                            name: true,
                            email: true,
                            isVerified: true,
                        },
                    },
                    property: {
                        select: {
                            id: true,
                            title: true,
                            slug: true,
                        },
                    },
                    person: {
                        select: {
                            id: true,
                            title: true,
                            slug: true,
                        },
                    },
                },
            }),
            this.prisma.comment.count({ where }),
        ]);
        return {
            comments,
            pagination: {
                total,
                page: pageNumber,
                limit: limitNumber,
                totalPages: Math.ceil(total / limitNumber),
            },
        };
    }
    async findOne(id) {
        const comment = await this.prisma.comment.findUnique({
            where: { id },
            include: {
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        isVerified: true,
                    },
                },
                property: {
                    select: {
                        id: true,
                        title: true,
                        slug: true,
                    },
                },
                person: {
                    select: {
                        id: true,
                        title: true,
                        slug: true,
                    },
                },
            },
        });
        if (!comment) {
            throw new common_1.NotFoundException('Comment not found');
        }
        return comment;
    }
    async update(id, updateCommentDto, user) {
        const comment = await this.prisma.comment.findUnique({
            where: { id },
        });
        if (!comment) {
            throw new common_1.NotFoundException('Comment not found');
        }
        if (comment.userId !== user.sub && !user.isAdmin) {
            throw new common_1.ForbiddenException('You can only update your own comments');
        }
        return this.prisma.comment.update({
            where: { id },
            data: updateCommentDto,
            include: {
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        isVerified: true,
                    },
                },
                property: {
                    select: {
                        id: true,
                        title: true,
                        slug: true,
                    },
                },
                person: {
                    select: {
                        id: true,
                        title: true,
                        slug: true,
                    },
                },
            },
        });
    }
    async remove(id, user) {
        const comment = await this.prisma.comment.findUnique({
            where: { id },
        });
        if (!comment) {
            throw new common_1.NotFoundException('Comment not found');
        }
        if (comment.userId !== user.sub && !user.isAdmin) {
            throw new common_1.ForbiddenException('You can only delete your own comments');
        }
        await this.prisma.comment.delete({
            where: { id },
        });
        return { message: 'Comment deleted successfully' };
    }
    async getPropertyComments(propertyId, query) {
        const property = await this.prisma.property.findUnique({
            where: { id: propertyId },
        });
        if (!property) {
            throw new common_1.NotFoundException('Property not found');
        }
        return this.findAll({ ...query, propertyId });
    }
    async getPersonComments(personId, query) {
        const person = await this.prisma.person.findUnique({
            where: { id: personId },
        });
        if (!person) {
            throw new common_1.NotFoundException('Person not found');
        }
        return this.findAll({ ...query, personId });
    }
};
exports.CommentsService = CommentsService;
exports.CommentsService = CommentsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], CommentsService);
//# sourceMappingURL=comments.service.js.map
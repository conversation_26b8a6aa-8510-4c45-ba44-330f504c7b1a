import { PrismaService } from '../prisma.service';
import { CreatePropertyDto } from './dto/create-property.dto';
import { UpdatePropertyDto } from './dto/update-property.dto';
import { QueryPropertiesDto } from './dto/query-properties.dto';
import { UserPayload } from '../users/interfaces/user.interface';
export declare class PropertiesService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    create(createPropertyDto: CreatePropertyDto, user: UserPayload): Promise<{
        success: boolean;
        message: string;
        data: {
            category: {
                createdAt: Date;
                updatedAt: Date;
                id: string;
                name: string;
                icon: string | null;
            };
            owner: {
                id: string;
                isVerified: boolean;
                name: string;
                email: string;
                phone: string | null;
            };
            _count: {
                ratings: number;
                favorites: number;
            };
        } & {
            createdAt: Date;
            price: string | null;
            rating: number;
            updatedAt: Date;
            separatedBathroom: boolean;
            priceIncludeWaterAndElectricity: boolean;
            allowSmoking: boolean;
            includeFurniture: boolean;
            airConditioning: boolean;
            includeWaterHeater: boolean;
            parking: boolean;
            internet: boolean;
            nearToMetro: boolean;
            nearToMarket: boolean;
            elevator: boolean;
            trialPeriod: boolean;
            goodForForeigners: boolean;
            termsAndConditions: string | null;
            categoryId: string;
            description: string | null;
            title: string | null;
            id: string;
            slug: string | null;
            images: string[];
            city: string | null;
            country: string | null;
            neighborhood: string | null;
            address: string | null;
            latitude: string | null;
            longitude: string | null;
            type: import(".prisma/client").$Enums.PropertyType;
            roomType: import(".prisma/client").$Enums.RoomType;
            genderRequired: string | null;
            totalRooms: string | null;
            availableRooms: string | null;
            roomsToComplete: string | null;
            size: string | null;
            floor: string | null;
            bathrooms: string | null;
            residentsCount: string | null;
            availablePersons: string | null;
            rentTime: import(".prisma/client").$Enums.RentTime;
            paymentTime: import(".prisma/client").$Enums.PaymentTime;
            totalRatings: number;
            isVerified: boolean;
            isAvailable: boolean;
            ownerId: string;
            userId: string | null;
        };
    }>;
    findAll(query: QueryPropertiesDto): Promise<{
        success: boolean;
        data: ({
            category: {
                createdAt: Date;
                updatedAt: Date;
                id: string;
                name: string;
                icon: string | null;
            };
            owner: {
                id: string;
                isVerified: boolean;
                name: string;
                email: string;
                phone: string | null;
            };
            _count: {
                ratings: number;
                favorites: number;
            };
        } & {
            createdAt: Date;
            price: string | null;
            rating: number;
            updatedAt: Date;
            separatedBathroom: boolean;
            priceIncludeWaterAndElectricity: boolean;
            allowSmoking: boolean;
            includeFurniture: boolean;
            airConditioning: boolean;
            includeWaterHeater: boolean;
            parking: boolean;
            internet: boolean;
            nearToMetro: boolean;
            nearToMarket: boolean;
            elevator: boolean;
            trialPeriod: boolean;
            goodForForeigners: boolean;
            termsAndConditions: string | null;
            categoryId: string;
            description: string | null;
            title: string | null;
            id: string;
            slug: string | null;
            images: string[];
            city: string | null;
            country: string | null;
            neighborhood: string | null;
            address: string | null;
            latitude: string | null;
            longitude: string | null;
            type: import(".prisma/client").$Enums.PropertyType;
            roomType: import(".prisma/client").$Enums.RoomType;
            genderRequired: string | null;
            totalRooms: string | null;
            availableRooms: string | null;
            roomsToComplete: string | null;
            size: string | null;
            floor: string | null;
            bathrooms: string | null;
            residentsCount: string | null;
            availablePersons: string | null;
            rentTime: import(".prisma/client").$Enums.RentTime;
            paymentTime: import(".prisma/client").$Enums.PaymentTime;
            totalRatings: number;
            isVerified: boolean;
            isAvailable: boolean;
            ownerId: string;
            userId: string | null;
        })[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
    }>;
    findOne(id: string): Promise<{
        success: boolean;
        data: {
            offers: ({
                user: {
                    id: string;
                    isVerified: boolean;
                    name: string;
                    email: string;
                    phone: string | null;
                };
            } & {
                createdAt: Date;
                price: string;
                updatedAt: Date;
                id: string;
                userId: string;
                phone: string;
                message: string;
                duration: string | null;
                deposit: boolean;
                status: import(".prisma/client").$Enums.OfferStatus;
                propertyId: string | null;
                personId: string | null;
            })[];
            ratings: ({
                user: {
                    id: string;
                    name: string;
                };
            } & {
                createdAt: Date;
                id: string;
                userId: string;
                propertyId: string | null;
                personId: string | null;
                score: number;
                comment: string | null;
            })[];
            category: {
                createdAt: Date;
                updatedAt: Date;
                id: string;
                name: string;
                icon: string | null;
            };
            owner: {
                id: string;
                isVerified: boolean;
                name: string;
                email: string;
                phone: string | null;
            };
            comments: ({
                user: {
                    id: string;
                    isVerified: boolean;
                    name: string;
                    email: string;
                };
            } & {
                createdAt: Date;
                updatedAt: Date;
                id: string;
                userId: string;
                propertyId: string | null;
                personId: string | null;
                content: string;
            })[];
            _count: {
                offers: number;
                ratings: number;
                favorites: number;
                comments: number;
            };
        } & {
            createdAt: Date;
            price: string | null;
            rating: number;
            updatedAt: Date;
            separatedBathroom: boolean;
            priceIncludeWaterAndElectricity: boolean;
            allowSmoking: boolean;
            includeFurniture: boolean;
            airConditioning: boolean;
            includeWaterHeater: boolean;
            parking: boolean;
            internet: boolean;
            nearToMetro: boolean;
            nearToMarket: boolean;
            elevator: boolean;
            trialPeriod: boolean;
            goodForForeigners: boolean;
            termsAndConditions: string | null;
            categoryId: string;
            description: string | null;
            title: string | null;
            id: string;
            slug: string | null;
            images: string[];
            city: string | null;
            country: string | null;
            neighborhood: string | null;
            address: string | null;
            latitude: string | null;
            longitude: string | null;
            type: import(".prisma/client").$Enums.PropertyType;
            roomType: import(".prisma/client").$Enums.RoomType;
            genderRequired: string | null;
            totalRooms: string | null;
            availableRooms: string | null;
            roomsToComplete: string | null;
            size: string | null;
            floor: string | null;
            bathrooms: string | null;
            residentsCount: string | null;
            availablePersons: string | null;
            rentTime: import(".prisma/client").$Enums.RentTime;
            paymentTime: import(".prisma/client").$Enums.PaymentTime;
            totalRatings: number;
            isVerified: boolean;
            isAvailable: boolean;
            ownerId: string;
            userId: string | null;
        };
    }>;
    findBySlug(slug: string): Promise<{
        success: boolean;
        data: {
            offers: ({
                user: {
                    id: string;
                    isVerified: boolean;
                    name: string;
                    email: string;
                    phone: string | null;
                };
            } & {
                createdAt: Date;
                price: string;
                updatedAt: Date;
                id: string;
                userId: string;
                phone: string;
                message: string;
                duration: string | null;
                deposit: boolean;
                status: import(".prisma/client").$Enums.OfferStatus;
                propertyId: string | null;
                personId: string | null;
            })[];
            ratings: ({
                user: {
                    id: string;
                    name: string;
                };
            } & {
                createdAt: Date;
                id: string;
                userId: string;
                propertyId: string | null;
                personId: string | null;
                score: number;
                comment: string | null;
            })[];
            category: {
                createdAt: Date;
                updatedAt: Date;
                id: string;
                name: string;
                icon: string | null;
            };
            owner: {
                id: string;
                isVerified: boolean;
                name: string;
                email: string;
                phone: string | null;
            };
            comments: ({
                user: {
                    id: string;
                    isVerified: boolean;
                    name: string;
                    email: string;
                };
            } & {
                createdAt: Date;
                updatedAt: Date;
                id: string;
                userId: string;
                propertyId: string | null;
                personId: string | null;
                content: string;
            })[];
            _count: {
                offers: number;
                ratings: number;
                favorites: number;
                comments: number;
            };
        } & {
            createdAt: Date;
            price: string | null;
            rating: number;
            updatedAt: Date;
            separatedBathroom: boolean;
            priceIncludeWaterAndElectricity: boolean;
            allowSmoking: boolean;
            includeFurniture: boolean;
            airConditioning: boolean;
            includeWaterHeater: boolean;
            parking: boolean;
            internet: boolean;
            nearToMetro: boolean;
            nearToMarket: boolean;
            elevator: boolean;
            trialPeriod: boolean;
            goodForForeigners: boolean;
            termsAndConditions: string | null;
            categoryId: string;
            description: string | null;
            title: string | null;
            id: string;
            slug: string | null;
            images: string[];
            city: string | null;
            country: string | null;
            neighborhood: string | null;
            address: string | null;
            latitude: string | null;
            longitude: string | null;
            type: import(".prisma/client").$Enums.PropertyType;
            roomType: import(".prisma/client").$Enums.RoomType;
            genderRequired: string | null;
            totalRooms: string | null;
            availableRooms: string | null;
            roomsToComplete: string | null;
            size: string | null;
            floor: string | null;
            bathrooms: string | null;
            residentsCount: string | null;
            availablePersons: string | null;
            rentTime: import(".prisma/client").$Enums.RentTime;
            paymentTime: import(".prisma/client").$Enums.PaymentTime;
            totalRatings: number;
            isVerified: boolean;
            isAvailable: boolean;
            ownerId: string;
            userId: string | null;
        };
    }>;
    update(id: string, updatePropertyDto: UpdatePropertyDto, user: UserPayload): Promise<{
        success: boolean;
        message: string;
        data: {
            category: {
                createdAt: Date;
                updatedAt: Date;
                id: string;
                name: string;
                icon: string | null;
            };
            owner: {
                id: string;
                isVerified: boolean;
                name: string;
                email: string;
                phone: string | null;
            };
            _count: {
                ratings: number;
                favorites: number;
            };
        } & {
            createdAt: Date;
            price: string | null;
            rating: number;
            updatedAt: Date;
            separatedBathroom: boolean;
            priceIncludeWaterAndElectricity: boolean;
            allowSmoking: boolean;
            includeFurniture: boolean;
            airConditioning: boolean;
            includeWaterHeater: boolean;
            parking: boolean;
            internet: boolean;
            nearToMetro: boolean;
            nearToMarket: boolean;
            elevator: boolean;
            trialPeriod: boolean;
            goodForForeigners: boolean;
            termsAndConditions: string | null;
            categoryId: string;
            description: string | null;
            title: string | null;
            id: string;
            slug: string | null;
            images: string[];
            city: string | null;
            country: string | null;
            neighborhood: string | null;
            address: string | null;
            latitude: string | null;
            longitude: string | null;
            type: import(".prisma/client").$Enums.PropertyType;
            roomType: import(".prisma/client").$Enums.RoomType;
            genderRequired: string | null;
            totalRooms: string | null;
            availableRooms: string | null;
            roomsToComplete: string | null;
            size: string | null;
            floor: string | null;
            bathrooms: string | null;
            residentsCount: string | null;
            availablePersons: string | null;
            rentTime: import(".prisma/client").$Enums.RentTime;
            paymentTime: import(".prisma/client").$Enums.PaymentTime;
            totalRatings: number;
            isVerified: boolean;
            isAvailable: boolean;
            ownerId: string;
            userId: string | null;
        };
    }>;
    remove(id: string, user: UserPayload): Promise<{
        success: boolean;
        message: string;
    }>;
    getMyProperties(userId: string, query: QueryPropertiesDto): Promise<{
        success: boolean;
        data: ({
            category: {
                createdAt: Date;
                updatedAt: Date;
                id: string;
                name: string;
                icon: string | null;
            };
            _count: {
                offers: number;
                ratings: number;
                favorites: number;
            };
        } & {
            createdAt: Date;
            price: string | null;
            rating: number;
            updatedAt: Date;
            separatedBathroom: boolean;
            priceIncludeWaterAndElectricity: boolean;
            allowSmoking: boolean;
            includeFurniture: boolean;
            airConditioning: boolean;
            includeWaterHeater: boolean;
            parking: boolean;
            internet: boolean;
            nearToMetro: boolean;
            nearToMarket: boolean;
            elevator: boolean;
            trialPeriod: boolean;
            goodForForeigners: boolean;
            termsAndConditions: string | null;
            categoryId: string;
            description: string | null;
            title: string | null;
            id: string;
            slug: string | null;
            images: string[];
            city: string | null;
            country: string | null;
            neighborhood: string | null;
            address: string | null;
            latitude: string | null;
            longitude: string | null;
            type: import(".prisma/client").$Enums.PropertyType;
            roomType: import(".prisma/client").$Enums.RoomType;
            genderRequired: string | null;
            totalRooms: string | null;
            availableRooms: string | null;
            roomsToComplete: string | null;
            size: string | null;
            floor: string | null;
            bathrooms: string | null;
            residentsCount: string | null;
            availablePersons: string | null;
            rentTime: import(".prisma/client").$Enums.RentTime;
            paymentTime: import(".prisma/client").$Enums.PaymentTime;
            totalRatings: number;
            isVerified: boolean;
            isAvailable: boolean;
            ownerId: string;
            userId: string | null;
        })[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
    }>;
    toggleFavorite(id: string, user: UserPayload): Promise<{
        success: boolean;
        message: string;
        isFavorite: boolean;
    }>;
    getFavorites(userId: string, query: QueryPropertiesDto): Promise<{
        success: boolean;
        data: ({
            category: {
                createdAt: Date;
                updatedAt: Date;
                id: string;
                name: string;
                icon: string | null;
            };
            owner: {
                id: string;
                isVerified: boolean;
                name: string;
                email: string;
                phone: string | null;
            };
            _count: {
                ratings: number;
                favorites: number;
            };
        } & {
            createdAt: Date;
            price: string | null;
            rating: number;
            updatedAt: Date;
            separatedBathroom: boolean;
            priceIncludeWaterAndElectricity: boolean;
            allowSmoking: boolean;
            includeFurniture: boolean;
            airConditioning: boolean;
            includeWaterHeater: boolean;
            parking: boolean;
            internet: boolean;
            nearToMetro: boolean;
            nearToMarket: boolean;
            elevator: boolean;
            trialPeriod: boolean;
            goodForForeigners: boolean;
            termsAndConditions: string | null;
            categoryId: string;
            description: string | null;
            title: string | null;
            id: string;
            slug: string | null;
            images: string[];
            city: string | null;
            country: string | null;
            neighborhood: string | null;
            address: string | null;
            latitude: string | null;
            longitude: string | null;
            type: import(".prisma/client").$Enums.PropertyType;
            roomType: import(".prisma/client").$Enums.RoomType;
            genderRequired: string | null;
            totalRooms: string | null;
            availableRooms: string | null;
            roomsToComplete: string | null;
            size: string | null;
            floor: string | null;
            bathrooms: string | null;
            residentsCount: string | null;
            availablePersons: string | null;
            rentTime: import(".prisma/client").$Enums.RentTime;
            paymentTime: import(".prisma/client").$Enums.PaymentTime;
            totalRatings: number;
            isVerified: boolean;
            isAvailable: boolean;
            ownerId: string;
            userId: string | null;
        })[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
    }>;
    private generateUniqueSlug;
}

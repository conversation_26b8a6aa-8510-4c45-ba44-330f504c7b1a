"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.responseExamples = exports.apiEndpointExamples = exports.validationExamples = exports.queryExamples = exports.updatePropertyExample = exports.sharedRoomExample = exports.minimalPropertyExample = exports.createPropertyExample = void 0;
const client_1 = require("@prisma/client");
exports.createPropertyExample = {
    title: 'Spacious 2-Bedroom Apartment in New Cairo with Modern Amenities',
    images: [
        'https://example.com/images/property1-main.jpg',
        'https://example.com/images/property1-living.jpg',
        'https://example.com/images/property1-bedroom.jpg',
        'https://example.com/images/property1-kitchen.jpg',
    ],
    city: 'Cairo',
    country: 'Egypt',
    address: '123 New Cairo, Fifth Settlement, Cairo Governorate',
    description: 'Beautiful modern apartment featuring 2 spacious bedrooms, 2 bathrooms, open-plan living area, fully equipped kitchen, and balcony with city views. Located in a prime area with easy access to shopping centers, restaurants, and public transportation.',
    latitude: '30.0444',
    longitude: '31.2357',
    type: 'house',
    roomType: 'single',
    genderRequired: 'mixed',
    totalRooms: '2',
    availableRooms: '1',
    roomsToComplete: '1',
    price: '8000',
    size: '120',
    floor: '3',
    bathrooms: '2',
    separatedBathroom: true,
    residentsCount: '1',
    availablePersons: '2',
    rentTime: 'monthly',
    paymentTime: 'monthly',
    priceIncludeWaterAndElectricity: true,
    allowSmoking: false,
    includeFurniture: true,
    airConditioning: true,
    includeWaterHeater: true,
    parking: true,
    internet: true,
    nearToMetro: true,
    nearToMarket: true,
    elevator: true,
    trialPeriod: false,
    goodForForeigners: true,
    termsAndConditions: 'No pets allowed. Minimum lease period is 6 months. Deposit equivalent to 2 months rent required.',
    categoryId: 'cat-uuid-123',
};
exports.minimalPropertyExample = {
    title: 'Affordable Studio in Downtown Cairo',
    city: 'Cairo',
    country: 'Egypt',
    price: '3500',
    categoryId: 'cat-uuid-456',
};
exports.sharedRoomExample = {
    title: 'Shared Room in Maadi for Students',
    description: 'Perfect for students looking for affordable accommodation in a friendly environment.',
    city: 'Maadi',
    country: 'Egypt',
    type: 'room',
    roomType: 'single',
    genderRequired: 'male',
    price: '2000',
    rentTime: 'monthly',
    allowSmoking: false,
    goodForForeigners: true,
    categoryId: 'cat-uuid-789',
};
exports.updatePropertyExample = {
    title: 'Updated: Spacious 2-Bedroom Apartment in New Cairo with Premium Amenities',
    price: '8500',
    availableRooms: '0',
    description: 'Recently renovated with premium finishes and smart home features.',
    airConditioning: true,
    parking: true,
};
exports.queryExamples = {
    getAllProperties: {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc',
    },
    searchProperties: {
        search: 'apartment cairo',
        page: 1,
        limit: 20,
        sortBy: 'price',
        sortOrder: 'asc',
    },
    filterByTitle: {
        title: 'spacious apartment',
        page: 1,
        limit: 10,
    },
    filterBySlug: {
        slug: 'spacious-2-bedroom-apartment-new-cairo-modern-amenities',
        page: 1,
        limit: 1,
    },
    filterByLocation: {
        city: 'Cairo',
        country: 'Egypt',
        neighborhood: 'New Cairo',
        page: 1,
        limit: 15,
    },
    filterByAddress: {
        address: 'Fifth Settlement',
        page: 1,
        limit: 10,
    },
    filterByCoordinates: {
        latitude: '30.0444',
        longitude: '31.2357',
        page: 1,
        limit: 5,
    },
    filterByType: {
        type: 'house',
        roomType: 'single',
        page: 1,
        limit: 10,
    },
    filterByGender: {
        genderRequired: 'mixed',
        page: 1,
        limit: 10,
    },
    filterByRoomSpecs: {
        totalRooms: '2',
        availableRooms: '1',
        roomsToComplete: '1',
        bathrooms: '2',
        separatedBathroom: true,
        page: 1,
        limit: 10,
    },
    filterBySpaceSpecs: {
        size: '120',
        floor: '3',
        residentsCount: '1',
        availablePersons: '2',
        page: 1,
        limit: 10,
    },
    filterByPrice: {
        minPrice: '5000',
        maxPrice: '10000',
        page: 1,
        limit: 10,
        sortBy: 'price',
        sortOrder: 'asc',
    },
    filterByTerms: {
        rentTime: 'monthly',
        paymentTime: 'monthly',
        priceIncludeWaterAndElectricity: true,
        page: 1,
        limit: 10,
    },
    filterByCategory: {
        categoryId: 'cat-uuid-123',
        page: 1,
        limit: 10,
    },
    filterByOwner: {
        ownerId: 'user-uuid-456',
        page: 1,
        limit: 10,
    },
    filterByRating: {
        minRating: 4.0,
        minTotalRatings: 10,
        page: 1,
        limit: 10,
        sortBy: 'rating',
        sortOrder: 'desc',
    },
    filterByDateRange: {
        createdAfter: '2024-01-01T00:00:00.000Z',
        createdBefore: '2024-12-31T23:59:59.999Z',
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc',
    },
    filterByRecentUpdates: {
        updatedAfter: '2024-12-01T00:00:00.000Z',
        page: 1,
        limit: 10,
        sortBy: 'updatedAt',
        sortOrder: 'desc',
    },
    filterByStatus: {
        isVerified: true,
        isAvailable: true,
        page: 1,
        limit: 10,
        sortBy: 'rating',
        sortOrder: 'desc',
    },
    filterByRentalPrefs: {
        allowSmoking: false,
        trialPeriod: true,
        goodForForeigners: true,
        page: 1,
        limit: 10,
    },
    filterByAmenities: {
        airConditioning: true,
        parking: true,
        internet: true,
        includeFurniture: true,
        includeWaterHeater: true,
        page: 1,
        limit: 10,
    },
    filterByLocationAmenities: {
        nearToMetro: true,
        nearToMarket: true,
        elevator: true,
        page: 1,
        limit: 10,
    },
    complexFilter: {
        search: 'furnished apartment',
        city: 'Cairo',
        type: 'house',
        roomType: 'single',
        minPrice: '6000',
        maxPrice: '12000',
        totalRooms: '2',
        separatedBathroom: true,
        airConditioning: true,
        includeFurniture: true,
        internet: true,
        nearToMetro: true,
        parking: true,
        goodForForeigners: true,
        isVerified: true,
        isAvailable: true,
        minRating: 3.5,
        page: 1,
        limit: 5,
        sortBy: 'rating',
        sortOrder: 'desc',
    },
    premiumFilter: {
        minPrice: '10000',
        includeFurniture: true,
        airConditioning: true,
        parking: true,
        elevator: true,
        separatedBathroom: true,
        includeWaterHeater: true,
        isVerified: true,
        minRating: 4.0,
        minTotalRatings: 5,
        page: 1,
        limit: 10,
        sortBy: 'price',
        sortOrder: 'desc',
    },
    budgetFilter: {
        maxPrice: '4000',
        allowSmoking: false,
        goodForForeigners: true,
        isAvailable: true,
        page: 1,
        limit: 10,
        sortBy: 'price',
        sortOrder: 'asc',
    },
    studentHousingFilter: {
        maxPrice: '5000',
        goodForForeigners: true,
        internet: true,
        nearToMetro: true,
        nearToMarket: true,
        trialPeriod: true,
        isAvailable: true,
        page: 1,
        limit: 15,
        sortBy: 'price',
        sortOrder: 'asc',
    },
    familyFilter: {
        type: 'house',
        minPrice: '6000',
        totalRooms: '3',
        separatedBathroom: true,
        includeFurniture: true,
        airConditioning: true,
        parking: true,
        elevator: true,
        nearToMarket: true,
        goodForForeigners: true,
        isVerified: true,
        page: 1,
        limit: 10,
        sortBy: 'rating',
        sortOrder: 'desc',
    },
    sharedFilter: {
        type: 'room',
        roomType: 'single',
        maxPrice: '3000',
        internet: true,
        nearToMetro: true,
        goodForForeigners: true,
        isAvailable: true,
        page: 1,
        limit: 15,
        sortBy: 'price',
        sortOrder: 'asc',
    },
    executiveFilter: {
        minPrice: '8000',
        type: 'house',
        includeFurniture: true,
        airConditioning: true,
        parking: true,
        internet: true,
        elevator: true,
        separatedBathroom: true,
        goodForForeigners: true,
        isVerified: true,
        minRating: 4.0,
        page: 1,
        limit: 10,
        sortBy: 'rating',
        sortOrder: 'desc',
    },
    roomCompletionFilter: {
        roomsToComplete: '1',
        isAvailable: true,
        goodForForeigners: true,
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc',
    },
    highRatedFilter: {
        minRating: 4.5,
        minTotalRatings: 10,
        isVerified: true,
        isAvailable: true,
        page: 1,
        limit: 10,
        sortBy: 'rating',
        sortOrder: 'desc',
    },
};
exports.validationExamples = {
    validPropertyTypes: Object.values(client_1.PropertyType),
    validRoomTypes: Object.values(client_1.RoomType),
    validRentTimes: Object.values(client_1.RentTime),
    validPaymentTimes: Object.values(client_1.PaymentTime),
    validGenderRequirements: ['male', 'female', 'mixed'],
    validAmenities: [
        'separatedBathroom',
        'priceIncludeWaterAndElectricity',
        'allowSmoking',
        'includeFurniture',
        'airConditioning',
        'includeWaterHeater',
        'parking',
        'internet',
        'nearToMetro',
        'nearToMarket',
        'elevator',
        'trialPeriod',
        'goodForForeigners',
    ],
    invalidExamples: [
        { title: 'Short' },
        { title: 'A'.repeat(201) },
        { description: 'Too short desc' },
        { description: 'A'.repeat(2001) },
        { categoryId: 'invalid-uuid' },
        { termsAndConditions: 'A'.repeat(1001) },
    ],
};
exports.apiEndpointExamples = {
    create: {
        method: 'POST',
        url: '/api/properties',
        headers: { Authorization: 'Bearer user-jwt-token' },
        body: exports.createPropertyExample,
    },
    getAll: {
        method: 'GET',
        url: '/api/properties?page=1&limit=10&sortBy=createdAt&sortOrder=desc',
    },
    search: {
        method: 'GET',
        url: '/api/properties?search=apartment&city=Cairo&minPrice=5000&maxPrice=10000',
    },
    getByCategory: {
        method: 'GET',
        url: '/api/properties?categoryId=cat-uuid-123&page=1&limit=20',
    },
    getUserProperties: {
        method: 'GET',
        url: '/api/properties/my-properties?page=1&limit=10',
        headers: { Authorization: 'Bearer user-jwt-token' },
    },
    getFavorites: {
        method: 'GET',
        url: '/api/properties/favorites?page=1&limit=10',
        headers: { Authorization: 'Bearer user-jwt-token' },
    },
    getById: {
        method: 'GET',
        url: '/api/properties/property-uuid-123',
    },
    getBySlug: {
        method: 'GET',
        url: '/api/properties/slug/spacious-apartment-new-cairo',
    },
    update: {
        method: 'PATCH',
        url: '/api/properties/property-uuid-123',
        headers: { Authorization: 'Bearer user-jwt-token' },
        body: exports.updatePropertyExample,
    },
    toggleFavorite: {
        method: 'PATCH',
        url: '/api/properties/property-uuid-123/toggle-favorite',
        headers: { Authorization: 'Bearer user-jwt-token' },
    },
    delete: {
        method: 'DELETE',
        url: '/api/properties/property-uuid-123',
        headers: { Authorization: 'Bearer user-jwt-token' },
    },
};
exports.responseExamples = {
    propertyResponse: {
        id: 'property-uuid-123',
        title: 'Spacious 2-Bedroom Apartment in New Cairo with Modern Amenities',
        slug: 'spacious-apartment-new-cairo',
        images: [
            'https://example.com/images/property1-main.jpg',
            'https://example.com/images/property1-living.jpg',
        ],
        city: 'Cairo',
        country: 'Egypt',
        address: '123 New Cairo, Fifth Settlement, Cairo Governorate',
        description: 'Beautiful modern apartment...',
        latitude: '30.0444',
        longitude: '31.2357',
        type: 'house',
        roomType: 'single',
        genderRequired: 'mixed',
        totalRooms: '2',
        availableRooms: '1',
        price: '8000',
        size: '120',
        floor: '3',
        bathrooms: '2',
        separatedBathroom: true,
        residentsCount: '1',
        availablePersons: '2',
        rentTime: 'MONTHLY',
        paymentTime: 'MONTHLY',
        priceIncludeWaterAndElectricity: true,
        allowSmoking: false,
        includeFurniture: true,
        airConditioning: true,
        parking: true,
        internet: true,
        nearToMetro: true,
        goodForForeigners: true,
        rating: 4.5,
        totalRatings: 12,
        isVerified: true,
        isAvailable: true,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-15T10:30:00.000Z',
        user: {
            id: 'user-uuid-456',
            name: 'Ahmed Hassan',
            email: '<EMAIL>',
            phone: '+************',
        },
        category: {
            id: 'cat-uuid-123',
            name: 'Apartments',
            icon: 'apartment-icon.svg',
        },
        offers: [
            {
                id: 'offer-uuid-789',
                message: 'Interested in this property',
                price: '7500',
                status: 'PENDING',
                user: {
                    name: 'Sara Mohamed',
                    email: '<EMAIL>',
                },
            },
        ],
        bookings: [
            {
                id: 'booking-uuid-101',
                startDate: '2024-02-01T00:00:00.000Z',
                endDate: '2024-02-29T00:00:00.000Z',
                status: 'CONFIRMED',
            },
        ],
    },
    paginatedResponse: {
        data: [],
        total: 127,
        page: 1,
        limit: 10,
        totalPages: 13,
        hasNext: true,
        hasPrev: false,
    },
    statsResponse: {
        totalProperties: 127,
        availableProperties: 89,
        bookedProperties: 38,
        propertiesByType: {
            APARTMENT: 45,
            HOUSE: 23,
            STUDIO: 31,
            SHARED_ROOM: 28,
        },
        propertiesByCity: {
            Cairo: 67,
            Alexandria: 23,
            Giza: 18,
            Others: 19,
        },
        avgPrice: 6750,
        avgSize: 95,
        propertiesWithAmenities: {
            airConditioning: 89,
            parking: 67,
            internet: 102,
            includeFurniture: 78,
        },
        newPropertiesThisMonth: 12,
    },
};
//# sourceMappingURL=properties.examples.js.map
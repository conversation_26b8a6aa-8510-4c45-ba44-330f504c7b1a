"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.responseExamples = exports.apiEndpointExamples = exports.updateRatingExample = exports.createBookingRatingExample = exports.simpleRatingExample = exports.createRatingExample = void 0;
exports.createRatingExample = {
    score: 5,
    propertyId: 'property-uuid-123',
};
exports.simpleRatingExample = {
    score: 4,
    propertyId: 'property-uuid-456',
};
exports.createBookingRatingExample = {
    score: 5,
    bookingId: 'booking-uuid-123',
};
exports.updateRatingExample = {
    score: 4,
};
exports.apiEndpointExamples = {
    create: {
        method: 'POST',
        url: '/api/ratings',
        headers: { Authorization: 'Bearer user-jwt-token' },
        body: exports.createRatingExample,
    },
    getPropertyRatings: {
        method: 'GET',
        url: '/api/ratings/property/property-uuid-123',
    },
};
exports.responseExamples = {
    ratingResponse: {
        id: 'rating-uuid-123',
        score: 5,
        createdAt: '2024-01-01T00:00:00.000Z',
        user: {
            id: 'user-uuid-456',
            name: '<PERSON>',
            email: '<EMAIL>',
        },
        property: {
            id: 'property-uuid-123',
            title: 'Beautiful Apartment in Cairo',
            avgRating: 4.7,
            totalRatings: 23,
        },
    },
};
//# sourceMappingURL=ratings.examples.js.map
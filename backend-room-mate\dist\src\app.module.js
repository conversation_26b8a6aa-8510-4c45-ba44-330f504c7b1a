"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const auth_module_1 = require("./auth/auth.module");
const users_module_1 = require("./users/users.module");
const categories_module_1 = require("./categories/categories.module");
const notifications_module_1 = require("./notifications/notifications.module");
const ratings_module_1 = require("./ratings/ratings.module");
const upload_module_1 = require("./upload/upload.module");
const properties_module_1 = require("./properties/properties.module");
const persons_module_1 = require("./persons/persons.module");
const offers_module_1 = require("./offers/offers.module");
const bookings_module_1 = require("./bookings/bookings.module");
const verification_module_1 = require("./verification/verification.module");
const comments_module_1 = require("./comments/comments.module");
const path_1 = require("path");
const serve_static_1 = require("@nestjs/serve-static");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: '.env',
            }),
            serve_static_1.ServeStaticModule.forRoot({
                rootPath: (0, path_1.join)(process.cwd(), 'uploads'),
                serveRoot: '/nestjs/uploads',
                serveStaticOptions: {
                    setHeaders: (res, path) => {
                        if (path.endsWith('.mp4')) {
                            res.setHeader('Content-Type', 'video/mp4');
                        }
                    },
                },
            }),
            auth_module_1.AuthModule,
            users_module_1.UsersModule,
            categories_module_1.CategoriesModule,
            notifications_module_1.NotificationsModule,
            ratings_module_1.RatingsModule,
            upload_module_1.UploadModule,
            properties_module_1.PropertiesModule,
            persons_module_1.PersonsModule,
            offers_module_1.OffersModule,
            bookings_module_1.BookingsModule,
            verification_module_1.VerificationModule,
            comments_module_1.CommentsModule,
        ],
        controllers: [app_controller_1.AppController],
        providers: [app_service_1.AppService],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map
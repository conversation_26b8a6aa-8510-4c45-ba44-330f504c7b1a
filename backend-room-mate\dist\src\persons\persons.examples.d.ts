import { CreatePersonDto } from './dto/create-person.dto';
import { UpdatePersonDto } from './dto/update-person.dto';
import { QueryPersonsDto } from './dto/query-persons.dto';
export declare const createPersonExample: CreatePersonDto;
export declare const minimalPersonExample: CreatePersonDto;
export declare const luxurySeekingPersonExample: CreatePersonDto;
export declare const updatePersonExample: UpdatePersonDto;
export declare const queryPersonsExamples: {
    getAllPersons: QueryPersonsDto;
    searchPersons: QueryPersonsDto;
    filterByTitle: QueryPersonsDto;
    filterBySlug: QueryPersonsDto;
    filterByLocation: QueryPersonsDto;
    filterByAddress: QueryPersonsDto;
    filterByCoordinates: QueryPersonsDto;
    filterByType: QueryPersonsDto;
    filterByGender: QueryPersonsDto;
    filterByRoomSpecs: QueryPersonsDto;
    filterBySpaceSpecs: QueryPersonsDto;
    filterByBudget: QueryPersonsDto;
    filterByTerms: QueryPersonsDto;
    filterByCategory: QueryPersonsDto;
    filterByOwner: QueryPersonsDto;
    filterByRating: QueryPersonsDto;
    filterByDateRange: QueryPersonsDto;
    filterByRecentUpdates: QueryPersonsDto;
    filterByStatus: QueryPersonsDto;
    filterByAccommodationPrefs: QueryPersonsDto;
    filterByAmenities: QueryPersonsDto;
    filterByLocationAmenities: QueryPersonsDto;
    complexFilter: QueryPersonsDto;
    premiumFilter: QueryPersonsDto;
    budgetFilter: QueryPersonsDto;
    studentFilter: QueryPersonsDto;
    professionalFilter: QueryPersonsDto;
};
export declare const apiEndpointExamples: {
    create: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
        body: CreatePersonDto;
    };
    getAll: {
        method: string;
        url: string;
    };
    search: {
        method: string;
        url: string;
    };
    getUserPersons: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    getById: {
        method: string;
        url: string;
    };
    getBySlug: {
        method: string;
        url: string;
    };
    update: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
        body: UpdatePersonDto;
    };
    delete: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    toggleFavorite: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
    getFavorites: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
    };
};
export declare const responseExamples: {
    personResponse: {
        id: string;
        title: string;
        slug: string;
        images: string[];
        city: string;
        country: string;
        address: string;
        description: string;
        type: string;
        roomType: string;
        genderRequired: string;
        totalRooms: string;
        availableRooms: string;
        price: string;
        size: string;
        floor: string;
        bathrooms: string;
        separatedBathroom: boolean;
        residentsCount: string;
        availablePersons: string;
        rentTime: string;
        paymentTime: string;
        priceIncludeWaterAndElectricity: boolean;
        allowSmoking: boolean;
        includeFurniture: boolean;
        airConditioning: boolean;
        parking: boolean;
        internet: boolean;
        nearToMetro: boolean;
        goodForForeigners: boolean;
        rating: number;
        totalRatings: number;
        isVerified: boolean;
        isAvailable: boolean;
        createdAt: string;
        updatedAt: string;
        owner: {
            id: string;
            name: string;
            email: string;
            phone: string;
        };
        category: {
            id: string;
            name: string;
            icon: string;
        };
        _count: {
            favorites: number;
        };
    };
    paginatedResponse: {
        success: boolean;
        data: {
            persons: never[];
            pagination: {
                page: number;
                limit: number;
                total: number;
                totalPages: number;
                hasNext: boolean;
                hasPrev: boolean;
            };
        };
    };
    toggleFavoriteResponse: {
        success: boolean;
        data: {
            isFavorited: boolean;
        };
        message: string;
    };
};

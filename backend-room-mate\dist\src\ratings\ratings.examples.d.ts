import { CreateRatingDto } from './dto/create-rating.dto';
import { UpdateRatingDto } from './dto/update-rating.dto';
import { CreateBookingRatingDto } from './dto/create-booking-rating.dto';
export declare const createRatingExample: CreateRatingDto;
export declare const simpleRatingExample: CreateRatingDto;
export declare const createBookingRatingExample: CreateBookingRatingDto;
export declare const updateRatingExample: UpdateRatingDto;
export declare const apiEndpointExamples: {
    create: {
        method: string;
        url: string;
        headers: {
            Authorization: string;
        };
        body: CreateRatingDto;
    };
    getPropertyRatings: {
        method: string;
        url: string;
    };
};
export declare const responseExamples: {
    ratingResponse: {
        id: string;
        score: number;
        createdAt: string;
        user: {
            id: string;
            name: string;
            email: string;
        };
        property: {
            id: string;
            title: string;
            avgRating: number;
            totalRatings: number;
        };
    };
};

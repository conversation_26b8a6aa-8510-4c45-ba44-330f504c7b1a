import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { QueryUsersDto } from './dto/query-users.dto';
import { UserPayload } from './interfaces/user.interface';
export declare class UsersController {
    private readonly usersService;
    constructor(usersService: UsersService);
    create(createUserDto: CreateUserDto): Promise<import("./interfaces/user.interface").UserResponse>;
    findAll(query: QueryUsersDto, user: UserPayload): Promise<import("./interfaces/user.interface").UsersQueryResult>;
    findMe(user: UserPayload): Promise<import("./interfaces/user.interface").UserResponse>;
    findOne(id: string): Promise<import("./interfaces/user.interface").UserResponse>;
    update(id: string, updateUserDto: UpdateUserDto, user: UserPayload): Promise<import("./interfaces/user.interface").UserResponse>;
    changePassword(id: string, changePasswordDto: ChangePasswordDto, user: UserPayload): Promise<{
        message: string;
    }>;
    remove(id: string, user: UserPayload): Promise<{
        message: string;
    }>;
    toggleVerification(id: string, user: UserPayload): Promise<import("./interfaces/user.interface").UserResponse>;
    toggleAdminStatus(id: string, user: UserPayload): Promise<import("./interfaces/user.interface").UserResponse>;
}

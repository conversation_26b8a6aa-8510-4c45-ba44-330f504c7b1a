
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.11.1
 * Query Engine version: f40f79ec31188888a2e33acda0ecc8fd10a853a9
 */
Prisma.prismaVersion = {
  client: "6.11.1",
  engine: "f40f79ec31188888a2e33acda0ecc8fd10a853a9"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  password: 'password',
  phone: 'phone',
  smoker: 'smoker',
  age: 'age',
  gender: 'gender',
  nationality: 'nationality',
  country: 'country',
  occupation: 'occupation',
  isAdmin: 'isAdmin',
  isVerified: 'isVerified',
  isVIP: 'isVIP',
  freeListingCount: 'freeListingCount',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.NotificationScalarFieldEnum = {
  id: 'id',
  title: 'title',
  message: 'message',
  read: 'read',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId',
  adminId: 'adminId'
};

exports.Prisma.CategoryScalarFieldEnum = {
  id: 'id',
  name: 'name',
  icon: 'icon',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PropertyScalarFieldEnum = {
  id: 'id',
  title: 'title',
  slug: 'slug',
  images: 'images',
  city: 'city',
  country: 'country',
  neighborhood: 'neighborhood',
  address: 'address',
  description: 'description',
  latitude: 'latitude',
  longitude: 'longitude',
  type: 'type',
  roomType: 'roomType',
  genderRequired: 'genderRequired',
  totalRooms: 'totalRooms',
  availableRooms: 'availableRooms',
  roomsToComplete: 'roomsToComplete',
  price: 'price',
  size: 'size',
  floor: 'floor',
  bathrooms: 'bathrooms',
  separatedBathroom: 'separatedBathroom',
  residentsCount: 'residentsCount',
  availablePersons: 'availablePersons',
  rentTime: 'rentTime',
  paymentTime: 'paymentTime',
  priceIncludeWaterAndElectricity: 'priceIncludeWaterAndElectricity',
  allowSmoking: 'allowSmoking',
  includeFurniture: 'includeFurniture',
  airConditioning: 'airConditioning',
  includeWaterHeater: 'includeWaterHeater',
  parking: 'parking',
  internet: 'internet',
  nearToMetro: 'nearToMetro',
  nearToMarket: 'nearToMarket',
  elevator: 'elevator',
  trialPeriod: 'trialPeriod',
  goodForForeigners: 'goodForForeigners',
  rating: 'rating',
  totalRatings: 'totalRatings',
  termsAndConditions: 'termsAndConditions',
  isVerified: 'isVerified',
  isAvailable: 'isAvailable',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  categoryId: 'categoryId',
  ownerId: 'ownerId',
  userId: 'userId'
};

exports.Prisma.PersonScalarFieldEnum = {
  id: 'id',
  title: 'title',
  slug: 'slug',
  city: 'city',
  images: 'images',
  country: 'country',
  neighborhood: 'neighborhood',
  address: 'address',
  description: 'description',
  latitude: 'latitude',
  longitude: 'longitude',
  type: 'type',
  roomType: 'roomType',
  genderRequired: 'genderRequired',
  totalRooms: 'totalRooms',
  availableRooms: 'availableRooms',
  price: 'price',
  size: 'size',
  floor: 'floor',
  bathrooms: 'bathrooms',
  separatedBathroom: 'separatedBathroom',
  residentsCount: 'residentsCount',
  availablePersons: 'availablePersons',
  rentTime: 'rentTime',
  paymentTime: 'paymentTime',
  priceIncludeWaterAndElectricity: 'priceIncludeWaterAndElectricity',
  allowSmoking: 'allowSmoking',
  includeFurniture: 'includeFurniture',
  airConditioning: 'airConditioning',
  includeWaterHeater: 'includeWaterHeater',
  parking: 'parking',
  internet: 'internet',
  nearToMetro: 'nearToMetro',
  nearToMarket: 'nearToMarket',
  elevator: 'elevator',
  trialPeriod: 'trialPeriod',
  goodForForeigners: 'goodForForeigners',
  rating: 'rating',
  totalRatings: 'totalRatings',
  termsAndConditions: 'termsAndConditions',
  isVerified: 'isVerified',
  isAvailable: 'isAvailable',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  categoryId: 'categoryId',
  ownerId: 'ownerId'
};

exports.Prisma.CommentScalarFieldEnum = {
  id: 'id',
  content: 'content',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId',
  propertyId: 'propertyId',
  personId: 'personId'
};

exports.Prisma.OfferScalarFieldEnum = {
  id: 'id',
  message: 'message',
  price: 'price',
  phone: 'phone',
  duration: 'duration',
  deposit: 'deposit',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  propertyId: 'propertyId',
  personId: 'personId',
  userId: 'userId'
};

exports.Prisma.BookingScalarFieldEnum = {
  id: 'id',
  startDate: 'startDate',
  endDate: 'endDate',
  totalAmount: 'totalAmount',
  status: 'status',
  depositPaid: 'depositPaid',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId',
  propertyId: 'propertyId',
  offerId: 'offerId'
};

exports.Prisma.RatingScalarFieldEnum = {
  id: 'id',
  score: 'score',
  comment: 'comment',
  createdAt: 'createdAt',
  userId: 'userId',
  propertyId: 'propertyId',
  personId: 'personId'
};

exports.Prisma.VerificationScalarFieldEnum = {
  id: 'id',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId',
  propertyId: 'propertyId',
  personId: 'personId'
};

exports.Prisma.VerificationRequestScalarFieldEnum = {
  id: 'id',
  images: 'images',
  userId: 'userId',
  propertyId: 'propertyId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};
exports.PropertyType = exports.$Enums.PropertyType = {
  house: 'house',
  room: 'room'
};

exports.RoomType = exports.$Enums.RoomType = {
  mixed: 'mixed',
  single: 'single'
};

exports.RentTime = exports.$Enums.RentTime = {
  daily: 'daily',
  weekly: 'weekly',
  monthly: 'monthly',
  quarterly: 'quarterly',
  semiannual: 'semiannual',
  annually: 'annually'
};

exports.PaymentTime = exports.$Enums.PaymentTime = {
  daily: 'daily',
  weekly: 'weekly',
  monthly: 'monthly',
  quarterly: 'quarterly',
  semiannual: 'semiannual',
  annually: 'annually'
};

exports.OfferStatus = exports.$Enums.OfferStatus = {
  pending: 'pending',
  accepted: 'accepted',
  rejected: 'rejected',
  cancelled: 'cancelled'
};

exports.BookingStatus = exports.$Enums.BookingStatus = {
  confirmed: 'confirmed',
  cancelled: 'cancelled',
  completed: 'completed'
};

exports.VerificationStatus = exports.$Enums.VerificationStatus = {
  pending: 'pending',
  approved: 'approved',
  rejected: 'rejected'
};

exports.Prisma.ModelName = {
  User: 'User',
  Notification: 'Notification',
  Category: 'Category',
  Property: 'Property',
  Person: 'Person',
  Comment: 'Comment',
  Offer: 'Offer',
  Booking: 'Booking',
  Rating: 'Rating',
  Verification: 'Verification',
  VerificationRequest: 'VerificationRequest'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
